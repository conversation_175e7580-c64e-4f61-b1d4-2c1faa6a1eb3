.backup-plan-detail {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  background: var(--background-color);
  min-height: 100vh;
}

.detail-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 32px;
  padding: 20px;
  background: var(--card-background);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.task-info h2 {
  margin: 0 0 8px 0;
  color: var(--text-primary);
  font-size: 24px;
  font-weight: 600;
}

.task-info {
  flex: 1;
  margin: 0 1rem;
}

.task-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.chain-status {
  display: flex;
  align-items: center;
  margin-top: 0.5rem;
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
  position: relative;
}

.chain-status.valid {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.chain-status.invalid {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.status-icon {
  margin-right: 0.5rem;
}

.issues-tooltip {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 0.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  z-index: 1000;
  margin-top: 0.25rem;
}

.issues-tooltip ul {
  margin: 0;
  padding-left: 1rem;
  list-style-type: disc;
}

.issues-tooltip li {
  margin-bottom: 0.25rem;
  font-size: 0.8rem;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.history-timeline {
  background: var(--card-background);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.history-timeline h3 {
  margin: 0;
  padding: 20px 24px;
  background: var(--primary-color);
  color: white;
  font-size: 18px;
  font-weight: 600;
  border-bottom: 1px solid var(--border-color);
}

.no-history {
  padding: 40px;
  text-align: center;
  color: var(--text-secondary);
}

.timeline {
  padding: 24px;
  position: relative;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 44px;
  top: 24px;
  bottom: 24px;
  width: 2px;
  background: var(--border-color);
}

.timeline-item {
  position: relative;
  margin-bottom: 32px;
  padding-left: 80px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-marker {
  position: absolute;
  left: 24px;
  top: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid var(--card-background);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.timeline-number {
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.timeline-content {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 20px;
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
}

.timeline-content:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.timeline-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.timeline-header h4 {
  margin: 0;
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  color: white;
  font-size: 12px;
  font-weight: 500;
}

.timeline-actions {
  display: flex;
  gap: 8px;
}

.timeline-details {
  display: grid;
  gap: 8px;
}

.detail-row {
  display: grid;
  grid-template-columns: 120px 1fr;
  gap: 16px;
  padding: 8px 0;
  border-bottom: 1px solid var(--border-light);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row span:first-child {
  color: var(--text-secondary);
  font-weight: 500;
}

.detail-row span:last-child {
  color: var(--text-primary);
}

.detail-row.error span:last-child {
  color: var(--error-color);
  font-family: monospace;
  font-size: 13px;
}

.detail-row.dependency {
  background-color: #e3f2fd;
  border-left: 3px solid #2196f3;
  padding-left: 8px;
  margin-left: -8px;
}

.detail-row.dependency span:first-child {
  color: #1976d2;
  font-weight: 500;
}

.detail-row.dependency span:last-child {
  color: #1976d2;
}

.detail-row.validation {
  padding: 0.5rem;
  border-radius: 4px;
  margin: 0.25rem 0;
}

.detail-row.validation.valid {
  background-color: #d4edda;
  border-left: 3px solid #28a745;
}

.detail-row.validation.invalid {
  background-color: #f8d7da;
  border-left: 3px solid #dc3545;
}

.detail-row.validation span:first-child {
  font-weight: 500;
}

.detail-row.validation.valid span {
  color: #155724;
}

.detail-row.validation.invalid span {
  color: #721c24;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px;
  color: var(--text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  text-align: center;
  padding: 40px;
  background: var(--card-background);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.error-message h3 {
  color: var(--error-color);
  margin-bottom: 16px;
}

.error-message p {
  color: var(--text-secondary);
  margin-bottom: 24px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.btn:active {
  transform: translateY(0);
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-hover);
}

.btn-secondary {
  background: var(--secondary-color);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--secondary-hover);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.btn-outline:hover {
  background: var(--primary-color);
  color: white;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .backup-plan-detail {
    padding: 16px;
  }
  
  .detail-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .timeline-item {
    padding-left: 60px;
  }
  
  .timeline::before {
    left: 34px;
  }
  
  .timeline-marker {
    left: 14px;
    width: 32px;
    height: 32px;
  }
  
  .timeline-number {
    font-size: 12px;
  }
  
  .timeline-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .detail-row {
    grid-template-columns: 1fr;
    gap: 4px;
  }
}
