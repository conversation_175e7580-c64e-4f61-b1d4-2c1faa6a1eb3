<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MySQL增量备份管理器 - 图标预览</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #2d3748;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        
        .subtitle {
            text-align: center;
            color: #718096;
            margin-bottom: 40px;
            font-size: 1.1em;
        }
        
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .icon-card {
            background: #f7fafc;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .icon-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
            border-color: #4299e1;
        }
        
        .icon-display {
            width: 128px;
            height: 128px;
            margin: 0 auto 20px;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 20px rgba(0,0,0,0.15);
        }
        
        .icon-display svg {
            width: 100%;
            height: 100%;
        }
        
        .icon-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 10px;
        }
        
        .icon-description {
            color: #718096;
            font-size: 0.95em;
            line-height: 1.5;
            margin-bottom: 15px;
        }
        
        .icon-features {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .icon-features ul {
            margin: 0;
            padding-left: 20px;
            text-align: left;
        }
        
        .icon-features li {
            color: #4a5568;
            font-size: 0.9em;
            margin-bottom: 5px;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .download-btn:hover {
            background: linear-gradient(135deg, #3182ce, #2c5282);
            transform: translateY(-2px);
        }
        
        .instructions {
            background: #edf2f7;
            border-radius: 15px;
            padding: 30px;
            margin-top: 40px;
        }
        
        .instructions h3 {
            color: #2d3748;
            margin-bottom: 20px;
        }
        
        .instructions ol {
            color: #4a5568;
            line-height: 1.8;
        }
        
        .instructions li {
            margin-bottom: 10px;
        }
        
        .color-palette {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 10px 0;
        }
        
        .color-dot {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>MySQL增量备份管理器</h1>
        <p class="subtitle">应用图标设计方案预览</p>
        
        <div class="icon-grid">
            <!-- 图标1：数据库+箭头 -->
            <div class="icon-card">
                <div class="icon-display">
                    <svg width="128" height="128" viewBox="0 0 256 256">
                        <rect width="256" height="256" rx="48" ry="48" fill="url(#bg1)"/>
                        <defs>
                            <linearGradient id="bg1" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#1E3A8A"/>
                                <stop offset="100%" style="stop-color:#3B82F6"/>
                            </linearGradient>
                        </defs>
                        <!-- 简化显示 -->
                        <ellipse cx="128" cy="100" rx="50" ry="12" fill="#E0F2FE"/>
                        <rect x="78" y="100" width="100" height="60" fill="#2563EB"/>
                        <ellipse cx="128" cy="160" rx="50" ry="12" fill="#BAE6FD"/>
                        <path d="M 180 80 L 200 60 L 220 80 L 210 80 L 210 120 L 190 120 L 190 80 Z" fill="#34D399"/>
                        <text x="128" y="200" text-anchor="middle" font-size="14" fill="white">MySQL</text>
                    </svg>
                </div>
                <div class="icon-title">经典数据库风格</div>
                <div class="icon-description">传统的圆柱形数据库图标配合绿色增量箭头，专业且易识别</div>
                <div class="color-palette">
                    <div class="color-dot" style="background: #1E3A8A;"></div>
                    <div class="color-dot" style="background: #3B82F6;"></div>
                    <div class="color-dot" style="background: #34D399;"></div>
                </div>
                <div class="icon-features">
                    <ul>
                        <li>蓝色渐变背景，专业感强</li>
                        <li>立体数据库图标</li>
                        <li>绿色箭头表示增量</li>
                        <li>适合企业级应用</li>
                    </ul>
                </div>
                <a href="icon-style1-database.svg" download class="download-btn">下载 SVG</a>
            </div>
            
            <!-- 图标2：盾牌保护 -->
            <div class="icon-card">
                <div class="icon-display">
                    <svg width="128" height="128" viewBox="0 0 256 256">
                        <rect width="256" height="256" rx="48" ry="48" fill="#10B981"/>
                        <path d="M 128 48 C 88 48 88 88 88 128 C 88 168 108 208 128 228 C 148 208 168 168 168 128 C 168 88 168 48 128 48 Z" fill="#34D399"/>
                        <ellipse cx="128" cy="110" rx="25" ry="6" fill="#047857"/>
                        <rect x="103" y="110" width="50" height="20" fill="#059669"/>
                        <ellipse cx="128" cy="130" rx="25" ry="6" fill="#047857"/>
                        <path d="M 113 150 L 123 160 L 143 140" stroke="#047857" stroke-width="4" fill="none"/>
                        <text x="128" y="190" text-anchor="middle" font-size="12" fill="white">BACKUP</text>
                    </svg>
                </div>
                <div class="icon-title">安全盾牌风格</div>
                <div class="icon-description">盾牌形状象征数据保护，内含数据库和安全标识</div>
                <div class="color-palette">
                    <div class="color-dot" style="background: #065F46;"></div>
                    <div class="color-dot" style="background: #10B981;"></div>
                    <div class="color-dot" style="background: #34D399;"></div>
                </div>
                <div class="icon-features">
                    <ul>
                        <li>绿色系，安全可靠</li>
                        <li>盾牌造型突出保护概念</li>
                        <li>对勾符号表示安全</li>
                        <li>适合强调安全性</li>
                    </ul>
                </div>
                <a href="icon-style2-shield.svg" download class="download-btn">下载 SVG</a>
            </div>
            
            <!-- 图标3：齿轮自动化 -->
            <div class="icon-card">
                <div class="icon-display">
                    <svg width="128" height="128" viewBox="0 0 256 256">
                        <rect width="256" height="256" rx="48" ry="48" fill="#F97316"/>
                        <circle cx="128" cy="128" r="40" fill="#FB923C"/>
                        <ellipse cx="128" cy="113" rx="20" ry="5" fill="#DBEAFE"/>
                        <rect x="108" y="113" width="40" height="15" fill="#3B82F6"/>
                        <ellipse cx="128" cy="128" rx="20" ry="5" fill="#93C5FD"/>
                        <line x1="128" y1="128" x2="128" y2="103" stroke="white" stroke-width="3"/>
                        <line x1="128" y1="128" x2="143" y2="128" stroke="white" stroke-width="3"/>
                        <text x="128" y="190" text-anchor="middle" font-size="12" fill="white">AUTO</text>
                    </svg>
                </div>
                <div class="icon-title">自动化齿轮风格</div>
                <div class="icon-description">齿轮象征自动化，融合数据库和时钟元素</div>
                <div class="color-palette">
                    <div class="color-dot" style="background: #C2410C;"></div>
                    <div class="color-dot" style="background: #F97316;"></div>
                    <div class="color-dot" style="background: #FB923C;"></div>
                </div>
                <div class="icon-features">
                    <ul>
                        <li>橙色系，活力动感</li>
                        <li>齿轮表示自动化</li>
                        <li>时钟指针表示定时</li>
                        <li>适合强调自动化功能</li>
                    </ul>
                </div>
                <a href="icon-style3-gear.svg" download class="download-btn">下载 SVG</a>
            </div>
            
            <!-- 图标4：文件夹版本 -->
            <div class="icon-card">
                <div class="icon-display">
                    <svg width="128" height="128" viewBox="0 0 256 256">
                        <rect width="256" height="256" rx="48" ry="48" fill="#A855F7"/>
                        <path d="M 78 108 L 98 93 L 178 93 L 178 153 L 78 153 Z" fill="#E9D5FF"/>
                        <path d="M 86 116 L 106 101 L 186 101 L 186 161 L 86 161 Z" fill="#DDD6FE"/>
                        <path d="M 94 124 L 114 109 L 194 109 L 194 169 L 94 169 Z" fill="#C4B5FD"/>
                        <ellipse cx="128" cy="135" rx="15" ry="3" fill="#581C87"/>
                        <rect x="113" y="135" width="30" height="8" fill="#6D28D9"/>
                        <text x="128" y="190" text-anchor="middle" font-size="12" fill="white">VERSIONS</text>
                    </svg>
                </div>
                <div class="icon-title">版本控制风格</div>
                <div class="icon-description">多层文件夹叠加，体现版本控制和增量存储概念</div>
                <div class="color-palette">
                    <div class="color-dot" style="background: #581C87;"></div>
                    <div class="color-dot" style="background: #A855F7;"></div>
                    <div class="color-dot" style="background: #E9D5FF;"></div>
                </div>
                <div class="icon-features">
                    <ul>
                        <li>紫色系，现代科技感</li>
                        <li>层叠效果表示版本</li>
                        <li>文件夹表示存储</li>
                        <li>适合强调版本管理</li>
                    </ul>
                </div>
                <a href="icon-style4-folder.svg" download class="download-btn">下载 SVG</a>
            </div>
            
            <!-- 图标5：极简线条 -->
            <div class="icon-card">
                <div class="icon-display">
                    <svg width="128" height="128" viewBox="0 0 256 256">
                        <rect width="256" height="256" rx="48" ry="48" fill="#1E293B"/>
                        <ellipse cx="128" cy="98" rx="40" ry="10" fill="none" stroke="#60A5FA" stroke-width="6"/>
                        <line x1="88" y1="98" x2="88" y2="158" stroke="#60A5FA" stroke-width="6"/>
                        <line x1="168" y1="98" x2="168" y2="158" stroke="#60A5FA" stroke-width="6"/>
                        <ellipse cx="128" cy="158" rx="40" ry="10" fill="none" stroke="#60A5FA" stroke-width="6"/>
                        <path d="M 188 108 L 188 88 M 178 98 L 188 88 L 198 98" stroke="#10B981" stroke-width="4" fill="none"/>
                        <text x="128" y="190" text-anchor="middle" font-size="14" fill="white">MySQL</text>
                    </svg>
                </div>
                <div class="icon-title">极简线条风格</div>
                <div class="icon-description">简化的数据库轮廓，极简线条设计，现代感强</div>
                <div class="color-palette">
                    <div class="color-dot" style="background: #0F172A;"></div>
                    <div class="color-dot" style="background: #60A5FA;"></div>
                    <div class="color-dot" style="background: #10B981;"></div>
                </div>
                <div class="icon-features">
                    <ul>
                        <li>深色背景，简洁专业</li>
                        <li>线条轮廓，现代设计</li>
                        <li>蓝绿配色，清新简约</li>
                        <li>适合现代化界面</li>
                    </ul>
                </div>
                <a href="icon-style5-minimal.svg" download class="download-btn">下载 SVG</a>
            </div>
        </div>
        
        <div class="instructions">
            <h3>🚀 使用说明</h3>
            <ol>
                <li><strong>选择图标</strong>：点击上方任意图标的"下载 SVG"按钮下载源文件</li>
                <li><strong>转换格式</strong>：使用在线工具将SVG转换为ICO格式：
                    <ul>
                        <li><a href="https://convertio.co/svg-ico/" target="_blank">Convertio</a></li>
                        <li><a href="https://cloudconvert.com/svg-to-ico" target="_blank">CloudConvert</a></li>
                        <li><a href="https://www.icoconverter.com/" target="_blank">ICO Converter</a></li>
                    </ul>
                </li>
                <li><strong>设置尺寸</strong>：确保ICO文件包含多种尺寸 (256, 128, 64, 48, 32, 16)</li>
                <li><strong>重命名文件</strong>：将转换后的文件重命名为 <code>mysql-backup-icon.ico</code></li>
                <li><strong>放置文件</strong>：将ICO文件放入项目的 <code>public/</code> 目录</li>
                <li><strong>开始打包</strong>：运行 <code>npm run dist:win</code> 进行Windows打包</li>
            </ol>
            
            <h3>💡 推荐选择</h3>
            <p><strong>图标1 (经典数据库风格)</strong> - 最适合MySQL备份应用，专业且易识别</p>
            <p><strong>图标5 (极简线条风格)</strong> - 现代简约，适合追求简洁设计的用户</p>
        </div>
    </div>
</body>
</html>
