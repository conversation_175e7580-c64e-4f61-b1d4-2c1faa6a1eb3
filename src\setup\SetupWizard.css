/* 安装向导样式 */

.setup-wizard {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

.wizard-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 24px;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.wizard-header h1 {
  text-align: center;
  margin: 0 0 32px 0;
  color: #2c3e50;
  font-size: 28px;
  font-weight: 600;
}

.step-indicator {
  display: flex;
  justify-content: center;
  gap: 24px;
  max-width: 800px;
  margin: 0 auto;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
  opacity: 0.6;
}

.step-item.active {
  opacity: 1;
  background: rgba(102, 126, 234, 0.1);
  border: 2px solid #667eea;
}

.step-item.completed {
  opacity: 1;
  background: rgba(46, 204, 113, 0.1);
  border: 2px solid #2ecc71;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #666;
  transition: all 0.3s ease;
}

.step-item.active .step-number {
  background: #667eea;
  color: white;
}

.step-item.completed .step-number {
  background: #2ecc71;
  color: white;
}

.step-item.completed .step-number::before {
  content: "✓";
  font-size: 14px;
}

.step-info {
  display: flex;
  flex-direction: column;
}

.step-title {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.step-description {
  font-size: 12px;
  color: #7f8c8d;
  margin-top: 2px;
}

.wizard-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 24px;
}

.setup-step {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
}

/* 欢迎步骤样式 */
.welcome-step {
  max-width: 800px;
  text-align: center;
}

.welcome-content h1 {
  color: #2c3e50;
  font-size: 32px;
  margin-bottom: 16px;
  font-weight: 700;
}

.welcome-description {
  color: #7f8c8d;
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 40px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.feature-item {
  text-align: center;
  padding: 20px;
  border-radius: 12px;
  background: #f8f9fa;
  transition: transform 0.2s ease;
}

.feature-item:hover {
  transform: translateY(-4px);
}

.feature-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.feature-item h3 {
  color: #2c3e50;
  font-size: 16px;
  margin-bottom: 8px;
  font-weight: 600;
}

.feature-item p {
  color: #7f8c8d;
  font-size: 14px;
  line-height: 1.4;
}

.setup-requirements {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 32px;
  text-align: left;
}

.setup-requirements h3 {
  color: #2c3e50;
  margin-bottom: 16px;
  font-size: 18px;
}

.setup-requirements ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.setup-requirements li {
  color: #7f8c8d;
  padding: 4px 0;
  position: relative;
  padding-left: 20px;
}

.setup-requirements li::before {
  content: "✓";
  color: #2ecc71;
  font-weight: bold;
  position: absolute;
  left: 0;
}

/* 表单样式 */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 14px;
}

.form-group input,
.form-group select {
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: white;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input.error {
  border-color: #e74c3c;
}

.form-group input[type="checkbox"] {
  width: auto;
  margin-right: 8px;
}

.error-text {
  color: #e74c3c;
  font-size: 12px;
  margin-top: 4px;
}

/* 密码要求样式 */
.password-requirements {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.password-requirements h4 {
  color: #2c3e50;
  margin-bottom: 12px;
  font-size: 14px;
}

.password-requirements ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.password-requirements li {
  color: #7f8c8d;
  font-size: 12px;
  padding: 2px 0;
  position: relative;
  padding-left: 20px;
  transition: color 0.2s ease;
}

.password-requirements li::before {
  content: "○";
  position: absolute;
  left: 0;
  color: #bdc3c7;
}

.password-requirements li.valid {
  color: #2ecc71;
}

.password-requirements li.valid::before {
  content: "✓";
  color: #2ecc71;
}

/* 连接测试样式 */
.connection-test {
  margin: 24px 0;
  text-align: center;
}

.test-result {
  margin-top: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
}

.test-result.success {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
  border: 1px solid #2ecc71;
}

.test-result.error {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  border: 1px solid #e74c3c;
}

/* 配置摘要样式 */
.config-summary {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 32px;
}

.config-summary h3 {
  color: #2c3e50;
  margin-bottom: 16px;
  font-size: 18px;
}

.summary-grid {
  display: grid;
  gap: 12px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e0e0e0;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-item strong {
  color: #2c3e50;
  font-size: 14px;
}

.summary-item span {
  color: #7f8c8d;
  font-size: 14px;
}

/* 安装进度样式 */
.installation-progress {
  text-align: center;
  margin: 32px 0;
}

.installation-progress h3 {
  color: #2c3e50;
  margin-bottom: 24px;
  font-size: 20px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 16px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
  border-radius: 4px;
}

/* 按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-primary.btn-large {
  padding: 16px 32px;
  font-size: 16px;
}

.btn-secondary {
  background: white;
  color: #667eea;
  border: 2px solid #667eea;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

.step-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
}

.wizard-footer {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 16px 24px;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wizard-header {
    padding: 16px;
  }
  
  .wizard-header h1 {
    font-size: 24px;
  }
  
  .step-indicator {
    flex-direction: column;
    gap: 12px;
  }
  
  .step-item {
    justify-content: center;
  }
  
  .wizard-content {
    padding: 20px 16px;
  }
  
  .setup-step {
    padding: 24px;
    max-height: none;
  }
  
  .welcome-content h1 {
    font-size: 24px;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .summary-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
