/**
 * 企业级API管理器
 * 提供RESTful API接口，支持第三方系统集成
 */

import express, { Express, Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import jwt from 'jsonwebtoken';
import { logger } from '../utils/Logger';
import { config } from '../config/AppConfig';
import { roleManager, Permission } from '../security/RoleManager';
import { systemMonitor } from '../monitoring/SystemMonitor';
import { backupPolicyManager } from '../backup/BackupPolicyManager';
import { performanceOptimizer } from '../performance/PerformanceOptimizer';

export interface ApiConfig {
  port: number;
  host: string;
  enableCors: boolean;
  enableRateLimit: boolean;
  rateLimit: {
    windowMs: number;
    max: number;
  };
  jwt: {
    secret: string;
    expiresIn: string;
  };
  apiKey: {
    enabled: boolean;
    keys: string[];
  };
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
  requestId: string;
}

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    username: string;
    role: string;
    permissions: string[];
  };
  apiKey?: string;
}

/**
 * API管理器
 */
export class ApiManager {
  private static instance: ApiManager;
  private app: Express;
  private server: any;
  private config: ApiConfig;

  private constructor() {
    this.config = this.getDefaultConfig();
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
  }

  public static getInstance(): ApiManager {
    if (!ApiManager.instance) {
      ApiManager.instance = new ApiManager();
    }
    return ApiManager.instance;
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): ApiConfig {
    return {
      port: config.get<number>('api.port') || 3001,
      host: config.get<string>('api.host') || '0.0.0.0',
      enableCors: config.get<boolean>('api.cors.enabled') || true,
      enableRateLimit: config.get<boolean>('api.rateLimit.enabled') || true,
      rateLimit: {
        windowMs: config.get<number>('api.rateLimit.windowMs') || 15 * 60 * 1000, // 15分钟
        max: config.get<number>('api.rateLimit.max') || 100 // 每个IP最多100个请求
      },
      jwt: {
        secret: config.get<string>('api.jwt.secret') || 'backup-system-secret',
        expiresIn: config.get<string>('api.jwt.expiresIn') || '24h'
      },
      apiKey: {
        enabled: config.get<boolean>('api.apiKey.enabled') || true,
        keys: config.get<string[]>('api.apiKey.keys') || []
      }
    };
  }

  /**
   * 设置中间件
   */
  private setupMiddleware(): void {
    // 安全头
    this.app.use(helmet());

    // CORS
    if (this.config.enableCors) {
      this.app.use(cors({
        origin: config.get<string[]>('api.cors.origins') || ['http://localhost:3000'],
        credentials: true
      }));
    }

    // 请求解析
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));

    // 速率限制
    if (this.config.enableRateLimit) {
      const limiter = rateLimit({
        windowMs: this.config.rateLimit.windowMs,
        max: this.config.rateLimit.max,
        message: {
          success: false,
          error: '请求过于频繁，请稍后再试',
          timestamp: new Date().toISOString(),
          requestId: ''
        }
      });
      this.app.use('/api/', limiter);
    }

    // 请求日志
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      const requestId = this.generateRequestId();
      (req as any).requestId = requestId;
      
      logger.info('API请求', {
        requestId,
        method: req.method,
        url: req.url,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      
      next();
    });

    // 认证中间件
    this.app.use('/api/', this.authMiddleware.bind(this));
  }

  /**
   * 认证中间件
   */
  private authMiddleware(req: AuthenticatedRequest, res: Response, next: NextFunction): void {
    // 跳过公开端点
    const publicEndpoints = ['/api/health', '/api/auth/login'];
    if (publicEndpoints.includes(req.path)) {
      return next();
    }

    const token = req.header('Authorization')?.replace('Bearer ', '');
    const apiKey = req.header('X-API-Key');

    try {
      if (token) {
        // JWT认证
        const decoded = jwt.verify(token, this.config.jwt.secret) as any;
        req.user = decoded;
        next();
      } else if (apiKey && this.config.apiKey.enabled) {
        // API Key认证
        if (this.config.apiKey.keys.includes(apiKey)) {
          req.apiKey = apiKey;
          req.user = {
            id: 'api-user',
            username: 'api-user',
            role: 'api',
            permissions: ['backup:read', 'backup:write', 'system:read']
          };
          next();
        } else {
          this.sendError(res, 401, '无效的API密钥', (req as any).requestId);
        }
      } else {
        this.sendError(res, 401, '未提供认证信息', (req as any).requestId);
      }
    } catch (error) {
      this.sendError(res, 401, '认证失败', (req as any).requestId);
    }
  }

  /**
   * 权限检查中间件
   */
  private requirePermission(permission: Permission) {
    return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
      if (!req.user) {
        return this.sendError(res, 401, '未认证', (req as any).requestId);
      }

      if (req.user.role === 'api' || req.user.permissions.includes(permission)) {
        next();
      } else {
        this.sendError(res, 403, '权限不足', (req as any).requestId);
      }
    };
  }

  /**
   * 设置路由
   */
  private setupRoutes(): void {
    // 健康检查
    this.app.get('/api/health', (req: Request, res: Response) => {
      this.sendSuccess(res, {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      }, (req as any).requestId);
    });

    // 认证相关
    this.app.post('/api/auth/login', this.handleLogin.bind(this));
    this.app.post('/api/auth/refresh', this.handleRefreshToken.bind(this));

    // 系统信息
    this.app.get('/api/system/info', 
      this.requirePermission('system:read'), 
      this.handleSystemInfo.bind(this)
    );
    
    this.app.get('/api/system/metrics', 
      this.requirePermission('system:read'), 
      this.handleSystemMetrics.bind(this)
    );

    // 备份管理
    this.app.get('/api/backups', 
      this.requirePermission('backup:read'), 
      this.handleGetBackups.bind(this)
    );
    
    this.app.post('/api/backups', 
      this.requirePermission('backup:write'), 
      this.handleCreateBackup.bind(this)
    );
    
    this.app.get('/api/backups/:id', 
      this.requirePermission('backup:read'), 
      this.handleGetBackup.bind(this)
    );
    
    this.app.delete('/api/backups/:id', 
      this.requirePermission('backup:delete'), 
      this.handleDeleteBackup.bind(this)
    );

    // 备份策略
    this.app.get('/api/policies', 
      this.requirePermission('backup:read'), 
      this.handleGetPolicies.bind(this)
    );
    
    this.app.post('/api/policies', 
      this.requirePermission('backup:write'), 
      this.handleCreatePolicy.bind(this)
    );

    // 性能监控
    this.app.get('/api/performance/metrics', 
      this.requirePermission('system:read'), 
      this.handlePerformanceMetrics.bind(this)
    );
    
    this.app.get('/api/performance/jobs', 
      this.requirePermission('backup:read'), 
      this.handlePerformanceJobs.bind(this)
    );

    // 错误处理
    this.app.use(this.errorHandler.bind(this));
  }

  /**
   * 处理登录
   */
  private async handleLogin(req: Request, res: Response): Promise<void> {
    try {
      const { username, password } = req.body;
      
      // 这里应该验证用户凭据
      // 简化实现，实际应该从数据库验证
      if (username && password) {
        const token = jwt.sign(
          { 
            id: '1', 
            username, 
            role: 'admin',
            permissions: ['backup:read', 'backup:write', 'backup:delete', 'system:read', 'system:write']
          },
          this.config.jwt.secret,
          { expiresIn: this.config.jwt.expiresIn }
        );

        this.sendSuccess(res, { token }, (req as any).requestId);
      } else {
        this.sendError(res, 400, '用户名和密码不能为空', (req as any).requestId);
      }
    } catch (error) {
      this.sendError(res, 500, '登录失败', (req as any).requestId);
    }
  }

  /**
   * 处理刷新令牌
   */
  private async handleRefreshToken(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        return this.sendError(res, 401, '未认证', (req as any).requestId);
      }

      const token = jwt.sign(
        req.user,
        this.config.jwt.secret,
        { expiresIn: this.config.jwt.expiresIn }
      );

      this.sendSuccess(res, { token }, (req as any).requestId);
    } catch (error) {
      this.sendError(res, 500, '刷新令牌失败', (req as any).requestId);
    }
  }

  /**
   * 处理系统信息
   */
  private async handleSystemInfo(req: Request, res: Response): Promise<void> {
    try {
      const info = {
        version: '1.0.0',
        uptime: process.uptime(),
        platform: process.platform,
        nodeVersion: process.version,
        memory: process.memoryUsage(),
        cpu: process.cpuUsage()
      };

      this.sendSuccess(res, info, (req as any).requestId);
    } catch (error) {
      this.sendError(res, 500, '获取系统信息失败', (req as any).requestId);
    }
  }

  /**
   * 处理系统指标
   */
  private async handleSystemMetrics(req: Request, res: Response): Promise<void> {
    try {
      // 这里应该从系统监控器获取实际指标
      const metrics = {
        timestamp: new Date(),
        cpu: { usage: Math.random() * 100 },
        memory: { usage: Math.random() * 100 },
        disk: { usage: Math.random() * 100 },
        network: { received: 1024000, sent: 512000 }
      };

      this.sendSuccess(res, metrics, (req as any).requestId);
    } catch (error) {
      this.sendError(res, 500, '获取系统指标失败', (req as any).requestId);
    }
  }

  /**
   * 处理获取备份列表
   */
  private async handleGetBackups(req: Request, res: Response): Promise<void> {
    try {
      // 模拟备份数据
      const backups = [
        {
          id: '1',
          name: 'database_backup_20240115',
          type: 'full',
          status: 'completed',
          createdAt: new Date(),
          size: 1024000
        }
      ];

      this.sendSuccess(res, backups, (req as any).requestId);
    } catch (error) {
      this.sendError(res, 500, '获取备份列表失败', (req as any).requestId);
    }
  }

  /**
   * 处理创建备份
   */
  private async handleCreateBackup(req: Request, res: Response): Promise<void> {
    try {
      const { name, type, databaseId } = req.body;
      
      // 这里应该创建实际的备份任务
      const backup = {
        id: Date.now().toString(),
        name,
        type,
        databaseId,
        status: 'queued',
        createdAt: new Date()
      };

      this.sendSuccess(res, backup, (req as any).requestId);
    } catch (error) {
      this.sendError(res, 500, '创建备份失败', (req as any).requestId);
    }
  }

  /**
   * 发送成功响应
   */
  private sendSuccess<T>(res: Response, data: T, requestId: string, message?: string): void {
    const response: ApiResponse<T> = {
      success: true,
      data,
      message,
      timestamp: new Date().toISOString(),
      requestId
    };
    res.json(response);
  }

  /**
   * 发送错误响应
   */
  private sendError(res: Response, status: number, error: string, requestId: string): void {
    const response: ApiResponse = {
      success: false,
      error,
      timestamp: new Date().toISOString(),
      requestId
    };
    res.status(status).json(response);
  }

  /**
   * 错误处理器
   */
  private errorHandler(error: Error, req: Request, res: Response, next: NextFunction): void {
    logger.error('API错误', error, { 
      requestId: (req as any).requestId,
      url: req.url,
      method: req.method 
    });

    this.sendError(res, 500, '内部服务器错误', (req as any).requestId);
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 启动API服务器
   */
  public start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.server = this.app.listen(this.config.port, this.config.host, () => {
          logger.info('API服务器已启动', { 
            host: this.config.host, 
            port: this.config.port 
          });
          resolve();
        });
      } catch (error) {
        logger.error('启动API服务器失败', error as Error);
        reject(error);
      }
    });
  }

  /**
   * 停止API服务器
   */
  public stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          logger.info('API服务器已停止');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  /**
   * 获取Express应用实例
   */
  public getApp(): Express {
    return this.app;
  }
}

export const apiManager = ApiManager.getInstance();
