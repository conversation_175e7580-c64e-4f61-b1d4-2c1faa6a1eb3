import React, { useState, useEffect } from 'react';
import './BackupPlanDetail.css';
import { BackupChainVisualization } from './BackupChainVisualization';

interface BackupHistoryItem {
  id: number;
  backup_type: 'full' | 'incremental';
  status: 'running' | 'completed' | 'failed';
  start_time: string;
  end_time?: string;
  file_size: number;
  file_path: string;
  error_message?: string;
  binlog_file?: string;
  binlog_position?: number;
  duration?: number;
  compression_ratio?: number;
  records_count?: number;
}

interface BackupTask {
  id: number;
  name: string;
  database_name: string;
  server_name?: string;
  schedule_type: string;
  schedule_time?: string;
  status: string;
  created_at: string;
  last_backup_time?: string;
  total_backups?: number;
  success_rate?: number;
}

interface BackupStats {
  totalBackups: number;
  successfulBackups: number;
  failedBackups: number;
  totalSize: number;
  avgDuration: number;
  lastFullBackup?: string;
  nextScheduledBackup?: string;
}

interface BackupPlanDetailProps {
  taskId: number;
  userId: number;
  onBack: () => void;
}

const BackupPlanDetail: React.FC<BackupPlanDetailProps> = ({ taskId, userId, onBack }) => {
  const [task, setTask] = useState<BackupTask | null>(null);
  const [history, setHistory] = useState<BackupHistoryItem[]>([]);
  const [stats, setStats] = useState<BackupStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [generatingSQL, setGeneratingSQL] = useState<number | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState('7d');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [chainValidation, setChainValidation] = useState<{isValid: boolean, issues: string[]} | null>(null);
  const [validatingChain, setValidatingChain] = useState(false);
  const [fileValidation, setFileValidation] = useState<{[key: number]: any}>({});
  const [validatingFiles, setValidatingFiles] = useState<{[key: number]: boolean}>({});
  const [showVisualization, setShowVisualization] = useState(true);
  const [restoringBackup, setRestoringBackup] = useState<number | null>(null);

  useEffect(() => {
    loadTaskHistory();
    validateBackupChain();

    // 自动刷新
    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(() => {
        loadTaskHistory(true);
        validateBackupChain();
      }, 30000); // 每30秒刷新一次
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [taskId, autoRefresh]);

  const loadTaskHistory = async (silent = false) => {
    try {
      if (!silent) setLoading(true);
      setError(null);

      if (!window.electronAPI) {
        throw new Error('electronAPI 不可用');
      }

      const result = await window.electronAPI.getBackupTaskHistory(taskId, userId);

      if (result.success) {
        setTask(result.task || null);
        setHistory(result.history || []);
      } else {
        setError(result.message || '获取备份历史失败');
      }
    } catch (err) {
      console.error('获取备份历史失败:', err);
      setError('获取备份历史失败');
    } finally {
      if (!silent) setLoading(false);
    }
  };

  const validateBackupChain = async () => {
    try {
      setValidatingChain(true);

      if (!window.electronAPI || !window.electronAPI.validateBackupChain) {
        console.warn('备份链验证功能不可用');
        return;
      }

      const result = await window.electronAPI.validateBackupChain(taskId, userId);

      if (result.success) {
        setChainValidation({
          isValid: result.isValid || false,
          issues: result.issues || []
        });
      } else {
        console.error('验证备份链失败:', result.message);
      }
    } catch (err) {
      console.error('验证备份链失败:', err);
    } finally {
      setValidatingChain(false);
    }
  };

  const validateBackupFile = async (historyId: number) => {
    try {
      setValidatingFiles(prev => ({ ...prev, [historyId]: true }));

      if (!window.electronAPI || !window.electronAPI.validateBackupFile) {
        console.warn('备份文件验证功能不可用');
        return;
      }

      const result = await window.electronAPI.validateBackupFile(historyId, userId);

      if (result.success) {
        setFileValidation(prev => ({
          ...prev,
          [historyId]: result
        }));
      } else {
        console.error('验证备份文件失败:', result.message);
      }
    } catch (err) {
      console.error('验证备份文件失败:', err);
    } finally {
      setValidatingFiles(prev => ({ ...prev, [historyId]: false }));
    }
  };

  const validateAllFiles = async () => {
    try {
      setValidatingFiles({});
      setFileValidation({});

      if (!window.electronAPI || !window.electronAPI.validateAllBackupFiles) {
        console.warn('批量验证功能不可用');
        return;
      }

      const result = await window.electronAPI.validateAllBackupFiles(taskId, userId);

      if (result.success && result.results) {
        const validationMap: {[key: number]: any} = {};
        result.results.forEach(item => {
          validationMap[item.historyId] = item.validation;
        });
        setFileValidation(validationMap);
      } else {
        console.error('批量验证备份文件失败:', result.message);
      }
    } catch (err) {
      console.error('批量验证备份文件失败:', err);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (startTime: string, endTime?: string): string => {
    if (!endTime) return '-';
    const start = new Date(startTime);
    const end = new Date(endTime);
    const duration = end.getTime() - start.getTime();
    const minutes = Math.floor(duration / 60000);
    const seconds = Math.floor((duration % 60000) / 1000);
    return `${minutes}分${seconds}秒`;
  };

  const handleDownloadCompleteSQL = async (historyId: number) => {
    try {
      setGeneratingSQL(historyId);

      if (!window.electronAPI) {
        throw new Error('electronAPI 不可用');
      }

      const result = await window.electronAPI.generateCompleteBackupSQL(
        taskId,
        historyId,
        userId
      );

      if (result.success) {
        // 显示生成成功的信息
        alert(`完整SQL文件生成成功！\n文件名: ${result.fileName}\n文件大小: ${formatFileSize(result.fileSize || 0)}\n包含增量备份: ${result.incrementalBackups?.length || 0} 个`);

        // 可以在这里添加下载逻辑
        if (result.filePath && window.electronAPI.showItemInFolder) {
          await window.electronAPI.showItemInFolder(result.filePath);
        }
      } else {
        alert(`生成失败: ${result.message}`);
      }
    } catch (err) {
      console.error('生成完整SQL文件失败:', err);
      alert('生成完整SQL文件失败');
    } finally {
      setGeneratingSQL(null);
    }
  };

  const handleRestoreBackup = async (historyId: number) => {
    try {
      const confirmed = window.confirm(
        '确定要恢复到此时间点吗？\n\n注意：这将会覆盖目标数据库的现有数据！\n建议先进行数据库备份。'
      );

      if (!confirmed) return;

      setRestoringBackup(historyId);

      if (!window.electronAPI || !window.electronAPI.restoreBackupToPointInTime) {
        throw new Error('恢复功能不可用');
      }

      // 询问恢复选项
      const targetDatabase = prompt('请输入目标数据库名称（留空使用原数据库）:');
      const dryRun = window.confirm('是否先进行干运行测试？（推荐）');

      const options = {
        targetDatabase: targetDatabase || undefined,
        dryRun
      };

      const result = await window.electronAPI.restoreBackupToPointInTime(
        taskId,
        historyId,
        userId,
        options
      );

      if (result.success) {
        if (dryRun) {
          const proceedWithRestore = window.confirm(
            `干运行验证成功！\n\n${result.message}\n\n是否继续执行实际恢复？`
          );

          if (proceedWithRestore) {
            // 执行实际恢复
            const actualResult = await window.electronAPI.restoreBackupToPointInTime(
              taskId,
              historyId,
              userId,
              { ...options, dryRun: false }
            );

            if (actualResult.success) {
              alert(`恢复成功！\n\n${actualResult.message}\n恢复时间点: ${actualResult.restoredToTime}`);
            } else {
              alert(`恢复失败: ${actualResult.message}`);
            }
          }
        } else {
          alert(`恢复成功！\n\n${result.message}\n恢复时间点: ${result.restoredToTime}`);
        }
      } else {
        alert(`恢复失败: ${result.message}`);
      }
    } catch (err) {
      console.error('恢复备份失败:', err);
      alert('恢复备份失败');
    } finally {
      setRestoringBackup(null);
    }
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'completed': return '#28a745';
      case 'running': return '#007bff';
      case 'failed': return '#dc3545';
      default: return '#6c757d';
    }
  };

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'completed': return '已完成';
      case 'running': return '运行中';
      case 'failed': return '失败';
      default: return '未知';
    }
  };

  if (loading) {
    return (
      <div className="backup-plan-detail">
        <div className="loading">
          <div className="loading-spinner"></div>
          <p>加载中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="backup-plan-detail">
        <div className="error-message">
          <h3>错误</h3>
          <p>{error}</p>
          <button onClick={onBack} className="btn btn-primary">
            返回备份管理
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="backup-plan-detail">
      <div className="detail-header">
        <button onClick={onBack} className="btn btn-secondary">
          ← 返回
        </button>
        <div className="task-info">
          <h2>{task?.name || '备份计划详情'}</h2>
          <p>数据库: {task?.database_name}</p>
          {chainValidation && (
            <div className={`chain-status ${chainValidation.isValid ? 'valid' : 'invalid'}`}>
              <span className="status-icon">
                {chainValidation.isValid ? '✅' : '⚠️'}
              </span>
              <span className="status-text">
                备份链{chainValidation.isValid ? '完整' : '存在问题'}
              </span>
              {!chainValidation.isValid && chainValidation.issues.length > 0 && (
                <div className="issues-tooltip">
                  <ul>
                    {chainValidation.issues.map((issue, index) => (
                      <li key={index}>{issue}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>
        <div className="header-actions">
          <button
            onClick={validateBackupChain}
            className="btn btn-secondary"
            disabled={validatingChain}
          >
            {validatingChain ? '验证中...' : '验证备份链'}
          </button>
          <button
            onClick={validateAllFiles}
            className="btn btn-secondary"
            disabled={Object.values(validatingFiles).some(v => v)}
          >
            验证所有文件
          </button>
          <button onClick={() => loadTaskHistory()} className="btn btn-primary">
            刷新
          </button>
        </div>
      </div>

      {/* 备份链可视化 */}
      {showVisualization && history.length > 0 && (
        <BackupChainVisualization
          taskId={taskId}
          userId={userId}
          history={history}
          onNodeClick={(node) => {
            console.log('选中节点:', node);
          }}
          onRestoreClick={handleRestoreBackup}
        />
      )}

      <div className="history-timeline">
        <div className="timeline-header">
          <h3>备份历史时间线</h3>
          <div className="view-controls">
            <button
              className={`btn btn-sm ${showVisualization ? 'btn-primary' : 'btn-outline'}`}
              onClick={() => setShowVisualization(!showVisualization)}
            >
              {showVisualization ? '隐藏' : '显示'}依赖关系图
            </button>
          </div>
        </div>
        {history.length === 0 ? (
          <div className="no-history">
            <p>暂无备份历史记录</p>
          </div>
        ) : (
          <div className="timeline">
            {history.map((item, index) => (
              <div key={item.id} className="timeline-item">
                <div className="timeline-marker" style={{ backgroundColor: getStatusColor(item.status) }}>
                  <span className="timeline-number">{index + 1}</span>
                </div>
                <div className="timeline-content">
                  <div className="timeline-header">
                    <h4>
                      {item.backup_type === 'full' ? '完整备份' : '增量备份'}
                      <span className="status-badge" style={{ backgroundColor: getStatusColor(item.status) }}>
                        {getStatusText(item.status)}
                      </span>
                    </h4>
                    <div className="timeline-actions">
                      {item.status === 'completed' && (
                        <>
                          <button
                            onClick={() => handleDownloadCompleteSQL(item.id)}
                            disabled={generatingSQL === item.id}
                            className="btn btn-sm btn-outline"
                          >
                            {generatingSQL === item.id ? '生成中...' : '下载完整SQL'}
                          </button>
                          <button
                            onClick={() => validateBackupFile(item.id)}
                            disabled={validatingFiles[item.id]}
                            className="btn btn-sm btn-outline"
                          >
                            {validatingFiles[item.id] ? '验证中...' : '验证文件'}
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                  <div className="timeline-details">
                    <div className="detail-row">
                      <span>开始时间:</span>
                      <span>{new Date(item.start_time).toLocaleString()}</span>
                    </div>
                    {item.end_time && (
                      <div className="detail-row">
                        <span>结束时间:</span>
                        <span>{new Date(item.end_time).toLocaleString()}</span>
                      </div>
                    )}
                    <div className="detail-row">
                      <span>持续时间:</span>
                      <span>{formatDuration(item.start_time, item.end_time)}</span>
                    </div>
                    <div className="detail-row">
                      <span>文件大小:</span>
                      <span>{formatFileSize(item.file_size)}</span>
                    </div>
                    {item.binlog_file && item.binlog_position && (
                      <div className="detail-row">
                        <span>Binlog位置:</span>
                        <span>{item.binlog_file}:{item.binlog_position}</span>
                      </div>
                    )}
                    {item.backup_type === 'incremental' && index > 0 && (
                      <div className="detail-row dependency">
                        <span>依赖备份:</span>
                        <span>#{index} ({history[index - 1].backup_type === 'full' ? '完整备份' : '增量备份'})</span>
                      </div>
                    )}
                    {fileValidation[item.id] && (
                      <div className={`detail-row validation ${fileValidation[item.id].isValid ? 'valid' : 'invalid'}`}>
                        <span>文件验证:</span>
                        <span>
                          {fileValidation[item.id].isValid ? '✅ 通过' : '❌ 失败'}
                          {fileValidation[item.id].message && ` - ${fileValidation[item.id].message}`}
                        </span>
                      </div>
                    )}
                    {item.error_message && (
                      <div className="detail-row error">
                        <span>错误信息:</span>
                        <span>{item.error_message}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default BackupPlanDetail;
