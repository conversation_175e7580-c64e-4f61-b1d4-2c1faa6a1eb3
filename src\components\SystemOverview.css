/* 企业级系统概览样式 */

.system-overview {
  padding: var(--spacing-3xl);
  background: var(--bg-primary);
  min-height: 100vh;
  animation: fadeIn var(--transition-slow);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-4xl);
  padding: var(--spacing-4xl);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 50%, var(--primary-active) 100%);
  border-radius: var(--radius-2xl);
  color: var(--text-inverse);
  box-shadow: var(--shadow-2xl);
  position: relative;
  overflow: hidden;
}

.overview-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hexagons" width="20" height="20" patternUnits="userSpaceOnUse"><polygon points="10,2 18,7 18,13 10,18 2,13 2,7" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23hexagons)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

.system-title {
  flex: 1;
}

.system-info h1 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-inverse);
  text-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  letter-spacing: -1px;
  line-height: var(--line-height-tight);
  position: relative;
  z-index: 1;
}

.system-details {
  display: flex;
  gap: var(--spacing-lg);
  align-items: center;
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
  position: relative;
  z-index: 1;
}

.system-details .version {
  background: rgba(255, 255, 255, 0.2);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.system-details .edition {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.9), rgba(255, 193, 7, 0.9));
  color: #333;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.system-details .status {
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  backdrop-filter: blur(20px);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.system-details .status.healthy {
  background: rgba(16, 124, 16, 0.2);
  border: 1px solid rgba(16, 124, 16, 0.5);
  color: rgba(255, 255, 255, 0.95);
}

.system-details .status.healthy::before {
  content: '●';
  color: var(--success-color);
  animation: statusPulse 2s infinite;
}

.system-details .status.warning {
  background: rgba(255, 140, 0, 0.2);
  border: 1px solid rgba(255, 140, 0, 0.5);
  color: rgba(255, 255, 255, 0.95);
}

.system-details .status.warning::before {
  content: '●';
  color: var(--warning-color);
  animation: statusPulse 2s infinite;
}

.system-details .status.critical {
  background: rgba(209, 52, 56, 0.2);
  border: 1px solid rgba(209, 52, 56, 0.5);
  color: rgba(255, 255, 255, 0.95);
}

.system-details .status.critical::before {
  content: '●';
  color: var(--danger-color);
  animation: statusPulse 2s infinite;
}

@keyframes statusPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

.system-stats {
  display: flex;
  gap: 24px;
  font-size: 14px;
  opacity: 0.9;
  flex-wrap: wrap;
}

.system-stats span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.system-stats span::before {
  content: "•";
  color: rgba(255, 255, 255, 0.6);
}

.refresh-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  white-space: nowrap;
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.system-overview-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: var(--text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.metric-card {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 0;
  box-shadow: var(--card-shadow);
  transition: all 0.2s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--card-shadow-hover);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid var(--border-color);
}

.card-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
}

.card-icon {
  font-size: 20px;
}

.card-content {
  padding: 20px 24px 24px;
}

/* 性能指标样式 */
.performance-metrics {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-label {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.metric-value {
  display: flex;
  align-items: center;
  gap: 12px;
}

.metric-number {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  min-width: 60px;
}

.metric-number.high {
  color: #dc3545;
}

.metric-bar {
  flex: 1;
  height: 8px;
  background: var(--border-color);
  border-radius: 4px;
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--success-color), var(--warning-color), var(--error-color));
  border-radius: 4px;
  transition: width 0.3s ease;
}

.metric-detail {
  font-size: 12px;
  color: var(--text-secondary);
  margin-top: 4px;
}

/* 备份统计样式 */
.backup-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: 16px;
  background: var(--background-color);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-number.active {
  color: var(--primary-color);
}

.stat-number.queued {
  color: var(--warning-color);
}

.stat-number.success {
  color: var(--success-color);
}

.stat-number.error {
  color: var(--error-color);
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 500;
}

.backup-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.detail-item span:first-child {
  color: var(--text-secondary);
}

.detail-item span:last-child {
  color: var(--text-primary);
  font-weight: 500;
}

/* 告警样式 */
.alerts-card .card-header {
  position: relative;
}

.alert-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--error-color);
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  min-width: 20px;
  text-align: center;
}

.no-alerts {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-secondary);
}

.no-alerts-icon {
  font-size: 48px;
  display: block;
  margin-bottom: 16px;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.alert-item {
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid;
  background: var(--background-color);
}

.alert-item.critical {
  border-left-color: #dc3545;
  background: rgba(220, 53, 69, 0.05);
}

.alert-item.high {
  border-left-color: #fd7e14;
  background: rgba(253, 126, 20, 0.05);
}

.alert-item.medium {
  border-left-color: #ffc107;
  background: rgba(255, 193, 7, 0.05);
}

.alert-item.low {
  border-left-color: #28a745;
  background: rgba(40, 167, 69, 0.05);
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.alert-severity {
  font-size: 10px;
  font-weight: 700;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  letter-spacing: 0.5px;
}

.alert-time {
  font-size: 12px;
  color: var(--text-secondary);
}

.alert-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.alert-message {
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .overview-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .stat-row {
    flex-direction: column;
    gap: 12px;
  }
  
  .metric-value {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .metric-bar {
    width: 100%;
  }
}
