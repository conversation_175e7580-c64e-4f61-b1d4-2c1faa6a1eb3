import React, { useState, useEffect } from 'react';
import './SystemOverview.css';

interface SystemInfo {
  productName: string;
  version: string;
  edition: string;
  licenseTo: string;
  installDate: Date;
  lastUpdateCheck: Date;
  connectedDatabases: number;
  activeBackupJobs: number;
  systemStatus: 'healthy' | 'warning' | 'critical';
}

interface SystemMetrics {
  timestamp: Date;
  cpu: { usage: number; cores: number; loadAverage: number[] };
  memory: { total: number; used: number; free: number; usage: number };
  uptime: number;
}

interface BackupMetrics {
  timestamp: Date;
  activeBackups: number;
  queuedBackups: number;
  completedBackups24h: number;
  failedBackups24h: number;
  averageBackupDuration: number;
  totalBackupSize: number;
  oldestBackup: Date;
  newestBackup: Date;
}

const SystemOverviewFixed: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [systemInfo, setSystemInfo] = useState<SystemInfo>({
    productName: 'MySQL 增量备份系统',
    version: '1.0.0',
    edition: '企业版',
    licenseTo: '企业用户',
    installDate: new Date('2024-01-15'),
    lastUpdateCheck: new Date(),
    connectedDatabases: 0,
    activeBackupJobs: 0,
    systemStatus: 'healthy'
  });
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null);
  const [backupMetrics, setBackupMetrics] = useState<BackupMetrics | null>(null);

  useEffect(() => {
    loadSystemData();
    const interval = setInterval(loadSystemData, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadSystemData = async () => {
    try {
      setError(null);
      
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      if (!user.id) {
        setError('用户未登录');
        setLoading(false);
        return;
      }

      // 检查 electronAPI 是否可用
      if (!window.electronAPI) {
        setError('electronAPI 不可用');
        setLoading(false);
        return;
      }

      // 获取真实的系统指标
      try {
        const metricsResult = await window.electronAPI.getSystemMetrics();
        if (metricsResult.success && metricsResult.data) {
          setSystemMetrics(metricsResult.data);
        }
      } catch (err) {
        console.error('获取系统指标失败:', err);
      }

      // 获取真实的备份统计
      try {
        const statsResult = await window.electronAPI.getBackupStats(user.id);
        if (statsResult.success && statsResult.data) {
          const stats = statsResult.data;
          setBackupMetrics({
            timestamp: stats.timestamp,
            activeBackups: stats.activeBackupJobs,
            queuedBackups: 0,
            completedBackups24h: stats.completedBackups24h,
            failedBackups24h: stats.failedBackups24h,
            averageBackupDuration: stats.averageBackupDuration,
            totalBackupSize: stats.totalBackupSize,
            oldestBackup: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            newestBackup: stats.latestBackup ? new Date(stats.latestBackup.time) : new Date()
          });

          // 更新系统信息中的真实数据
          setSystemInfo(prev => ({
            ...prev,
            connectedDatabases: stats.connectedDatabases,
            activeBackupJobs: stats.activeBackupJobs,
            systemStatus: stats.failedBackups24h > 0 ? 'warning' : 'healthy'
          }));
        }
      } catch (err) {
        console.error('获取备份统计失败:', err);
      }

      setLoading(false);
    } catch (error) {
      console.error('加载系统数据失败:', error);
      setError(error instanceof Error ? error.message : '未知错误');
      setLoading(false);
    }
  };

  const formatUptime = (uptimeMs: number): string => {
    const days = Math.floor(uptimeMs / (1000 * 60 * 60 * 24));
    const hours = Math.floor((uptimeMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((uptimeMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (days > 0) {
      return `${days}天 ${hours}小时`;
    } else if (hours > 0) {
      return `${hours}小时 ${minutes}分钟`;
    } else {
      return `${minutes}分钟`;
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <div className="system-overview">
        <div className="overview-header">
          <h2>系统概览</h2>
        </div>
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <div style={{
            width: '40px',
            height: '40px',
            border: '4px solid #f3f3f3',
            borderTop: '4px solid #3498db',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 20px'
          }}></div>
          <p>正在加载系统数据...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="system-overview">
        <div className="overview-header">
          <h2>系统概览</h2>
          <button onClick={loadSystemData} style={{
            background: '#3498db',
            color: 'white',
            border: 'none',
            padding: '10px 20px',
            borderRadius: '5px',
            cursor: 'pointer'
          }}>
            🔄 重试
          </button>
        </div>
        <div style={{ textAlign: 'center', padding: '40px', background: '#ffebee', borderRadius: '8px' }}>
          <h3>❌ 加载失败</h3>
          <p>错误信息: {error}</p>
          <button onClick={loadSystemData} style={{
            background: '#f44336',
            color: 'white',
            border: 'none',
            padding: '10px 20px',
            borderRadius: '5px',
            cursor: 'pointer',
            marginTop: '10px'
          }}>
            重新加载
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="system-overview">
      <div className="overview-header">
        <div className="system-title">
          <div className="system-info">
            <h1>{systemInfo.productName}</h1>
            <div className="system-details">
              <span className="version">版本 {systemInfo.version}</span>
              <span className="edition">{systemInfo.edition}</span>
              <span className={`status ${systemInfo.systemStatus}`}>
                {systemInfo.systemStatus === 'healthy' ? '🟢 运行正常' :
                 systemInfo.systemStatus === 'warning' ? '🟡 需要注意' : '🔴 需要处理'}
              </span>
            </div>
            <div className="system-stats">
              <span>已连接数据库: {systemInfo.connectedDatabases}</span>
              <span>活跃备份任务: {systemInfo.activeBackupJobs}</span>
              <span>运行时间: {systemMetrics ? formatUptime(systemMetrics.uptime) : '加载中...'}</span>
            </div>
          </div>
        </div>
        <button onClick={loadSystemData} className="refresh-btn">
          🔄 刷新数据
        </button>
      </div>

      <div className="metrics-grid">
        {/* 系统性能指标 */}
        <div className="metric-card">
          <div className="card-header">
            <h3>系统性能</h3>
            <span className="card-icon">📊</span>
          </div>
          <div className="card-content">
            {systemMetrics ? (
              <div className="performance-metrics">
                <div className="metric-item">
                  <div className="metric-label">CPU使用率</div>
                  <div className="metric-value">{systemMetrics.cpu.usage.toFixed(1)}%</div>
                </div>
                <div className="metric-item">
                  <div className="metric-label">CPU核心数</div>
                  <div className="metric-value">{systemMetrics.cpu.cores} 核心</div>
                </div>
                <div className="metric-item">
                  <div className="metric-label">内存使用率</div>
                  <div className="metric-value">{systemMetrics.memory.usage.toFixed(1)}%</div>
                </div>
                <div className="metric-item">
                  <div className="metric-label">总内存</div>
                  <div className="metric-value">{formatBytes(systemMetrics.memory.total)}</div>
                </div>
              </div>
            ) : (
              <p>加载中...</p>
            )}
          </div>
        </div>

        {/* 备份统计 */}
        <div className="metric-card">
          <div className="card-header">
            <h3>备份统计</h3>
            <span className="card-icon">💾</span>
          </div>
          <div className="card-content">
            {backupMetrics ? (
              <div className="backup-metrics">
                <div className="metric-item">
                  <div className="metric-label">24小时成功备份</div>
                  <div className="metric-value">{backupMetrics.completedBackups24h} 个</div>
                </div>
                <div className="metric-item">
                  <div className="metric-label">24小时失败备份</div>
                  <div className="metric-value">{backupMetrics.failedBackups24h} 个</div>
                </div>
                <div className="metric-item">
                  <div className="metric-label">总备份大小</div>
                  <div className="metric-value">{formatBytes(backupMetrics.totalBackupSize)}</div>
                </div>
                <div className="metric-item">
                  <div className="metric-label">平均备份时间</div>
                  <div className="metric-value">{(backupMetrics.averageBackupDuration / 1000).toFixed(1)} 秒</div>
                </div>
              </div>
            ) : (
              <p>加载中...</p>
            )}
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default SystemOverviewFixed;
