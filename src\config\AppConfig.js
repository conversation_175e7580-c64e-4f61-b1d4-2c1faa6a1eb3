/**
 * 企业级应用配置管理
 * 支持多环境配置、动态配置更新、配置验证
 */
// 默认配置
export const defaultConfig = {
    environment: 'development',
    version: '1.0.0',
    database: {
        host: 'localhost',
        port: 3306,
        database: 'backup_system',
        connectionLimit: 10,
        acquireTimeout: 60000,
        timeout: 60000,
        reconnect: true,
        charset: 'utf8mb4'
    },
    security: {
        jwtSecret: (typeof process !== 'undefined' && process.env && process.env.JWT_SECRET) || 'default-secret-change-in-production',
        jwtExpiresIn: '24h',
        passwordMinLength: 8,
        passwordRequireSpecialChars: true,
        maxLoginAttempts: 5,
        lockoutDuration: 15,
        sessionTimeout: 480,
        encryptionKey: (typeof process !== 'undefined' && process.env && process.env.ENCRYPTION_KEY) || 'default-encryption-key'
    },
    backup: {
        defaultBackupPath: './backups',
        maxConcurrentBackups: 3,
        backupRetentionDays: 30,
        compressionEnabled: true,
        compressionLevel: 6,
        verificationEnabled: true,
        maxBackupSize: 10240, // 10GB
        allowedDatabases: [],
        excludedTables: ['temp_', 'cache_', 'session_']
    },
    monitoring: {
        metricsEnabled: true,
        metricsInterval: 60,
        alertsEnabled: true,
        healthCheckInterval: 30,
        performanceThreshold: {
            cpuUsage: 80,
            memoryUsage: 85,
            diskUsage: 90,
            backupDuration: 120
        }
    },
    logging: {
        level: 'info',
        maxFileSize: 100,
        maxFiles: 10,
        auditEnabled: true,
        sensitiveDataMasking: true,
        logRetentionDays: 90
    },
    notification: {
        email: {
            enabled: false,
            smtp: {
                host: '',
                port: 587,
                secure: false,
                auth: {
                    user: '',
                    pass: ''
                }
            },
            from: '',
            templates: {
                backupSuccess: 'backup-success.html',
                backupFailure: 'backup-failure.html',
                systemAlert: 'system-alert.html'
            }
        },
        webhook: {
            enabled: false,
            url: '',
            secret: ''
        }
    },
    features: {
        incrementalBackup: true,
        compression: true,
        encryption: false,
        clustering: false,
        apiAccess: false
    }
};
/**
 * 配置管理器
 */
export class ConfigManager {
    constructor() {
        this.configPath = (typeof process !== 'undefined' && process.env && process.env.CONFIG_PATH) || './config.json';
        this.config = { ...defaultConfig };
        this.loadConfig();
    }
    static getInstance() {
        if (!ConfigManager.instance) {
            ConfigManager.instance = new ConfigManager();
        }
        return ConfigManager.instance;
    }
    loadConfig() {
        try {
            // 检查是否在Node.js环境中
            if (typeof require !== 'undefined' && typeof process !== 'undefined' && process.versions && process.versions.node) {
                const fs = require('fs');
                if (fs.existsSync(this.configPath)) {
                    const configData = JSON.parse(fs.readFileSync(this.configPath, 'utf8'));
                    this.config = this.mergeConfig(this.config, configData);
                }
            }
            this.loadEnvironmentVariables();
            this.validateConfig();
        }
        catch (error) {
            console.error('Failed to load configuration:', error);
        }
    }
    mergeConfig(defaultConfig, userConfig) {
        const result = { ...defaultConfig };
        for (const key in userConfig) {
            if (typeof userConfig[key] === 'object' && !Array.isArray(userConfig[key])) {
                result[key] = this.mergeConfig(result[key] || {}, userConfig[key]);
            }
            else {
                result[key] = userConfig[key];
            }
        }
        return result;
    }
    loadEnvironmentVariables() {
        // 从环境变量加载敏感配置（仅在Node.js环境中）
        if (typeof process !== 'undefined' && process.env) {
            if (process.env.DB_HOST)
                this.config.database.host = process.env.DB_HOST;
            if (process.env.DB_PORT)
                this.config.database.port = parseInt(process.env.DB_PORT);
            if (process.env.DB_NAME)
                this.config.database.database = process.env.DB_NAME;
            if (process.env.JWT_SECRET)
                this.config.security.jwtSecret = process.env.JWT_SECRET;
            if (process.env.ENCRYPTION_KEY)
                this.config.security.encryptionKey = process.env.ENCRYPTION_KEY;
            if (process.env.NODE_ENV)
                this.config.environment = process.env.NODE_ENV;
        }
    }
    validateConfig() {
        // 配置验证逻辑
        if (this.config.environment === 'production') {
            if (this.config.security.jwtSecret === 'default-secret-change-in-production') {
                throw new Error('JWT secret must be changed in production environment');
            }
            if (this.config.security.encryptionKey === 'default-encryption-key') {
                throw new Error('Encryption key must be changed in production environment');
            }
        }
    }
    getConfig() {
        return { ...this.config };
    }
    updateConfig(updates) {
        this.config = this.mergeConfig(this.config, updates);
        this.saveConfig();
    }
    saveConfig() {
        try {
            // 检查是否在Node.js环境中
            if (typeof require !== 'undefined' && typeof process !== 'undefined' && process.versions && process.versions.node) {
                const fs = require('fs');
                fs.writeFileSync(this.configPath, JSON.stringify(this.config, null, 2));
            }
        }
        catch (error) {
            console.error('Failed to save configuration:', error);
        }
    }
    get(path) {
        const keys = path.split('.');
        let value = this.config;
        for (const key of keys) {
            value = value?.[key];
        }
        return value;
    }
}
export const config = ConfigManager.getInstance();
