; 自定义安装程序脚本
; MySQL增量备份管理器

!macro customInit
  ; 检查是否已安装旧版本
  ReadRegStr $R0 HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\{appId}" "UninstallString"
  StrCmp $R0 "" done
  
  MessageBox MB_OKCANCEL|MB_ICONEXCLAMATION \
    "检测到已安装的MySQL增量备份管理器。$\n$\n点击 '确定' 卸载旧版本并继续安装，或点击 '取消' 退出安装程序。" \
    IDOK uninst
  Abort
  
  uninst:
    ClearErrors
    ExecWait '$R0 _?=$INSTDIR'
    
    IfErrors no_remove_uninstaller done
      Delete $R0
      RMDir $INSTDIR
    no_remove_uninstaller:
  
  done:
!macroend

!macro customInstall
  ; 创建应用程序数据目录
  CreateDirectory "$APPDATA\MySQL Backup Manager"
  CreateDirectory "$APPDATA\MySQL Backup Manager\logs"
  CreateDirectory "$APPDATA\MySQL Backup Manager\config"
  
  ; 设置权限
  AccessControl::GrantOnFile "$APPDATA\MySQL Backup Manager" "(BU)" "FullAccess"
!macroend

!macro customUnInstall
  ; 询问是否保留用户数据
  MessageBox MB_YESNO|MB_ICONQUESTION \
    "是否保留应用程序数据和配置文件？$\n$\n选择 '是' 保留数据，选择 '否' 完全删除。" \
    IDYES keep_data
  
  ; 删除用户数据
  RMDir /r "$APPDATA\MySQL Backup Manager"
  
  keep_data:
  ; 删除注册表项
  DeleteRegKey HKLM "Software\MySQL Backup Manager"
!macroend
