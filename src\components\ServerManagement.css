/* 企业级服务器管理样式 */

.server-management {
  padding: var(--spacing-3xl);
  background: var(--bg-primary);
  min-height: 100vh;
}

.server-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-3xl);
  padding: var(--spacing-2xl) var(--spacing-3xl);
  background: var(--bg-secondary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-card);
  border: 1px solid var(--border-color);
}

.server-management-title {
  margin: 0;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.server-management-title::before {
  content: '🖥️';
  font-size: var(--font-size-xl);
}

.add-server-button {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: var(--text-inverse);
  border: none;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.add-server-button::before {
  content: '+';
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
}

.add-server-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* 服务器列表样式 */
.servers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-3xl);
}

.server-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow-card);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.server-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--info-color));
}

.server-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-card-hover);
  border-color: var(--border-hover);
}

.server-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
}

.server-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.server-status {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.server-status.active {
  background: var(--success-light);
  color: var(--success-color);
  border: 1px solid var(--success-color);
}

.server-status.active::before {
  content: '●';
  color: var(--success-color);
  animation: statusPulse 2s infinite;
}

.server-status.inactive {
  background: var(--secondary-light);
  color: var(--secondary-color);
  border: 1px solid var(--secondary-color);
}

.server-status.inactive::before {
  content: '●';
  color: var(--secondary-color);
}

.server-status.error {
  background: var(--danger-light);
  color: var(--danger-color);
  border: 1px solid var(--danger-color);
}

.server-status.error::before {
  content: '●';
  color: var(--danger-color);
  animation: statusPulse 2s infinite;
}

.server-details {
  margin-bottom: var(--spacing-xl);
}

.server-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--border-color);
}

.server-detail-item:last-child {
  border-bottom: none;
}

.server-detail-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.server-detail-value {
  font-size: var(--font-size-base);
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.server-actions {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
}

.server-action-button {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  min-height: auto;
}

.server-action-button:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
  border-color: var(--border-hover);
  transform: translateY(-1px);
}

.server-action-button.test {
  color: var(--info-color);
  border-color: var(--info-color);
}

.server-action-button.test:hover {
  background: var(--info-light);
  color: var(--info-color);
}

.server-action-button.edit {
  color: var(--warning-color);
  border-color: var(--warning-color);
}

.server-action-button.edit:hover {
  background: var(--warning-light);
  color: var(--warning-color);
}

.server-action-button.delete {
  color: var(--danger-color);
  border-color: var(--danger-color);
}

.server-action-button.delete:hover {
  background: var(--danger-light);
  color: var(--danger-color);
}

/* 加载状态 */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-4xl);
  color: var(--text-secondary);
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: var(--spacing-5xl);
  color: var(--text-secondary);
}

.empty-state-icon {
  font-size: 64px;
  margin-bottom: var(--spacing-lg);
  opacity: 0.5;
}

.empty-state-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.empty-state-description {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: var(--line-height-relaxed);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .servers-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .server-management-header {
    flex-direction: column;
    gap: var(--spacing-lg);
    align-items: stretch;
  }
  
  .server-actions {
    flex-wrap: wrap;
  }
  
  .server-management {
    padding: var(--spacing-lg);
  }
}
