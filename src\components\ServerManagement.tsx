import React, { useState, useEffect } from 'react';
import { ServerData } from '../types/electron';

interface Server {
  id: number;
  name: string;
  host: string;
  port: number;
  username: string;
  ssh_host?: string;
  ssh_port?: number;
  ssh_username?: string;
  status: 'active' | 'inactive' | 'error';
  created_at: string;
  updated_at: string;
}

interface ServerManagementProps {
  userId: number;
}

const ServerManagement: React.FC<ServerManagementProps> = ({ userId }) => {
  const [servers, setServers] = useState<Server[]>([]);
  const [loading, setLoading] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingServer, setEditingServer] = useState<Server | null>(null);
  const [testingConnection, setTestingConnection] = useState<number | null>(null);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showTestDialog, setShowTestDialog] = useState(false);
  const [testingServer, setTestingServer] = useState<Server | null>(null);
  const [testPassword, setTestPassword] = useState('');
  const [testSshPassword, setTestSshPassword] = useState('');

  const [formData, setFormData] = useState({
    name: '',
    host: '',
    port: 3306,
    username: '',
    password: '',
    sshHost: '',
    sshPort: 22,
    sshUsername: '',
    sshPassword: '',
    sshPrivateKey: ''
  });

  // 加载服务器列表
  const loadServers = async () => {
    try {
      setLoading(true);
      const result = await window.electronAPI.getServers(userId);
      if (result.success) {
        setServers(result.servers || []);
      } else {
        setError(result.message || '获取服务器列表失败');
      }
    } catch (error) {
      console.error('获取服务器列表失败:', error);
      setError('获取服务器列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadServers();
  }, [userId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'port' || name === 'sshPort' ? parseInt(value) || 0 : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name || !formData.host || !formData.username || !formData.password) {
      setError('请填写必填字段');
      return;
    }

    // SSH配置是必填的
    if (!formData.sshHost || !formData.sshUsername) {
      setError('SSH连接配置是必填的，增量备份需要存储在服务器文件系统中');
      return;
    }

    if (!formData.sshPassword && !formData.sshPrivateKey) {
      setError('必须提供SSH密码或私钥');
      return;
    }

    try {
      setLoading(true);
      setError('');
      setSuccess('');

      const serverConfig = {
        host: formData.host,
        port: formData.port,
        username: formData.username,
        password: formData.password,
        sshHost: formData.sshHost?.trim() || undefined,
        sshPort: formData.sshPort || undefined,
        sshUsername: formData.sshUsername?.trim() || undefined,
        sshPassword: formData.sshPassword?.trim() || undefined,
        sshPrivateKey: formData.sshPrivateKey?.trim() || undefined
      };

      // 先测试连接
      console.log('测试连接配置:', { host: serverConfig.host, port: serverConfig.port });
      const testResult = await window.electronAPI.testServerConnection(serverConfig);

      if (!testResult.success) {
        setError(`连接测试失败: ${testResult.message}`);
        return;
      }

      // 连接测试成功，继续保存服务器配置
      const serverData: ServerData = {
        userId,
        name: formData.name,
        host: formData.host,
        port: formData.port,
        username: formData.username,
        password: formData.password,
        sshHost: formData.sshHost?.trim() || undefined,
        sshPort: formData.sshPort || undefined,
        sshUsername: formData.sshUsername?.trim() || undefined,
        sshPassword: formData.sshPassword?.trim() || undefined,
        sshPrivateKey: formData.sshPrivateKey?.trim() || undefined
      };

      let result;
      if (editingServer) {
        // 更新服务器
        result = await window.electronAPI.updateServer(editingServer.id, userId, serverData);
        if (result.success) {
          setSuccess(`服务器更新成功！MySQL版本: ${testResult.details?.version || '未知'}`);
        }
      } else {
        // 添加新服务器
        result = await window.electronAPI.addServer(serverData);
        if (result.success) {
          setSuccess(`服务器添加成功！MySQL版本: ${testResult.details?.version || '未知'}`);
        }
      }

      if (result.success) {
        setFormData({
          name: '',
          host: '',
          port: 3306,
          username: '',
          password: '',
          sshHost: '',
          sshPort: 22,
          sshUsername: '',
          sshPassword: '',
          sshPrivateKey: ''
        });
        setShowAddForm(false);
        setEditingServer(null);
        loadServers(); // 重新加载列表
      } else {
        setError(result.message);
      }
    } catch (error) {
      console.error(editingServer ? '更新服务器失败:' : '添加服务器失败:', error);
      setError(editingServer ? '更新服务器失败，请稍后重试' : '添加服务器失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (serverId: number) => {
    if (!confirm('确定要删除这个服务器吗？')) {
      return;
    }

    try {
      setLoading(true);
      const result = await window.electronAPI.deleteServer(serverId, userId);

      if (result.success) {
        setSuccess('服务器删除成功');
        loadServers(); // 重新加载列表
      } else {
        setError(result.message);
      }
    } catch (error) {
      console.error('删除服务器失败:', error);
      setError('删除服务器失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = async (serverId: number) => {
    try {
      setLoading(true);
      const result = await window.electronAPI.getServer(serverId, userId);

      if (result.success && result.server) {
        const server = result.server;
        setEditingServer(server);
        setFormData({
          name: server.name || '',
          host: server.host || '',
          port: server.port || 3306,
          username: server.username || '',
          password: server.password || '',
          sshHost: server.ssh_host || '',
          sshPort: server.ssh_port || 22,
          sshUsername: server.ssh_username || '',
          sshPassword: server.ssh_password || '',
          sshPrivateKey: server.ssh_private_key || ''
        });
        setShowAddForm(true);
      } else {
        setError(result.message || '获取服务器信息失败');
      }
    } catch (error) {
      console.error('获取服务器信息失败:', error);
      setError('获取服务器信息失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleTestConnection = (server: Server) => {
    setTestingServer(server);
    setTestPassword('');
    setTestSshPassword('');
    setShowTestDialog(true);
  };

  const performTestConnection = async () => {
    if (!testingServer) return;

    try {
      setTestingConnection(testingServer.id);
      setError('');
      setSuccess('');

      const serverConfig = {
        host: testingServer.host,
        port: testingServer.port,
        username: testingServer.username,
        password: testPassword,
        sshHost: testingServer.ssh_host || undefined,
        sshPort: testingServer.ssh_port || undefined,
        sshUsername: testingServer.ssh_username || undefined,
        sshPassword: testSshPassword?.trim() || testingServer.ssh_password || undefined,
        sshPrivateKey: testingServer.ssh_private_key || undefined
      };

      const result = await window.electronAPI.testServerConnection(serverConfig);

      if (result.success) {
        setSuccess(`连接测试成功！${result.details?.version ? `MySQL版本: ${result.details.version}` : ''}`);
        setShowTestDialog(false);

        // 更新服务器状态为活跃
        try {
          await window.electronAPI.updateServerStatus(testingServer.id, 'active');
          // 重新加载服务器列表以更新状态显示
          loadServers();
        } catch (error) {
          console.error('更新服务器状态失败:', error);
        }
      } else {
        setError(`连接测试失败: ${result.message}`);

        // 更新服务器状态为错误
        try {
          await window.electronAPI.updateServerStatus(testingServer.id, 'error');
          loadServers();
        } catch (error) {
          console.error('更新服务器状态失败:', error);
        }
      }
    } catch (error) {
      console.error('连接测试失败:', error);
      setError('连接测试失败，请稍后重试');
    } finally {
      setTestingConnection(null);
    }
  };

  return (
    <div className="server-management">
      <div className="section-header">
        <h3>服务器管理</h3>
        <button
          className="primary-button"
          onClick={() => {
            setShowAddForm(!showAddForm);
            if (showAddForm) {
              setEditingServer(null);
              setFormData({
                name: '',
                host: '',
                port: 3306,
                username: '',
                password: '',
                sshHost: '',
                sshPort: 22,
                sshUsername: '',
                sshPassword: '',
                sshPrivateKey: ''
              });
            }
          }}
          disabled={loading}
        >
          {showAddForm ? '取消' : '添加服务器'}
        </button>
      </div>

      {error && <div className="error-message">{error}</div>}
      {success && <div className="success-message">{success}</div>}

      {showAddForm && (
        <div className="add-server-form">
          <h4>{editingServer ? '编辑服务器' : '添加新服务器'}</h4>
          <form onSubmit={handleSubmit}>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="name">服务器名称 *</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="例如：生产环境MySQL"
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="host">主机地址 *</label>
                <input
                  type="text"
                  id="host"
                  name="host"
                  value={formData.host}
                  onChange={handleInputChange}
                  placeholder="例如：*************"
                  required
                />
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="port">端口</label>
                <input
                  type="number"
                  id="port"
                  name="port"
                  value={formData.port}
                  onChange={handleInputChange}
                  min="1"
                  max="65535"
                />
              </div>
              <div className="form-group">
                <label htmlFor="username">MySQL用户名 *</label>
                <input
                  type="text"
                  id="username"
                  name="username"
                  value={formData.username}
                  onChange={handleInputChange}
                  placeholder="MySQL用户名"
                  required
                />
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="password">MySQL密码 *</label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                placeholder="MySQL密码"
                required
              />
            </div>

            <div className="ssh-section">
              <h5>SSH连接配置（必填 - 增量备份需要存储在服务器文件系统）</h5>
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="sshHost">SSH主机 *</label>
                  <input
                    type="text"
                    id="sshHost"
                    name="sshHost"
                    value={formData.sshHost}
                    onChange={handleInputChange}
                    placeholder="SSH服务器地址"
                    required
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="sshPort">SSH端口</label>
                  <input
                    type="number"
                    id="sshPort"
                    name="sshPort"
                    value={formData.sshPort}
                    onChange={handleInputChange}
                    min="1"
                    max="65535"
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="sshUsername">SSH用户名 *</label>
                  <input
                    type="text"
                    id="sshUsername"
                    name="sshUsername"
                    value={formData.sshUsername}
                    onChange={handleInputChange}
                    placeholder="SSH用户名"
                    required
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="sshPassword">SSH密码 *</label>
                  <input
                    type="password"
                    id="sshPassword"
                    name="sshPassword"
                    value={formData.sshPassword}
                    onChange={handleInputChange}
                    placeholder="SSH密码（或使用私钥）"
                  />
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="sshPrivateKey">SSH私钥（可选，优先于密码）</label>
                <textarea
                  id="sshPrivateKey"
                  name="sshPrivateKey"
                  value={formData.sshPrivateKey}
                  onChange={handleInputChange}
                  placeholder="粘贴SSH私钥内容（如果使用私钥认证）"
                  rows={4}
                />
              </div>
            </div>

            <div className="form-actions">
              <button type="submit" className="primary-button" disabled={loading}>
                {loading ? (editingServer ? '更新中...' : '添加中...') : (editingServer ? '更新服务器' : '添加服务器')}
              </button>
              <button 
                type="button" 
                className="secondary-button"
                onClick={() => setShowAddForm(false)}
                disabled={loading}
              >
                取消
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="servers-list">
        {loading && !showAddForm && <div className="loading">加载中...</div>}
        
        {servers.length === 0 && !loading ? (
          <div className="empty-state">
            <p>还没有添加任何服务器</p>
            <p>点击"添加服务器"按钮开始配置</p>
          </div>
        ) : (
          <div className="servers-grid">
            {servers.map(server => (
              <div key={server.id} className="server-card">
                <div className="server-header">
                  <h4>{server.name}</h4>
                  <span className={`status-badge ${server.status}`}>
                    {server.status === 'active' ? '活跃' : 
                     server.status === 'inactive' ? '未连接' : '错误'}
                  </span>
                </div>
                <div className="server-details">
                  <p><strong>主机:</strong> {server.host}:{server.port}</p>
                  <p><strong>用户:</strong> {server.username}</p>
                  {server.ssh_host && (
                    <p><strong>SSH:</strong> {server.ssh_username}@{server.ssh_host}:{server.ssh_port}</p>
                  )}
                  <p><strong>创建时间:</strong> {new Date(server.created_at).toLocaleString()}</p>
                </div>
                <div className="server-actions">
                  <button
                    className="secondary-button"
                    onClick={() => handleTestConnection(server)}
                    disabled={loading || testingConnection === server.id}
                  >
                    {testingConnection === server.id ? '测试中...' : '测试连接'}
                  </button>
                  <button
                    className="primary-button"
                    onClick={() => handleEdit(server.id)}
                    disabled={loading}
                  >
                    编辑
                  </button>
                  <button
                    className="danger-button"
                    onClick={() => handleDelete(server.id)}
                    disabled={loading}
                  >
                    删除
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 测试连接对话框 */}
      {showTestDialog && testingServer && (
        <div className="modal-overlay">
          <div className="modal-content">
            <h4>测试连接 - {testingServer.name}</h4>
            <p>为了安全起见，请重新输入密码进行连接测试</p>

            <div className="form-group">
              <label htmlFor="testPassword">MySQL密码 *</label>
              <input
                type="password"
                id="testPassword"
                value={testPassword}
                onChange={(e) => setTestPassword(e.target.value)}
                placeholder="输入MySQL密码"
                required
              />
            </div>

            {testingServer.ssh_host && (
              <div className="form-group">
                <label htmlFor="testSshPassword">SSH密码 {testingServer.ssh_private_key ? '(可选)' : '*'}</label>
                <input
                  type="password"
                  id="testSshPassword"
                  value={testSshPassword}
                  onChange={(e) => setTestSshPassword(e.target.value)}
                  placeholder="输入SSH密码（如果使用私钥可留空）"
                />
              </div>
            )}

            <div className="modal-actions">
              <button
                type="button"
                className="primary-button"
                onClick={performTestConnection}
                disabled={!testPassword || testingConnection === testingServer.id}
              >
                {testingConnection === testingServer.id ? '测试中...' : '开始测试'}
              </button>
              <button
                type="button"
                className="secondary-button"
                onClick={() => {
                  setShowTestDialog(false);
                  setTestingServer(null);
                  setTestPassword('');
                  setTestSshPassword('');
                }}
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ServerManagement;
