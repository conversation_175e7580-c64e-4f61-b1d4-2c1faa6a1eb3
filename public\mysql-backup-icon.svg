<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="dbGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E5BBA;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="arrowGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#7ED321;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5BA517;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="128" cy="128" r="120" fill="url(#dbGradient)" stroke="#1E3A8A" stroke-width="4"/>
  
  <!-- 数据库主体 -->
  <ellipse cx="128" cy="100" rx="60" ry="15" fill="#E3F2FD" stroke="#1976D2" stroke-width="2"/>
  <rect x="68" y="100" width="120" height="60" fill="url(#dbGradient)" stroke="#1976D2" stroke-width="2"/>
  <ellipse cx="128" cy="160" rx="60" ry="15" fill="#BBDEFB" stroke="#1976D2" stroke-width="2"/>
  
  <!-- 数据库分层线 -->
  <ellipse cx="128" cy="120" rx="60" ry="15" fill="none" stroke="#E3F2FD" stroke-width="1.5"/>
  <ellipse cx="128" cy="140" rx="60" ry="15" fill="none" stroke="#E3F2FD" stroke-width="1.5"/>
  
  <!-- 增量箭头 -->
  <path d="M 180 80 L 200 60 L 220 80 L 210 80 L 210 120 L 190 120 L 190 80 Z" 
        fill="url(#arrowGradient)" stroke="#4CAF50" stroke-width="2"/>
  
  <!-- 小箭头表示增量 -->
  <path d="M 185 100 L 195 90 L 205 100 L 200 100 L 200 110 L 190 110 L 190 100 Z" 
        fill="#81C784" stroke="#4CAF50" stroke-width="1"/>
  
  <!-- MySQL标识 -->
  <text x="128" y="190" text-anchor="middle" font-family="Arial, sans-serif" 
        font-size="14" font-weight="bold" fill="#FFFFFF">MySQL</text>
  
  <!-- 备份标识 -->
  <text x="128" y="210" text-anchor="middle" font-family="Arial, sans-serif" 
        font-size="12" fill="#E3F2FD">Backup</text>
</svg>
