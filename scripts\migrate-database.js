import mysql from 'mysql2/promise';

// 数据库配置
const DB_CONFIG = {
  host: '***************',
  user: 'user-hiram',
  password: 'user-hiram',
  database: 'user',
  port: 3306
};

async function migrateDatabase() {
  let connection;
  
  try {
    console.log('连接到数据库...');
    connection = await mysql.createConnection(DB_CONFIG);
    console.log('数据库连接成功');

    // 检查 custom_interval_minutes 字段是否存在
    console.log('检查 custom_interval_minutes 字段...');
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? 
      AND TABLE_NAME = 'backup_tasks' 
      AND COLUMN_NAME = 'custom_interval_minutes'
    `, [DB_CONFIG.database]);

    if (columns.length === 0) {
      console.log('添加 custom_interval_minutes 字段...');
      await connection.execute(`
        ALTER TABLE backup_tasks
        ADD COLUMN custom_interval_minutes INT DEFAULT NULL COMMENT '自定义备份间隔（分钟）'
      `);
      console.log('✅ custom_interval_minutes 字段添加成功');
    } else {
      console.log('✅ custom_interval_minutes 字段已存在');
    }

    // 更新 schedule_type 枚举值
    console.log('更新 schedule_type 枚举值...');
    await connection.execute(`
      ALTER TABLE backup_tasks
      MODIFY COLUMN schedule_type ENUM('manual', 'hourly', 'daily', 'weekly', 'monthly', 'custom') DEFAULT 'manual'
    `);
    console.log('✅ schedule_type 枚举值更新成功');

    // 验证表结构
    console.log('验证表结构...');
    const [tableStructure] = await connection.execute(`
      DESCRIBE backup_tasks
    `);
    
    console.log('backup_tasks 表结构:');
    tableStructure.forEach(column => {
      console.log(`  ${column.Field}: ${column.Type} ${column.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${column.Default ? `DEFAULT ${column.Default}` : ''}`);
    });

    console.log('\n🎉 数据库迁移完成！');

  } catch (error) {
    console.error('❌ 数据库迁移失败:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 运行迁移
migrateDatabase();
