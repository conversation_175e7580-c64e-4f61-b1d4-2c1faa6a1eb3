/**
 * 企业级角色权限管理系统
 * 支持基于角色的访问控制(RBAC)、细粒度权限、权限继承
 */
export var Permission;
(function (Permission) {
    // 用户管理权限
    Permission["USER_CREATE"] = "user:create";
    Permission["USER_READ"] = "user:read";
    Permission["USER_UPDATE"] = "user:update";
    Permission["USER_DELETE"] = "user:delete";
    Permission["USER_LIST"] = "user:list";
    // 服务器管理权限
    Permission["SERVER_CREATE"] = "server:create";
    Permission["SERVER_READ"] = "server:read";
    Permission["SERVER_UPDATE"] = "server:update";
    Permission["SERVER_DELETE"] = "server:delete";
    Permission["SERVER_LIST"] = "server:list";
    Permission["SERVER_TEST"] = "server:test";
    // 备份任务权限
    Permission["BACKUP_TASK_CREATE"] = "backup_task:create";
    Permission["BACKUP_TASK_READ"] = "backup_task:read";
    Permission["BACKUP_TASK_UPDATE"] = "backup_task:update";
    Permission["BACKUP_TASK_DELETE"] = "backup_task:delete";
    Permission["BACKUP_TASK_LIST"] = "backup_task:list";
    Permission["BACKUP_TASK_EXECUTE"] = "backup_task:execute";
    // 备份历史权限
    Permission["BACKUP_HISTORY_READ"] = "backup_history:read";
    Permission["BACKUP_HISTORY_LIST"] = "backup_history:list";
    Permission["BACKUP_HISTORY_DOWNLOAD"] = "backup_history:download";
    Permission["BACKUP_HISTORY_DELETE"] = "backup_history:delete";
    // 系统管理权限
    Permission["SYSTEM_CONFIG"] = "system:config";
    Permission["SYSTEM_MONITOR"] = "system:monitor";
    Permission["SYSTEM_LOGS"] = "system:logs";
    Permission["SYSTEM_AUDIT"] = "system:audit";
    // 高级权限
    Permission["ADMIN_FULL"] = "admin:full";
    Permission["SECURITY_MANAGE"] = "security:manage";
    Permission["ROLE_MANAGE"] = "role:manage";
})(Permission || (Permission = {}));
/**
 * 预定义系统角色
 */
export const SystemRoles = {
    SUPER_ADMIN: {
        id: 'super_admin',
        name: '超级管理员',
        description: '拥有所有权限的系统管理员',
        permissions: Object.values(Permission),
        isSystem: true,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    ADMIN: {
        id: 'admin',
        name: '管理员',
        description: '系统管理员，拥有大部分管理权限',
        permissions: [
            Permission.USER_READ,
            Permission.USER_LIST,
            Permission.SERVER_CREATE,
            Permission.SERVER_READ,
            Permission.SERVER_UPDATE,
            Permission.SERVER_DELETE,
            Permission.SERVER_LIST,
            Permission.SERVER_TEST,
            Permission.BACKUP_TASK_CREATE,
            Permission.BACKUP_TASK_READ,
            Permission.BACKUP_TASK_UPDATE,
            Permission.BACKUP_TASK_DELETE,
            Permission.BACKUP_TASK_LIST,
            Permission.BACKUP_TASK_EXECUTE,
            Permission.BACKUP_HISTORY_READ,
            Permission.BACKUP_HISTORY_LIST,
            Permission.BACKUP_HISTORY_DOWNLOAD,
            Permission.BACKUP_HISTORY_DELETE,
            Permission.SYSTEM_MONITOR,
            Permission.SYSTEM_LOGS
        ],
        isSystem: true,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    OPERATOR: {
        id: 'operator',
        name: '操作员',
        description: '备份操作员，可以执行备份任务和查看历史',
        permissions: [
            Permission.SERVER_READ,
            Permission.SERVER_LIST,
            Permission.SERVER_TEST,
            Permission.BACKUP_TASK_READ,
            Permission.BACKUP_TASK_LIST,
            Permission.BACKUP_TASK_EXECUTE,
            Permission.BACKUP_HISTORY_READ,
            Permission.BACKUP_HISTORY_LIST,
            Permission.BACKUP_HISTORY_DOWNLOAD
        ],
        isSystem: true,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    VIEWER: {
        id: 'viewer',
        name: '查看者',
        description: '只读权限，可以查看备份状态和历史',
        permissions: [
            Permission.SERVER_READ,
            Permission.SERVER_LIST,
            Permission.BACKUP_TASK_READ,
            Permission.BACKUP_TASK_LIST,
            Permission.BACKUP_HISTORY_READ,
            Permission.BACKUP_HISTORY_LIST
        ],
        isSystem: true,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    USER: {
        id: 'user',
        name: '普通用户',
        description: '普通用户，只能管理自己的备份任务',
        permissions: [
            Permission.SERVER_CREATE,
            Permission.SERVER_READ,
            Permission.SERVER_UPDATE,
            Permission.SERVER_DELETE,
            Permission.SERVER_LIST,
            Permission.SERVER_TEST,
            Permission.BACKUP_TASK_CREATE,
            Permission.BACKUP_TASK_READ,
            Permission.BACKUP_TASK_UPDATE,
            Permission.BACKUP_TASK_DELETE,
            Permission.BACKUP_TASK_LIST,
            Permission.BACKUP_TASK_EXECUTE,
            Permission.BACKUP_HISTORY_READ,
            Permission.BACKUP_HISTORY_LIST,
            Permission.BACKUP_HISTORY_DOWNLOAD
        ],
        isSystem: true,
        createdAt: new Date(),
        updatedAt: new Date()
    }
};
/**
 * 角色权限管理器
 */
export class RoleManager {
    constructor() {
        this.roles = new Map();
        this.userRoles = new Map();
        this.initializeSystemRoles();
    }
    static getInstance() {
        if (!RoleManager.instance) {
            RoleManager.instance = new RoleManager();
        }
        return RoleManager.instance;
    }
    initializeSystemRoles() {
        Object.values(SystemRoles).forEach(role => {
            this.roles.set(role.id, role);
        });
    }
    /**
     * 创建自定义角色
     */
    createRole(role) {
        const newRole = {
            ...role,
            id: this.generateRoleId(),
            createdAt: new Date(),
            updatedAt: new Date()
        };
        this.roles.set(newRole.id, newRole);
        return newRole;
    }
    /**
     * 更新角色
     */
    updateRole(roleId, updates) {
        const role = this.roles.get(roleId);
        if (!role) {
            return null;
        }
        if (role.isSystem) {
            throw new Error('Cannot modify system roles');
        }
        const updatedRole = {
            ...role,
            ...updates,
            id: roleId, // 确保ID不被修改
            updatedAt: new Date()
        };
        this.roles.set(roleId, updatedRole);
        return updatedRole;
    }
    /**
     * 删除角色
     */
    deleteRole(roleId) {
        const role = this.roles.get(roleId);
        if (!role) {
            return false;
        }
        if (role.isSystem) {
            throw new Error('Cannot delete system roles');
        }
        // 检查是否有用户使用此角色
        const usersWithRole = Array.from(this.userRoles.values())
            .flat()
            .filter(ur => ur.roleId === roleId);
        if (usersWithRole.length > 0) {
            throw new Error('Cannot delete role that is assigned to users');
        }
        return this.roles.delete(roleId);
    }
    /**
     * 获取角色
     */
    getRole(roleId) {
        return this.roles.get(roleId) || null;
    }
    /**
     * 获取所有角色
     */
    getAllRoles() {
        return Array.from(this.roles.values());
    }
    /**
     * 为用户分配角色
     */
    assignRoleToUser(userId, roleId, assignedBy, expiresAt) {
        const role = this.roles.get(roleId);
        if (!role) {
            throw new Error('Role not found');
        }
        const userRole = {
            userId,
            roleId,
            assignedBy,
            assignedAt: new Date(),
            expiresAt
        };
        const userRoles = this.userRoles.get(userId) || [];
        // 检查是否已经有此角色
        const existingRoleIndex = userRoles.findIndex(ur => ur.roleId === roleId);
        if (existingRoleIndex >= 0) {
            userRoles[existingRoleIndex] = userRole; // 更新现有角色
        }
        else {
            userRoles.push(userRole); // 添加新角色
        }
        this.userRoles.set(userId, userRoles);
    }
    /**
     * 移除用户角色
     */
    removeRoleFromUser(userId, roleId) {
        const userRoles = this.userRoles.get(userId);
        if (!userRoles) {
            return false;
        }
        const filteredRoles = userRoles.filter(ur => ur.roleId !== roleId);
        if (filteredRoles.length === userRoles.length) {
            return false; // 没有找到要移除的角色
        }
        this.userRoles.set(userId, filteredRoles);
        return true;
    }
    /**
     * 获取用户角色
     */
    getUserRoles(userId) {
        const userRoles = this.userRoles.get(userId) || [];
        const currentTime = new Date();
        return userRoles
            .filter(ur => !ur.expiresAt || ur.expiresAt > currentTime) // 过滤过期角色
            .map(ur => this.roles.get(ur.roleId))
            .filter(role => role !== undefined);
    }
    /**
     * 获取用户所有权限（包括继承的权限）
     */
    getUserPermissions(userId) {
        const roles = this.getUserRoles(userId);
        const permissions = new Set();
        const addRolePermissions = (role) => {
            // 添加角色直接权限
            role.permissions.forEach(permission => permissions.add(permission));
            // 添加继承的权限
            if (role.inherits) {
                role.inherits.forEach(inheritedRoleId => {
                    const inheritedRole = this.roles.get(inheritedRoleId);
                    if (inheritedRole) {
                        addRolePermissions(inheritedRole);
                    }
                });
            }
        };
        roles.forEach(addRolePermissions);
        return Array.from(permissions);
    }
    /**
     * 检查用户是否有特定权限
     */
    hasPermission(userId, permission) {
        const userPermissions = this.getUserPermissions(userId);
        return userPermissions.includes(permission) || userPermissions.includes(Permission.ADMIN_FULL);
    }
    /**
     * 检查用户是否有任一权限
     */
    hasAnyPermission(userId, permissions) {
        return permissions.some(permission => this.hasPermission(userId, permission));
    }
    /**
     * 检查用户是否有所有权限
     */
    hasAllPermissions(userId, permissions) {
        return permissions.every(permission => this.hasPermission(userId, permission));
    }
    generateRoleId() {
        return 'role_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
}
export const roleManager = RoleManager.getInstance();
