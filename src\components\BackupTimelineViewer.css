/* 备份时间节点查看器样式 */

.backup-timeline-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
  padding: var(--spacing-lg);
}

.backup-timeline-modal {
  background: var(--bg-secondary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-modal);
  border: 1px solid var(--border-color);
  width: 95%;
  max-width: 1000px;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
  overflow: hidden;
}

.backup-timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-2xl) var(--spacing-3xl);
  border-bottom: 1px solid var(--border-color);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  color: var(--text-inverse);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.backup-timeline-header h2 {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.backup-timeline-header h2::before {
  content: '⏰';
  font-size: var(--font-size-lg);
}

.close-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: var(--text-inverse);
  font-size: var(--font-size-xl);
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal);
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.backup-timeline-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-2xl);
}

.loading-state, .error-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4xl);
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-lg);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  color: var(--danger-color);
  margin-bottom: var(--spacing-lg);
}

.retry-button {
  background: var(--primary-color);
  color: var(--text-inverse);
  border: none;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.retry-button:hover {
  background: var(--primary-hover);
  transform: translateY(-1px);
}

.timeline-list {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-card);
}

.timeline-header {
  background: var(--bg-tertiary);
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr 1.5fr;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  border-bottom: 2px solid var(--border-color);
  font-size: var(--font-size-sm);
  align-items: center;
}

.timeline-item {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr 1.5fr;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  transition: all var(--transition-normal);
  align-items: center;
  position: relative;
  min-height: 80px;
  background: var(--bg-primary);
}

.timeline-item:hover {
  background: var(--bg-hover);
  transform: translateX(4px);
}

.timeline-item:last-child {
  border-bottom: none;
}

.timeline-item.success {
  border-left: 4px solid var(--success-color);
}

.timeline-item.error {
  border-left: 4px solid var(--danger-color);
}

.timeline-item.warning {
  border-left: 4px solid var(--warning-color);
}

.timeline-time {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  min-width: 0;
}

.start-time {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.end-time {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.backup-type {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
}

.backup-type.full {
  background: var(--primary-light);
  color: var(--primary-color);
}

.backup-type.incremental {
  background: var(--info-light);
  color: var(--info-color);
}

.status-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
}

.status-badge.success {
  background: var(--success-light);
  color: var(--success-color);
}

.status-badge.error {
  background: var(--danger-light);
  color: var(--danger-color);
}

.status-badge.warning {
  background: var(--warning-light);
  color: var(--warning-color);
}

.file-size {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  white-space: nowrap;
}

.binlog-info {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  line-height: 1.3;
  min-width: 0;
}

.binlog-info div {
  margin-bottom: var(--spacing-xs);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.no-binlog {
  color: var(--text-tertiary);
  font-style: italic;
}

.timeline-actions {
  display: flex;
  justify-content: center;
  align-items: center;
}

.download-button {
  padding: var(--spacing-xs) var(--spacing-md);
  border: none;
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-normal);
  min-width: 80px;
  text-align: center;
  white-space: nowrap;
}

.download-button {
  background: var(--primary-color);
  color: white;
}

.download-button:hover:not(:disabled) {
  background: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.download-button:disabled {
  background: var(--bg-disabled);
  color: var(--text-disabled);
  cursor: not-allowed;
  opacity: 0.6;
}

.backup-timeline-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-3xl);
  border-top: 1px solid var(--border-color);
  background: var(--bg-tertiary);
}

.refresh-button, .close-footer-button {
  padding: var(--spacing-sm) var(--spacing-lg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.refresh-button {
  background: var(--primary-color);
  color: var(--text-inverse);
  border-color: var(--primary-color);
}

.refresh-button:hover {
  background: var(--primary-hover);
  transform: translateY(-1px);
}

.close-footer-button {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.close-footer-button:hover {
  background: var(--bg-hover);
  border-color: var(--border-hover);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .backup-timeline-modal {
    width: 98%;
    max-height: 95vh;
  }
  
  .timeline-header, .timeline-item {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }
  
  .timeline-actions {
    justify-content: flex-start;
  }
  
  .backup-timeline-header {
    padding: var(--spacing-lg);
  }
  
  .backup-timeline-content {
    padding: var(--spacing-lg);
  }
}
