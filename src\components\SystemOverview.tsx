/**
 * 企业级系统概览组件
 * 显示系统性能指标、备份状态、告警信息等
 */

import React, { useState, useEffect } from 'react';
import './SystemOverview.css';

interface SystemMetrics {
  timestamp: Date;
  cpu: { usage: number; cores: number; loadAverage: number[] };
  memory: { total: number; used: number; free: number; usage: number };
  disk?: { total: number; used: number; free: number; usage: number };
  network?: { bytesReceived: number; bytesSent: number };
  uptime: number;
}

interface BackupMetrics {
  timestamp: Date;
  activeBackups: number;
  queuedBackups: number;
  completedBackups24h: number;
  failedBackups24h: number;
  averageBackupDuration: number;
  totalBackupSize: number;
  oldestBackup: Date;
  newestBackup: Date;
}

interface Alert {
  id: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  timestamp: Date;
  acknowledged: boolean;
  resolved: boolean;
}

interface SystemInfo {
  productName: string;
  version: string;
  edition: string;
  licenseTo: string;
  installDate: Date;
  lastUpdateCheck: Date;
  connectedDatabases: number;
  activeBackupJobs: number;
  systemStatus: 'healthy' | 'warning' | 'critical';
}

const SystemOverview: React.FC = () => {
  const [systemInfo, setSystemInfo] = useState<SystemInfo>({
    productName: 'MySQL 增量备份系统',
    version: '1.0.0',
    edition: '企业版',
    licenseTo: '企业用户',
    installDate: new Date('2024-01-15'),
    lastUpdateCheck: new Date(),
    connectedDatabases: 3,
    activeBackupJobs: 1,
    systemStatus: 'healthy'
  });
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null);
  const [backupMetrics, setBackupMetrics] = useState<BackupMetrics | null>(null);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSystemData();
    
    // 设置自动刷新
    const interval = setInterval(loadSystemData, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadSystemData = async () => {
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      if (!user.id) {
        console.error('用户未登录');
        setLoading(false);
        return;
      }

      // 检查 electronAPI 是否可用
      if (!window.electronAPI) {
        console.error('electronAPI 不可用');
        setLoading(false);
        return;
      }

      // 获取真实的系统指标
      const metricsResult = await window.electronAPI.getSystemMetrics();
      if (metricsResult.success) {
        setSystemMetrics(metricsResult.data);
      }

      // 获取真实的备份统计
      const statsResult = await window.electronAPI.getBackupStats(user.id);
      if (statsResult.success) {
        const stats = statsResult.data;
        setBackupMetrics({
          timestamp: stats.timestamp,
          activeBackups: stats.activeBackupJobs,
          queuedBackups: 0, // 暂时设为0，后续可以从任务队列获取
          completedBackups24h: stats.completedBackups24h,
          failedBackups24h: stats.failedBackups24h,
          averageBackupDuration: stats.averageBackupDuration,
          totalBackupSize: stats.totalBackupSize,
          oldestBackup: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 暂时使用固定值
          newestBackup: stats.latestBackup ? new Date(stats.latestBackup.time) : new Date()
        });

        // 更新系统信息中的真实数据
        setSystemInfo(prev => ({
          ...prev,
          connectedDatabases: stats.connectedDatabases,
          activeBackupJobs: stats.activeBackupJobs,
          systemStatus: stats.failedBackups24h > 0 ? 'warning' : 'healthy'
        }));
      }

      // 生成基于真实数据的告警
      const alerts: Alert[] = [];

      if (statsResult.success && statsResult.data.failedBackups24h > 0) {
        alerts.push({
          id: 'backup-failures',
          severity: 'high',
          title: '备份失败警告',
          message: `过去24小时内有 ${statsResult.data.failedBackups24h} 个备份任务失败`,
          timestamp: new Date(Date.now() - 30 * 60 * 1000),
          acknowledged: false,
          resolved: false
        });
      }

      if (metricsResult.success && metricsResult.data.memory.usage > 80) {
        alerts.push({
          id: 'memory-warning',
          severity: 'medium',
          title: '内存使用率警告',
          message: `系统内存使用率已达到 ${metricsResult.data.memory.usage.toFixed(1)}%`,
          timestamp: new Date(Date.now() - 15 * 60 * 1000),
          acknowledged: false,
          resolved: false
        });
      }

      if (statsResult.success && statsResult.data.completedBackups24h > 0) {
        alerts.push({
          id: 'backup-success',
          severity: 'low',
          title: '备份任务完成',
          message: `过去24小时内成功完成 ${statsResult.data.completedBackups24h} 个备份任务`,
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
          acknowledged: true,
          resolved: true
        });
      }

      setAlerts(alerts);
      setLoading(false);
    } catch (error) {
      console.error('加载系统数据失败:', error);
      setLoading(false);
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (minutes: number): string => {
    if (minutes < 60) return `${Math.round(minutes)}分钟`;
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    return `${hours}小时${mins}分钟`;
  };

  const getSeverityColor = (severity: string): string => {
    switch (severity) {
      case 'critical': return '#dc3545';
      case 'high': return '#fd7e14';
      case 'medium': return '#ffc107';
      case 'low': return '#28a745';
      default: return '#6c757d';
    }
  };

  const formatUptime = (uptimeMs: number): string => {
    const days = Math.floor(uptimeMs / (1000 * 60 * 60 * 24));
    const hours = Math.floor((uptimeMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((uptimeMs % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) {
      return `${days}天 ${hours}小时`;
    } else if (hours > 0) {
      return `${hours}小时 ${minutes}分钟`;
    } else {
      return `${minutes}分钟`;
    }
  };

  if (loading) {
    return (
      <div className="system-overview-loading">
        <div className="loading-spinner"></div>
        <p>加载系统数据...</p>
      </div>
    );
  }

  return (
    <div className="system-overview">
      <div className="overview-header">
        <div className="system-title">
          <div className="system-info">
            <h1>{systemInfo.productName}</h1>
            <div className="system-details">
              <span className="version">版本 {systemInfo.version}</span>
              <span className="edition">{systemInfo.edition}</span>
              <span className={`status ${systemInfo.systemStatus}`}>
                {systemInfo.systemStatus === 'healthy' ? '🟢 运行正常' :
                 systemInfo.systemStatus === 'warning' ? '🟡 需要注意' : '🔴 需要处理'}
              </span>
            </div>
            <div className="system-stats">
              <span>已连接数据库: {systemInfo.connectedDatabases}</span>
              <span>活跃备份任务: {systemInfo.activeBackupJobs}</span>
              <span>运行时间: {systemMetrics ? formatUptime(systemMetrics.uptime) : '加载中...'}</span>
            </div>
          </div>
        </div>
        <button onClick={loadSystemData} className="refresh-btn">
          🔄 刷新数据
        </button>
      </div>

      <div className="metrics-grid">
        {/* 系统性能指标 */}
        <div className="metric-card">
          <div className="card-header">
            <h3>系统性能</h3>
            <span className="card-icon">📊</span>
          </div>
          <div className="card-content">
            {systemMetrics && (
              <div className="performance-metrics">
                <div className="metric-item">
                  <div className="metric-label">CPU使用率</div>
                  <div className="metric-value">
                    <span className={`metric-number ${systemMetrics.cpu.usage > 80 ? 'high' : ''}`}>
                      {systemMetrics.cpu.usage.toFixed(1)}%
                    </span>
                    <div className="metric-bar">
                      <div 
                        className="metric-fill" 
                        style={{ width: `${systemMetrics.cpu.usage}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
                
                <div className="metric-item">
                  <div className="metric-label">内存使用率</div>
                  <div className="metric-value">
                    <span className={`metric-number ${systemMetrics.memory.usage > 85 ? 'high' : ''}`}>
                      {systemMetrics.memory.usage.toFixed(1)}%
                    </span>
                    <div className="metric-bar">
                      <div 
                        className="metric-fill" 
                        style={{ width: `${systemMetrics.memory.usage}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="metric-detail">
                    {formatBytes(systemMetrics.memory.used)} / {formatBytes(systemMetrics.memory.total)}
                  </div>
                </div>
                
                <div className="metric-item">
                  <div className="metric-label">磁盘使用率</div>
                  <div className="metric-value">
                    <span className={`metric-number ${systemMetrics.disk.usage > 90 ? 'high' : ''}`}>
                      {systemMetrics.disk.usage.toFixed(1)}%
                    </span>
                    <div className="metric-bar">
                      <div 
                        className="metric-fill" 
                        style={{ width: `${systemMetrics.disk.usage}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="metric-detail">
                    {formatBytes(systemMetrics.disk.used)} / {formatBytes(systemMetrics.disk.total)}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 备份状态 */}
        <div className="metric-card">
          <div className="card-header">
            <h3>备份状态</h3>
            <span className="card-icon">💾</span>
          </div>
          <div className="card-content">
            {backupMetrics && (
              <div className="backup-stats">
                <div className="stat-row">
                  <div className="stat-item">
                    <div className="stat-number active">{backupMetrics.activeBackups}</div>
                    <div className="stat-label">正在执行</div>
                  </div>
                  <div className="stat-item">
                    <div className="stat-number queued">{backupMetrics.queuedBackups}</div>
                    <div className="stat-label">队列中</div>
                  </div>
                </div>
                
                <div className="stat-row">
                  <div className="stat-item">
                    <div className="stat-number success">{backupMetrics.completedBackups24h}</div>
                    <div className="stat-label">24h成功</div>
                  </div>
                  <div className="stat-item">
                    <div className="stat-number error">{backupMetrics.failedBackups24h}</div>
                    <div className="stat-label">24h失败</div>
                  </div>
                </div>
                
                <div className="backup-details">
                  <div className="detail-item">
                    <span>平均耗时:</span>
                    <span>{formatDuration(backupMetrics.averageBackupDuration)}</span>
                  </div>
                  <div className="detail-item">
                    <span>总备份大小:</span>
                    <span>{formatBytes(backupMetrics.totalBackupSize)}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 告警信息 */}
        <div className="metric-card alerts-card">
          <div className="card-header">
            <h3>系统告警</h3>
            <span className="card-icon">⚠️</span>
            {alerts.length > 0 && (
              <span className="alert-count">{alerts.length}</span>
            )}
          </div>
          <div className="card-content">
            {alerts.length === 0 ? (
              <div className="no-alerts">
                <span className="no-alerts-icon">✅</span>
                <p>暂无活跃告警</p>
              </div>
            ) : (
              <div className="alerts-list">
                {alerts.map(alert => (
                  <div key={alert.id} className={`alert-item ${alert.severity}`}>
                    <div className="alert-header">
                      <span 
                        className="alert-severity" 
                        style={{ backgroundColor: getSeverityColor(alert.severity) }}
                      >
                        {alert.severity.toUpperCase()}
                      </span>
                      <span className="alert-time">
                        {alert.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                    <div className="alert-title">{alert.title}</div>
                    <div className="alert-message">{alert.message}</div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SystemOverview;
