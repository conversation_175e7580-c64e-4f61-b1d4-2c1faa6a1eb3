<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MySQL 增量备份系统 - 界面预览</title>
    <style>
        :root {
            --primary-color: #667eea;
            --primary-hover: #764ba2;
            --text-primary: #2c3e50;
            --text-secondary: #7f8c8d;
            --border-color: #e0e0e0;
            --bg-primary: #f8f9fa;
            --bg-secondary: #ffffff;
            --bg-hover: #f1f3f4;
            --bg-tertiary: #e8f0fe;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
        }

        .dashboard {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            color: white;
            padding: 20px 32px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            min-height: 80px;
        }

        .app-branding {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .app-title {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .app-subtitle {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 400;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-info {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 2px;
        }

        .welcome-text {
            font-size: 14px;
            color: white;
            font-weight: 500;
        }

        .user-role {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            background: rgba(255, 255, 255, 0.1);
            padding: 2px 8px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .logout-button {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 10px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            backdrop-filter: blur(10px);
        }

        .logout-button:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .dashboard-nav {
            display: flex;
            background: white;
            border-bottom: 1px solid var(--border-color);
            padding: 0 32px;
            overflow-x: auto;
            min-height: 48px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .nav-button {
            background: none;
            border: none;
            padding: 12px 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
            border-bottom: 3px solid transparent;
            transition: all 0.2s ease;
            white-space: nowrap;
            position: relative;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-button:hover {
            color: var(--primary-color);
            background: rgba(102, 126, 234, 0.05);
        }

        .nav-button.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
            background: rgba(102, 126, 234, 0.1);
            font-weight: 600;
        }

        .nav-button::before {
            content: "";
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: transparent;
            transition: all 0.2s ease;
        }

        .nav-button.active::before {
            background: var(--primary-color);
        }

        .system-overview {
            padding: 20px 32px;
            background: var(--bg-primary);
            min-height: calc(100vh - 128px);
        }

        .overview-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 32px;
            padding: 24px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            border-radius: 16px;
            color: white;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .system-title {
            flex: 1;
        }

        .system-info h1 {
            margin: 0 0 8px 0;
            font-size: 32px;
            font-weight: 700;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .system-details {
            display: flex;
            gap: 16px;
            align-items: center;
            margin-bottom: 12px;
            flex-wrap: wrap;
        }

        .system-details .version {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }

        .system-details .edition {
            background: rgba(255, 215, 0, 0.9);
            color: #333;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
        }

        .system-details .status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }

        .system-details .status.healthy {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid rgba(40, 167, 69, 0.5);
        }

        .system-stats {
            display: flex;
            gap: 24px;
            font-size: 14px;
            opacity: 0.9;
            flex-wrap: wrap;
        }

        .system-stats span {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .system-stats span::before {
            content: "•";
            color: rgba(255, 255, 255, 0.6);
        }

        .refresh-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            backdrop-filter: blur(10px);
            white-space: nowrap;
        }

        .refresh-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .demo-note {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 32px;
            color: #1565c0;
            font-size: 14px;
        }

        .demo-note strong {
            color: #0d47a1;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <header class="dashboard-header">
            <div class="header-left">
                <div class="app-branding">
                    <h1 class="app-title">MySQL 增量备份系统</h1>
                    <span class="app-subtitle">企业级数据库备份解决方案 v1.0.0</span>
                </div>
            </div>
            <div class="header-right">
                <div class="user-section">
                    <div class="user-info">
                        <span class="welcome-text">欢迎，管理员</span>
                        <span class="user-role">系统管理员</span>
                    </div>
                    <button class="logout-button">退出登录</button>
                </div>
            </div>
        </header>

        <nav class="dashboard-nav">
            <button class="nav-button active">系统概览</button>
            <button class="nav-button">服务器管理</button>
            <button class="nav-button">备份任务</button>
            <button class="nav-button">恢复管理</button>
            <button class="nav-button">报告生成</button>
            <button class="nav-button">系统设置</button>
        </nav>

        <div class="demo-note">
            <strong>界面优化完成！</strong> 现在用户可以清楚地看到这是"MySQL 增量备份系统"，包含版本信息、系统状态和用户身份。整体设计更加专业和企业级。
        </div>

        <div class="system-overview">
            <div class="overview-header">
                <div class="system-title">
                    <div class="system-info">
                        <h1>MySQL 增量备份系统</h1>
                        <div class="system-details">
                            <span class="version">版本 1.0.0</span>
                            <span class="edition">企业版</span>
                            <span class="status healthy">🟢 运行正常</span>
                        </div>
                        <div class="system-stats">
                            <span>已连接数据库: 3</span>
                            <span>活跃备份任务: 1</span>
                            <span>运行时间: 2天 14小时</span>
                        </div>
                    </div>
                </div>
                <button class="refresh-btn">🔄 刷新数据</button>
            </div>
        </div>
    </div>

    <script>
        // 简单的交互效果
        document.querySelectorAll('.nav-button').forEach(button => {
            button.addEventListener('click', function() {
                document.querySelectorAll('.nav-button').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
            });
        });

        document.querySelector('.refresh-btn').addEventListener('click', function() {
            this.textContent = '🔄 刷新中...';
            setTimeout(() => {
                this.textContent = '🔄 刷新数据';
            }, 1000);
        });
    </script>
</body>
</html>
