{"compilerOptions": {"target": "ES2020", "module": "ES2020", "lib": ["ES2020"], "outDir": "../dist-electron", "rootDir": ".", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "declaration": false, "sourceMap": false, "noUnusedLocals": false, "noUnusedParameters": false}, "include": ["**/*.ts"], "exclude": ["node_modules", "../dist-electron"]}