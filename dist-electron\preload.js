import { ipcRenderer, contextBridge } from 'electron';
// --------- Expose some API to the Renderer process ---------
contextBridge.exposeInMainWorld('electronAPI', {
    // 用户认证相关API
    userRegister: (userData) => ipcRenderer.invoke('user-register', userData),
    userLogin: (credentials) => ipcRenderer.invoke('user-login', credentials),
    // 服务器管理API
    addServer: (serverData) => ipcRenderer.invoke('add-server', serverData),
    getServers: (userId) => ipcRenderer.invoke('get-servers', userId),
    deleteServer: (serverId, userId) => ipcRenderer.invoke('delete-server', { serverId, userId }),
    getServer: (serverId, userId) => ipcRenderer.invoke('get-server', { serverId, userId }),
    updateServer: (serverId, userId, serverData) => ipcRenderer.invoke('update-server', { serverId, userId, serverData }),
    testServerConnection: (serverConfig) => ipcRenderer.invoke('test-server-connection', serverConfig),
    updateServerStatus: (serverId, status) => ipcRenderer.invoke('update-server-status', serverId, status),
    // 备份任务管理API
    createBackupTask: (taskData) => ipcRenderer.invoke('create-backup-task', taskData),
    getBackupTasks: (userId) => ipcRenderer.invoke('get-backup-tasks', userId),
    deleteBackupTask: (taskId, userId) => ipcRenderer.invoke('delete-backup-task', { taskId, userId }),
    updateBackupTaskStatus: (taskId, userId, status) => ipcRenderer.invoke('update-backup-task-status', taskId, userId, status),
    // 备份历史API
    getBackupHistory: (userId, limit) => ipcRenderer.invoke('get-backup-history', userId, limit),
    getBackupTaskHistory: (taskId, userId) => ipcRenderer.invoke('get-backup-task-history', taskId, userId),
    generateCompleteBackupSQL: (taskId, targetHistoryId, userId) => ipcRenderer.invoke('generate-complete-backup-sql', taskId, targetHistoryId, userId),
    executeBackup: (taskId) => ipcRenderer.invoke('execute-backup', taskId),
    downloadBackupFile: (historyId, userId, fileName) => ipcRenderer.invoke('download-backup-file', historyId, userId, fileName),
    getBackupFilePath: (historyId, userId) => ipcRenderer.invoke('get-backup-file-path', historyId, userId),
    showItemInFolder: (filePath) => ipcRenderer.invoke('show-item-in-folder', filePath),
    // 连接测试API
    testDatabaseExists: (serverConfig, databaseName) => ipcRenderer.invoke('test-database-exists', { serverConfig, databaseName }),
    testDatabaseConnection: (serverId, databaseName, userId) => ipcRenderer.invoke('test-database-connection', serverId, databaseName, userId),
    // 系统信息API
    getSystemInfo: (userId) => ipcRenderer.invoke('get-system-info', userId),
    getSystemMetrics: () => ipcRenderer.invoke('get-system-metrics'),
    getBackupStats: (userId) => ipcRenderer.invoke('get-backup-stats', userId),
    // 服务器端备份文件API
    getServerBackupFiles: (serverId, userId) => ipcRenderer.invoke('get-server-backup-files', serverId, userId),
    downloadServerBackupFile: (serverId, filePath, userId) => ipcRenderer.invoke('download-server-backup-file', serverId, filePath, userId),
    // 通用IPC方法
    on(...args) {
        const [channel, listener] = args;
        return ipcRenderer.on(channel, (event, ...args) => listener(event, ...args));
    },
    off(...args) {
        const [channel, ...omit] = args;
        return ipcRenderer.off(channel, ...omit);
    },
    send(...args) {
        const [channel, ...omit] = args;
        return ipcRenderer.send(channel, ...omit);
    },
    invoke(...args) {
        const [channel, ...omit] = args;
        return ipcRenderer.invoke(channel, ...omit);
    }
});
