// 测试下载功能
const { downloadBackupFile } = require('./dist-electron/main.js');

async function testDownload() {
  try {
    console.log('Testing download function...');
    
    // 测试参数
    const historyId = 1; // 假设的历史记录ID
    const userId = 2;    // 假设的用户ID
    const fileName = 'test_backup.sql';
    
    const result = await downloadBackupFile(historyId, userId, fileName);
    console.log('Download result:', result);
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testDownload();
