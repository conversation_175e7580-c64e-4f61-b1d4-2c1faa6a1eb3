/**
 * 企业级日志系统
 * 支持结构化日志、日志轮转、敏感数据脱敏、审计日志
 */
// 条件导入Node.js模块
let fs, path;
if (typeof require !== 'undefined' && typeof process !== 'undefined' && process.versions && process.versions.node) {
    fs = require('fs');
    path = require('path');
}
import { config } from '../config/AppConfig';
export var LogLevel;
(function (LogLevel) {
    LogLevel[LogLevel["DEBUG"] = 0] = "DEBUG";
    LogLevel[LogLevel["INFO"] = 1] = "INFO";
    LogLevel[LogLevel["WARN"] = 2] = "WARN";
    LogLevel[LogLevel["ERROR"] = 3] = "ERROR";
})(LogLevel || (LogLevel = {}));
/**
 * 企业级日志记录器
 */
export class Logger {
    constructor() {
        this.sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
        const logConfig = config.get('logging');
        this.logLevel = this.getLogLevel(logConfig.level);

        // 只在Node.js环境中设置文件路径
        if (path && typeof process !== 'undefined' && process.cwd) {
            this.logDir = path.join(process.cwd(), 'logs');
            this.auditLogDir = path.join(this.logDir, 'audit');
            this.ensureLogDirectories();
        } else {
            // 浏览器环境中使用默认值
            this.logDir = './logs';
            this.auditLogDir = './logs/audit';
        }
    }
    static getInstance() {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }
        return Logger.instance;
    }
    getLogLevel(level) {
        switch (level.toLowerCase()) {
            case 'debug': return LogLevel.DEBUG;
            case 'info': return LogLevel.INFO;
            case 'warn': return LogLevel.WARN;
            case 'error': return LogLevel.ERROR;
            default: return LogLevel.INFO;
        }
    }
    ensureLogDirectories() {
        // 只在Node.js环境中创建目录
        if (fs && fs.existsSync && fs.mkdirSync) {
            if (!fs.existsSync(this.logDir)) {
                fs.mkdirSync(this.logDir, { recursive: true });
            }
            if (!fs.existsSync(this.auditLogDir)) {
                fs.mkdirSync(this.auditLogDir, { recursive: true });
            }
        }
    }
    maskSensitiveData(data) {
        if (!config.get('logging.sensitiveDataMasking')) {
            return data;
        }
        if (typeof data === 'string') {
            return data;
        }
        if (Array.isArray(data)) {
            return data.map(item => this.maskSensitiveData(item));
        }
        if (typeof data === 'object' && data !== null) {
            const masked = { ...data };
            for (const key in masked) {
                if (this.sensitiveFields.some(field => key.toLowerCase().includes(field))) {
                    masked[key] = '***MASKED***';
                }
                else if (typeof masked[key] === 'object') {
                    masked[key] = this.maskSensitiveData(masked[key]);
                }
            }
            return masked;
        }
        return data;
    }
    formatLogEntry(entry) {
        const maskedEntry = this.maskSensitiveData(entry);
        return JSON.stringify(maskedEntry) + '\n';
    }
    writeToFile(filename, content) {
        // 只在Node.js环境中写入文件
        if (fs && fs.appendFileSync && path && path.join) {
            const filePath = path.join(this.logDir, filename);
            try {
                fs.appendFileSync(filePath, content);
                this.rotateLogIfNeeded(filePath);
            }
            catch (error) {
                console.error('Failed to write to log file:', error);
            }
        }
    }
    rotateLogIfNeeded(filePath) {
        // 只在Node.js环境中进行日志轮转
        if (!fs || !fs.statSync || !path) {
            return;
        }

        try {
            const stats = fs.statSync(filePath);
            const maxSize = config.get('logging.maxFileSize') * 1024 * 1024; // Convert MB to bytes
            if (stats.size > maxSize) {
                const maxFiles = config.get('logging.maxFiles');
                const dir = path.dirname(filePath);
                const ext = path.extname(filePath);
                const basename = path.basename(filePath, ext);
                // Rotate existing files
                for (let i = maxFiles - 1; i > 0; i--) {
                    const oldFile = path.join(dir, `${basename}.${i}${ext}`);
                    const newFile = path.join(dir, `${basename}.${i + 1}${ext}`);
                    if (fs.existsSync(oldFile)) {
                        if (i === maxFiles - 1) {
                            fs.unlinkSync(oldFile); // Delete oldest file
                        }
                        else {
                            fs.renameSync(oldFile, newFile);
                        }
                    }
                }
                // Move current file to .1
                const rotatedFile = path.join(dir, `${basename}.1${ext}`);
                fs.renameSync(filePath, rotatedFile);
            }
        }
        catch (error) {
            console.error('Failed to rotate log file:', error);
        }
    }
    log(level, message, metadata, module) {
        if (level < this.logLevel) {
            return;
        }
        const entry = {
            timestamp: new Date().toISOString(),
            level: LogLevel[level],
            message,
            module,
            metadata: metadata ? this.maskSensitiveData(metadata) : undefined
        };
        const logContent = this.formatLogEntry(entry);
        // Write to console in development
        if (config.get('environment') === 'development') {
            console.log(logContent.trim());
        }
        // Write to file
        const today = new Date().toISOString().split('T')[0];
        this.writeToFile(`app-${today}.log`, logContent);
        // Write errors to separate file
        if (level === LogLevel.ERROR) {
            this.writeToFile(`error-${today}.log`, logContent);
        }
    }
    debug(message, metadata, module) {
        this.log(LogLevel.DEBUG, message, metadata, module);
    }
    info(message, metadata, module) {
        this.log(LogLevel.INFO, message, metadata, module);
    }
    warn(message, metadata, module) {
        this.log(LogLevel.WARN, message, metadata, module);
    }
    error(message, error, metadata, module) {
        const logMetadata = {
            ...metadata,
            stack: error?.stack,
            errorMessage: error?.message
        };
        this.log(LogLevel.ERROR, message, logMetadata, module);
    }
    /**
     * 审计日志记录
     */
    audit(entry) {
        if (!config.get('logging.auditEnabled')) {
            return;
        }
        const auditEntry = {
            ...entry,
            timestamp: new Date().toISOString(),
            level: 'AUDIT'
        };
        const logContent = this.formatLogEntry(auditEntry);
        const today = new Date().toISOString().split('T')[0];
        const auditFilePath = path.join(this.auditLogDir, `audit-${today}.log`);
        try {
            fs.appendFileSync(auditFilePath, logContent);
        }
        catch (error) {
            this.error('Failed to write audit log', error, { auditEntry });
        }
    }
    /**
     * 性能日志记录
     */
    performance(operation, duration, metadata) {
        this.info(`Performance: ${operation}`, {
            duration,
            operation,
            ...metadata
        }, 'PERFORMANCE');
    }
    /**
     * 安全事件日志记录
     */
    security(event, severity, metadata) {
        this.warn(`Security Event: ${event}`, {
            severity,
            event,
            ...metadata
        }, 'SECURITY');
    }
    /**
     * 清理过期日志文件
     */
    cleanupOldLogs() {
        const retentionDays = config.get('logging.logRetentionDays');
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
        const cleanupDirectory = (dir) => {
            try {
                const files = fs.readdirSync(dir);
                for (const file of files) {
                    const filePath = path.join(dir, file);
                    const stats = fs.statSync(filePath);
                    if (stats.mtime < cutoffDate) {
                        fs.unlinkSync(filePath);
                        this.info(`Cleaned up old log file: ${file}`);
                    }
                }
            }
            catch (error) {
                this.error('Failed to cleanup old logs', error, { directory: dir });
            }
        };
        cleanupDirectory(this.logDir);
        cleanupDirectory(this.auditLogDir);
    }
}
// 导出单例实例
export const logger = Logger.getInstance();
// 导出便捷函数
export const log = {
    debug: (message, metadata, module) => logger.debug(message, metadata, module),
    info: (message, metadata, module) => logger.info(message, metadata, module),
    warn: (message, metadata, module) => logger.warn(message, metadata, module),
    error: (message, error, metadata, module) => logger.error(message, error, metadata, module),
    audit: (entry) => logger.audit(entry),
    performance: (operation, duration, metadata) => logger.performance(operation, duration, metadata),
    security: (event, severity, metadata) => logger.security(event, severity, metadata)
};
