<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg5" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0F172A;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E293B;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="minimal5" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#60A5FA;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆角矩形 -->
  <rect width="256" height="256" rx="48" ry="48" fill="url(#bg5)"/>
  
  <!-- 极简设计 -->
  <g transform="translate(128, 128)" stroke-linecap="round" stroke-linejoin="round">
    
    <!-- 数据库轮廓 -->
    <g fill="none" stroke="url(#minimal5)" stroke-width="6">
      <!-- 数据库顶部 -->
      <ellipse cx="0" cy="-30" rx="40" ry="10"/>
      
      <!-- 数据库侧面 -->
      <line x1="-40" y1="-30" x2="-40" y2="30"/>
      <line x1="40" y1="-30" x2="40" y2="30"/>
      
      <!-- 数据库底部 -->
      <ellipse cx="0" cy="30" rx="40" ry="10"/>
      
      <!-- 数据库分层 -->
      <ellipse cx="0" cy="-10" rx="40" ry="10" opacity="0.6"/>
      <ellipse cx="0" cy="10" rx="40" ry="10" opacity="0.6"/>
    </g>
    
    <!-- 增量指示线 -->
    <g stroke="#10B981" stroke-width="4" fill="none">
      <!-- 主箭头 -->
      <path d="M 60 -20 L 60 -40 M 50 -30 L 60 -40 L 70 -30"/>
      
      <!-- 增量线条 -->
      <line x1="55" y1="-15" x2="65" y2="-15" opacity="0.8"/>
      <line x1="57" y1="-10" x2="63" y2="-10" opacity="0.6"/>
      <line x1="59" y1="-5" x2="61" y2="-5" opacity="0.4"/>
    </g>
    
    <!-- 连接线 -->
    <line x1="40" y1="0" x2="50" y2="-15" stroke="#60A5FA" stroke-width="2" 
          stroke-dasharray="4,4" opacity="0.5"/>
    
    <!-- MySQL标识 -->
    <g fill="#FFFFFF" font-family="Arial, sans-serif">
      <text x="0" y="55" text-anchor="middle" font-size="14" font-weight="300">MySQL</text>
      <text x="0" y="70" text-anchor="middle" font-size="10" opacity="0.7">BACKUP</text>
    </g>
    
    <!-- 装饰点 -->
    <g fill="#60A5FA" opacity="0.3">
      <circle cx="-60" cy="-60" r="2"/>
      <circle cx="60" cy="60" r="2"/>
      <circle cx="-60" cy="60" r="1.5"/>
      <circle cx="70" cy="-50" r="1"/>
    </g>
    
  </g>
</svg>
