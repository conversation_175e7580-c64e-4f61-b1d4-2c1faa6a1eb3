/* 报告生成器样式 */

.report-generator {
  padding: 24px;
  background: var(--background-color);
  min-height: 100vh;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-color);
}

.report-header h2 {
  margin: 0;
  color: var(--text-primary);
  font-size: 28px;
  font-weight: 600;
}

.report-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.report-section h3 {
  margin: 0 0 20px 0;
  color: var(--text-primary);
  font-size: 20px;
  font-weight: 600;
}

/* 模板网格 */
.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.template-card {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.2s ease;
  box-shadow: var(--card-shadow);
}

.template-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--card-shadow-hover);
}

.template-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.template-icon {
  font-size: 24px;
}

.template-header h4 {
  margin: 0;
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
}

.template-description {
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
}

.template-info {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.template-format {
  background: var(--primary-color);
  color: white;
  font-size: 11px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
  letter-spacing: 0.5px;
}

.template-scheduled {
  background: var(--success-color);
  color: white;
  font-size: 11px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
}

.template-actions {
  display: flex;
  gap: 8px;
}

/* 报告表格 */
.reports-table {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr 1fr;
  gap: 16px;
  padding: 16px 20px;
  background: var(--background-color);
  border-bottom: 1px solid var(--border-color);
  font-weight: 600;
  color: var(--text-secondary);
  font-size: 14px;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr 1fr;
  gap: 16px;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background: var(--background-color);
}

.table-row:last-child {
  border-bottom: none;
}

.col-name {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-primary);
  font-weight: 500;
}

.report-icon {
  font-size: 16px;
}

.col-status .status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.generating {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.status-badge.completed {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.status-badge.failed {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.col-size,
.col-date {
  color: var(--text-secondary);
  font-size: 14px;
}

.col-actions {
  display: flex;
  gap: 8px;
}

/* 按钮样式 */
.btn-primary {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-hover);
  transform: translateY(-1px);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background: var(--background-color);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: var(--border-color);
  transform: translateY(-1px);
}

.btn-link {
  background: none;
  color: var(--primary-color);
  border: none;
  padding: 4px 8px;
  font-size: 14px;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.btn-link:hover {
  color: var(--primary-hover);
}

.btn-link.danger {
  color: var(--error-color);
}

.btn-link.danger:hover {
  color: #c82333;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: var(--card-background);
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: var(--border-color);
  color: var(--text-primary);
}

.modal-body {
  padding: 24px;
  max-height: 400px;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid var(--border-color);
  background: var(--background-color);
}

/* 表单样式 */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: var(--text-primary);
  font-weight: 500;
  font-size: 14px;
}

.form-group input[type="date"],
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--card-background);
  color: var(--text-primary);
  font-size: 14px;
}

.form-group input[type="checkbox"] {
  margin-right: 8px;
}

.date-range {
  display: flex;
  align-items: center;
  gap: 12px;
}

.date-range input {
  flex: 1;
}

.date-range span {
  color: var(--text-secondary);
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .report-generator {
    padding: 16px;
  }
  
  .report-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .template-grid {
    grid-template-columns: 1fr;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .table-header {
    display: none;
  }
  
  .table-row {
    display: flex;
    flex-direction: column;
    padding: 16px;
  }
  
  .col-name::before {
    content: "名称: ";
    font-weight: 600;
    color: var(--text-secondary);
  }
  
  .col-status::before {
    content: "状态: ";
    font-weight: 600;
    color: var(--text-secondary);
  }
  
  .col-size::before {
    content: "大小: ";
    font-weight: 600;
    color: var(--text-secondary);
  }
  
  .col-date::before {
    content: "时间: ";
    font-weight: 600;
    color: var(--text-secondary);
  }
  
  .modal-content {
    width: 95%;
    margin: 20px;
  }
  
  .date-range {
    flex-direction: column;
    align-items: stretch;
  }
}
