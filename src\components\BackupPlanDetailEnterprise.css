/* 企业级备份计划详情页面样式 */
.backup-detail-enterprise {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Roboto', sans-serif;
  padding: 0;
  margin: 0;
}

/* 加载和错误状态 */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  padding: 40px;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #e3e3e3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  margin: 40px;
  max-width: 500px;
  margin: 40px auto;
}

.error-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.error-actions {
  display: flex;
  gap: 16px;
  margin-top: 24px;
  justify-content: center;
}

/* 页面头部 */
.detail-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  opacity: 0.9;
}

.separator {
  opacity: 0.6;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.auto-refresh-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  opacity: 0.9;
}

.auto-refresh-toggle input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: white;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.btn-ghost {
  background: transparent;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-ghost:hover {
  background: rgba(255, 255, 255, 0.1);
}

.btn-sm {
  padding: 8px 16px;
  font-size: 12px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn.loading {
  opacity: 0.8;
  cursor: wait;
}

.icon {
  font-size: 16px;
}

/* 任务概览卡片 */
.task-overview-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  margin: 32px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-header {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  padding: 24px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.status-badge {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.active {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.status-badge.inactive {
  background: rgba(158, 158, 158, 0.2);
  color: #9e9e9e;
  border: 1px solid rgba(158, 158, 158, 0.3);
}

.card-content {
  padding: 32px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.overview-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.overview-item .label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.overview-item .value {
  font-size: 18px;
  color: #1f2937;
  font-weight: 600;
}

.next-backup {
  color: #667eea !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 统计信息网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
  margin: 0 32px 32px 32px;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.success::before {
  background: linear-gradient(135deg, #4caf50 0%, #8bc34a 100%);
}

.stat-card.error::before {
  background: linear-gradient(135deg, #f44336 0%, #e91e63 100%);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  font-size: 32px;
  opacity: 0.8;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 控制面板 */
.controls-panel {
  background: white;
  border-radius: 16px;
  padding: 24px 32px;
  margin: 0 32px 32px 32px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.controls-left, .controls-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.time-range-selector {
  display: flex;
  align-items: center;
  gap: 12px;
}

.time-range-selector label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.select {
  padding: 8px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.view-mode-toggle {
  display: flex;
  background: #f3f4f6;
  border-radius: 10px;
  padding: 4px;
  gap: 4px;
}

.view-mode-toggle .btn {
  padding: 8px 16px;
  margin: 0;
  border-radius: 6px;
  font-size: 13px;
}

.view-mode-toggle .btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.view-mode-toggle .btn-secondary {
  background: transparent;
  color: #6b7280;
  border: none;
  box-shadow: none;
}

.view-mode-toggle .btn-secondary:hover {
  background: rgba(107, 114, 128, 0.1);
  color: #374151;
}

/* 备份历史部分 */
.backup-history-section {
  background: white;
  border-radius: 16px;
  margin: 0 32px 32px 32px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.section-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.history-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 40px;
  color: #6b7280;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-state h4 {
  margin: 0 0 12px 0;
  font-size: 20px;
  color: #374151;
}

.empty-state p {
  margin: 0;
  font-size: 16px;
}

/* 时间线视图 */
.timeline-container {
  padding: 32px;
  max-height: 800px;
  overflow-y: auto;
}

.timeline {
  position: relative;
}

.timeline-item {
  display: flex;
  margin-bottom: 32px;
  position: relative;
}

.timeline-marker {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 24px;
  position: relative;
}

.marker-dot {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 2;
}

.marker-dot.completed {
  background: linear-gradient(135deg, #4caf50 0%, #8bc34a 100%);
  color: white;
}

.marker-dot.failed {
  background: linear-gradient(135deg, #f44336 0%, #e91e63 100%);
  color: white;
}

.marker-dot.running {
  background: linear-gradient(135deg, #ff9800 0%, #ffc107 100%);
  color: white;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.timeline-line {
  width: 3px;
  height: 60px;
  background: linear-gradient(180deg, #e5e7eb 0%, #d1d5db 100%);
  margin-top: 8px;
  border-radius: 2px;
}

.timeline-content {
  flex: 1;
  margin-top: -8px;
}

.timeline-card {
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.3s ease;
}

.timeline-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.timeline-card .card-header {
  background: white;
  color: #374151;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.backup-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.backup-type {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  width: fit-content;
}

.backup-type.full {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.backup-type.incremental {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
}

.backup-time {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.backup-actions {
  display: flex;
  gap: 8px;
}

.timeline-card .card-content {
  padding: 20px 24px;
}

.backup-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item.error {
  grid-column: 1 / -1;
}

.detail-item .label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-item .value {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.detail-item .error-message {
  color: #dc2626;
  background: #fef2f2;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #fecaca;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

/* 表格视图 */
.table-container {
  overflow-x: auto;
}

.backup-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.backup-table th {
  background: #f9fafb;
  color: #374151;
  font-weight: 600;
  padding: 16px 20px;
  text-align: left;
  border-bottom: 2px solid #e5e7eb;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.backup-table td {
  padding: 16px 20px;
  border-bottom: 1px solid #f3f4f6;
  vertical-align: middle;
}

.backup-table tr:hover {
  background: #f9fafb;
}

.backup-type-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.backup-type-badge.full {
  background: #dbeafe;
  color: #1d4ed8;
}

.backup-type-badge.incremental {
  background: #ede9fe;
  color: #7c3aed;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }

  .overview-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .backup-detail-enterprise {
    padding: 0;
  }

  .detail-header {
    padding: 16px 20px;
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .header-left {
    flex-direction: column;
    gap: 12px;
  }

  .task-overview-card,
  .backup-history-section,
  .controls-panel {
    margin: 16px;
    border-radius: 12px;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    margin: 0 16px 16px 16px;
    gap: 16px;
  }

  .controls-panel {
    flex-direction: column;
    gap: 16px;
  }

  .timeline-container {
    padding: 20px;
  }

  .timeline-item {
    margin-bottom: 24px;
  }

  .marker-dot {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .backup-details {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .backup-table {
    font-size: 12px;
  }

  .backup-table th,
  .backup-table td {
    padding: 12px 16px;
  }
}

@media (max-width: 480px) {
  .task-overview-card,
  .backup-history-section,
  .controls-panel {
    margin: 12px;
  }

  .stats-grid {
    margin: 0 12px 12px 12px;
    grid-template-columns: 1fr 1fr;
  }

  .card-content,
  .timeline-container {
    padding: 16px;
  }

  .section-header {
    padding: 16px 20px;
  }

  .timeline-card .card-header,
  .timeline-card .card-content {
    padding: 16px;
  }
}
