<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E3A8A;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="db1" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#60A5FA;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2563EB;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="arrow1" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#34D399;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆角矩形 -->
  <rect width="256" height="256" rx="48" ry="48" fill="url(#bg1)"/>
  
  <!-- 数据库主体 -->
  <g transform="translate(128, 128)">
    <!-- 数据库顶部 -->
    <ellipse cx="0" cy="-40" rx="50" ry="12" fill="#E0F2FE" stroke="#0284C7" stroke-width="2"/>
    
    <!-- 数据库主体 -->
    <rect x="-50" y="-40" width="100" height="80" fill="url(#db1)" stroke="#0284C7" stroke-width="2"/>
    
    <!-- 数据库底部 -->
    <ellipse cx="0" cy="40" rx="50" ry="12" fill="#BAE6FD" stroke="#0284C7" stroke-width="2"/>
    
    <!-- 数据库分层 -->
    <ellipse cx="0" cy="-15" rx="50" ry="12" fill="none" stroke="#E0F2FE" stroke-width="1.5" opacity="0.7"/>
    <ellipse cx="0" cy="10" rx="50" ry="12" fill="none" stroke="#E0F2FE" stroke-width="1.5" opacity="0.7"/>
    
    <!-- 增量箭头组 -->
    <g transform="translate(70, -20)">
      <!-- 大箭头 -->
      <path d="M 0 20 L 15 0 L 30 20 L 22 20 L 22 40 L 8 40 L 8 20 Z" 
            fill="url(#arrow1)" stroke="#047857" stroke-width="2"/>
      
      <!-- 小箭头1 -->
      <path d="M 5 30 L 12 20 L 19 30 L 16 30 L 16 35 L 8 35 L 8 30 Z" 
            fill="#6EE7B7" opacity="0.8"/>
      
      <!-- 小箭头2 -->
      <path d="M 11 35 L 15 28 L 19 35 L 17 35 L 17 38 L 13 38 L 13 35 Z" 
            fill="#A7F3D0" opacity="0.6"/>
    </g>
    
    <!-- MySQL文字 -->
    <text x="0" y="65" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="16" font-weight="bold" fill="#FFFFFF">MySQL</text>
  </g>
</svg>
