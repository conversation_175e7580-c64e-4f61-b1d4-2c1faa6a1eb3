.backup-chain-visualization {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.visualization-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e9ecef;
}

.visualization-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
}

.legend {
  display: flex;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #6c757d;
}

.legend-circle {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid;
}

.legend-circle.full {
  background: #007bff;
  border-color: #007bff;
}

.legend-circle.incremental {
  background: #28a745;
  border-color: #28a745;
}

.visualization-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 24px;
}

.chain-diagram {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  min-height: 300px;
  border: 1px solid #e9ecef;
}

.chain-diagram svg {
  width: 100%;
  height: 100%;
}

/* SVG节点样式 */
.backup-node {
  cursor: pointer;
  transition: all 0.3s ease;
}

.backup-node:hover {
  transform: scale(1.1);
}

.backup-node circle.node-full {
  fill: #007bff;
  stroke: #0056b3;
  stroke-width: 3;
}

.backup-node circle.node-incremental {
  fill: #28a745;
  stroke: #1e7e34;
  stroke-width: 3;
}

.backup-node circle.node-completed {
  opacity: 1;
}

.backup-node circle.node-failed {
  fill: #dc3545;
  stroke: #c82333;
}

.backup-node circle.node-running {
  fill: #ffc107;
  stroke: #e0a800;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.node-label {
  fill: white;
  font-size: 14px;
  font-weight: bold;
  pointer-events: none;
}

.time-label {
  fill: #6c757d;
  font-size: 12px;
  pointer-events: none;
}

.node-details {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e9ecef;
}

.node-details h4 {
  margin: 0 0 16px 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
  border-bottom: 2px solid #007bff;
  padding-bottom: 8px;
}

.detail-grid {
  display: grid;
  gap: 12px;
}

.detail-item {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 8px;
  align-items: center;
}

.detail-item .label {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
}

.detail-item .value {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 600;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

.node-actions {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #dee2e6;
}

.node-actions .btn {
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.node-actions .btn-primary {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
}

.node-actions .btn-primary:hover {
  background: linear-gradient(135deg, #0056b3, #004085);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .visualization-content {
    grid-template-columns: 1fr;
  }
  
  .visualization-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .legend {
    flex-wrap: wrap;
  }
  
  .chain-diagram {
    min-height: 250px;
  }
}

/* 空状态样式 */
.empty-chain {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6c757d;
  font-size: 16px;
}

.empty-chain .empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

/* 加载状态 */
.loading-chain {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6c757d;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 工具提示 */
.node-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  pointer-events: none;
  z-index: 1000;
  white-space: nowrap;
}

/* 连接线动画 */
.dependency-line {
  stroke-dasharray: 5,5;
  animation: dash 1s linear infinite;
}

@keyframes dash {
  to {
    stroke-dashoffset: -10;
  }
}

/* 选中状态 */
.backup-node.selected circle {
  stroke-width: 5;
  filter: drop-shadow(0 0 8px rgba(0, 123, 255, 0.6));
}

/* 错误状态 */
.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #f5c6cb;
  margin-bottom: 16px;
}

.error-message .error-icon {
  margin-right: 8px;
}
