import React, { useState, useEffect } from 'react';
import './BackupStatusMonitor.css';

interface BackupTask {
  id: number;
  name: string;
  database_name: string;
  backup_type: 'full' | 'incremental';
  schedule_type: 'manual' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'custom';
  custom_interval_minutes?: number;
  status: 'active' | 'inactive' | 'running' | 'error';
  last_backup_time?: string;
  server_name: string;
  server_host: string;
}

interface BackupStatusMonitorProps {
  userId: number;
  tasks: BackupTask[];
  onRefresh: () => void;
}

const BackupStatusMonitor: React.FC<BackupStatusMonitorProps> = ({ 
  userId, 
  tasks, 
  onRefresh 
}) => {
  const [realtimeStats, setRealtimeStats] = useState({
    activeTasks: 0,
    runningTasks: 0,
    lastBackupTime: null as string | null,
    nextScheduledBackup: null as string | null,
    totalBackupsToday: 0
  });

  // 计算实时统计信息
  const calculateStats = () => {
    const activeTasks = tasks.filter(task => task.status === 'active').length;
    const runningTasks = tasks.filter(task => task.status === 'running').length;
    
    // 找到最近的备份时间
    const lastBackupTimes = tasks
      .filter(task => task.last_backup_time)
      .map(task => new Date(task.last_backup_time!))
      .sort((a, b) => b.getTime() - a.getTime());
    
    const lastBackupTime = lastBackupTimes.length > 0
      ? lastBackupTimes[0].toLocaleString('zh-CN', {
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      : null;

    // 计算下一个预定备份时间（简化版本）
    const nextScheduledBackup = calculateNextScheduledBackup();

    setRealtimeStats({
      activeTasks,
      runningTasks,
      lastBackupTime,
      nextScheduledBackup,
      totalBackupsToday: 0 // 这里可以从API获取今日备份总数
    });
  };

  // 计算下一个预定备份时间
  const calculateNextScheduledBackup = (): string | null => {
    const now = new Date();
    let nextBackup: Date | null = null;

    for (const task of tasks) {
      if (task.status !== 'active') continue;

      let taskNextBackup: Date | null = null;

      switch (task.schedule_type) {
        case 'custom':
          if (task.custom_interval_minutes && task.last_backup_time) {
            const lastBackup = new Date(task.last_backup_time);
            taskNextBackup = new Date(lastBackup.getTime() + task.custom_interval_minutes * 60 * 1000);
          } else if (task.custom_interval_minutes) {
            // 如果从未备份过，下一次备份时间就是现在
            taskNextBackup = now;
          }
          break;
        case 'hourly':
          taskNextBackup = new Date(now.getTime() + 60 * 60 * 1000); // 下一小时
          break;
        case 'daily':
          taskNextBackup = new Date(now);
          taskNextBackup.setDate(taskNextBackup.getDate() + 1);
          taskNextBackup.setHours(0, 0, 0, 0);
          break;
        // 可以添加更多调度类型的计算
      }

      if (taskNextBackup && (!nextBackup || taskNextBackup < nextBackup)) {
        nextBackup = taskNextBackup;
      }
    }

    return nextBackup ? nextBackup.toLocaleString('zh-CN') : null;
  };

  // 获取任务状态的显示文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '运行中';
      case 'inactive': return '已停用';
      case 'running': return '备份中';
      case 'error': return '错误';
      default: return '未知';
    }
  };

  // 获取任务状态的样式类
  const getStatusClass = (status: string) => {
    switch (status) {
      case 'active': return 'status-active';
      case 'inactive': return 'status-inactive';
      case 'running': return 'status-running';
      case 'error': return 'status-error';
      default: return 'status-unknown';
    }
  };

  // 获取调度类型的显示文本
  const getScheduleText = (task: BackupTask) => {
    switch (task.schedule_type) {
      case 'manual': return '手动执行';
      case 'hourly': return '每小时';
      case 'daily': return '每日';
      case 'weekly': return '每周';
      case 'monthly': return '每月';
      case 'custom': 
        return task.custom_interval_minutes 
          ? `每${task.custom_interval_minutes}分钟` 
          : '自定义';
      default: return '未知';
    }
  };

  // 格式化时间显示
  const formatTimeAgo = (timeString: string | null) => {
    if (!timeString) return '从未';
    
    const time = new Date(timeString);
    const now = new Date();
    const diffMs = now.getTime() - time.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return '刚刚';
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    return `${diffDays}天前`;
  };

  useEffect(() => {
    calculateStats();
    
    // 每30秒更新一次统计信息
    const interval = setInterval(calculateStats, 30000);
    
    return () => clearInterval(interval);
  }, [tasks]);

  return (
    <div className="backup-status-monitor">
      <div className="monitor-header">
        <h3>备份状态监控</h3>
        <button onClick={onRefresh} className="refresh-button">
          🔄 刷新
        </button>
      </div>

      {/* 实时统计卡片 */}
      <div className="stats-cards">
        <div className="stat-card active">
          <div className="stat-icon">🟢</div>
          <div className="stat-content">
            <div className="stat-number">{realtimeStats.activeTasks}</div>
            <div className="stat-label">活跃任务</div>
          </div>
        </div>

        <div className="stat-card running">
          <div className="stat-icon">🔄</div>
          <div className="stat-content">
            <div className="stat-number">{realtimeStats.runningTasks}</div>
            <div className="stat-label">正在备份</div>
          </div>
        </div>

        <div className="stat-card time">
          <div className="stat-icon">⏰</div>
          <div className="stat-content">
            <div className="stat-text">
              {realtimeStats.lastBackupTime ?
                realtimeStats.lastBackupTime.length > 20 ?
                  realtimeStats.lastBackupTime.substring(0, 16) + '...' :
                  realtimeStats.lastBackupTime
                : '从未'}
            </div>
            <div className="stat-label">最近备份</div>
          </div>
        </div>

        <div className="stat-card next">
          <div className="stat-icon">📅</div>
          <div className="stat-content">
            <div className="stat-text">
              {realtimeStats.nextScheduledBackup ?
                realtimeStats.nextScheduledBackup.length > 20 ?
                  realtimeStats.nextScheduledBackup.substring(0, 16) + '...' :
                  realtimeStats.nextScheduledBackup
                : '无计划'}
            </div>
            <div className="stat-label">下次备份</div>
          </div>
        </div>
      </div>

      {/* 任务状态列表 */}
      <div className="task-status-list">
        <h4>任务状态详情</h4>
        {tasks.length === 0 ? (
          <div className="empty-state">
            <p>暂无备份任务</p>
          </div>
        ) : (
          <div className="task-items">
            {tasks.map(task => (
              <div key={task.id} className={`task-status-item ${getStatusClass(task.status)}`}>
                <div className="task-info">
                  <div className="task-name">{task.name}</div>
                  <div className="task-details">
                    {task.server_name} • {task.database_name} • {getScheduleText(task)}
                  </div>
                </div>
                <div className="task-status">
                  <span className={`status-badge ${getStatusClass(task.status)}`}>
                    {getStatusText(task.status)}
                  </span>
                  <div className="last-backup">
                    {formatTimeAgo(task.last_backup_time)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default BackupStatusMonitor;
