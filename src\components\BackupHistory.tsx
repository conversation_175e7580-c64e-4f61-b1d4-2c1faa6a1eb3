import React, { useState, useEffect } from 'react';
import './BackupHistory.css';

interface BackupHistoryItem {
  id: number;
  backup_type: 'full' | 'incremental';
  file_path: string;
  file_size: number;
  start_time: string;
  end_time?: string;
  status: 'running' | 'completed' | 'failed';
  error_message?: string;
  binlog_file?: string;
  binlog_position?: number;
  task_name: string;
  server_name: string;
  server_host: string;
  created_at: string;
}

interface BackupHistoryProps {
  userId: number;
}

const BackupHistory: React.FC<BackupHistoryProps> = ({ userId }) => {
  const [history, setHistory] = useState<BackupHistoryItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [limit, setLimit] = useState(50);
  const [downloading, setDownloading] = useState<number | null>(null);

  // 加载备份历史
  const loadHistory = async () => {
    try {
      setLoading(true);
      setError('');
      const result = await window.electronAPI.getBackupHistory(userId, limit);
      if (result.success) {
        setHistory(result.history || []);
      } else {
        setError(result.message || '获取备份历史失败');
      }
    } catch (error) {
      console.error('获取备份历史失败:', error);
      setError('获取备份历史失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadHistory();
  }, [userId, limit]);

  // 下载备份文件
  const handleDownload = async (historyId: number, fileName: string) => {
    try {
      setDownloading(historyId);
      setError('');

      console.log('开始下载备份文件:', { historyId, fileName, userId });

      const result = await window.electronAPI.getBackupFilePath(historyId, userId);
      console.log('获取文件路径结果:', result);

      if (result.success && result.filePath) {
        // 使用Electron的shell模块打开文件所在目录
        const showResult = await window.electronAPI.showItemInFolder(result.filePath);
        console.log('打开文件夹结果:', showResult);
        
        if (showResult.success) {
          // 显示成功消息
          const successMsg = `文件位置已打开: ${result.filePath}`;
          console.log(successMsg);
          setError(''); // 清除错误
        } else {
          setError(showResult.message || '无法打开文件所在目录');
        }
      } else {
        setError(result.message || '获取备份文件失败');
      }
    } catch (error: any) {
      console.error('下载备份文件失败:', error);
      setError(`下载备份文件失败: ${error.message || error.toString()}`);
    } finally {
      setDownloading(null);
    }
  };

  const formatFileSize = (bytes: number | null | undefined) => {
    if (!bytes || bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (startTime: string, endTime?: string) => {
    if (!endTime) return '进行中...';
    
    try {
      const start = new Date(startTime);
      const end = new Date(endTime);
      
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return '无效时间';
      }
      
      const duration = end.getTime() - start.getTime();
      
      if (duration < 0) return '时间错误';
      
      const seconds = Math.floor(duration / 1000);
      const minutes = Math.floor(seconds / 60);
      const hours = Math.floor(minutes / 60);
      
      if (hours > 0) {
        return `${hours}小时${minutes % 60}分钟`;
      } else if (minutes > 0) {
        return `${minutes}分钟${seconds % 60}秒`;
      } else {
        return `${seconds}秒`;
      }
    } catch (error) {
      console.error('时间格式化错误:', error);
      return '时间错误';
    }
  };

  const formatDateTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return '无效日期';
      }
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (error) {
      console.error('日期格式化错误:', error);
      return '日期错误';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return '已完成';
      case 'failed': return '失败';
      case 'running': return '运行中';
      default: return '未知';
    }
  };

  const getStatusClass = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'failed': return 'error';
      case 'running': return 'warning';
      default: return 'secondary';
    }
  };

  return (
    <div className="backup-history">
      <div className="section-header">
        <h3>备份历史</h3>
        <div className="controls">
          <select 
            value={limit} 
            onChange={(e) => setLimit(parseInt(e.target.value))}
            className="limit-select"
          >
            <option value={20}>显示20条</option>
            <option value={50}>显示50条</option>
            <option value={100}>显示100条</option>
          </select>
          <button 
            className="secondary-button"
            onClick={loadHistory}
            disabled={loading}
          >
            刷新
          </button>
        </div>
      </div>

      {error && <div className="error-message">{error}</div>}

      <div className="history-content">
        {loading ? (
          <div className="loading">加载中...</div>
        ) : history.length === 0 ? (
          <div className="empty-state">
            <p>还没有备份历史记录</p>
            <p>创建备份任务并执行后，历史记录将显示在这里</p>
          </div>
        ) : (
          <div className="history-table">
            <div className="table-header">
              <div className="col-task">任务名称</div>
              <div className="col-server">服务器</div>
              <div className="col-type">类型</div>
              <div className="col-size">大小</div>
              <div className="col-duration">耗时</div>
              <div className="col-status">状态</div>
              <div className="col-time">开始时间</div>
              <div className="col-actions">操作</div>
            </div>
            
            <div className="table-body">
              {history.map(item => (
                <div key={item.id} className="table-row">
                  <div className="col-task">
                    <div className="task-info">
                      <span className="task-name">{item.task_name}</span>
                      <span className="file-path">{item.file_path}</span>
                    </div>
                  </div>
                  
                  <div className="col-server">
                    <div className="server-info">
                      <span className="server-name">{item.server_name}</span>
                      <span className="server-host">{item.server_host}</span>
                    </div>
                  </div>
                  
                  <div className="col-type">
                    <span className={`backup-type ${item.backup_type}`}>
                      {item.backup_type === 'full' ? '完整' : '增量'}
                    </span>
                  </div>
                  
                  <div className="col-size">
                    {formatFileSize(item.file_size)}
                  </div>
                  
                  <div className="col-duration">
                    {formatDuration(item.start_time, item.end_time)}
                  </div>
                  
                  <div className="col-status">
                    <span className={`status-badge ${getStatusClass(item.status)}`}>
                      {getStatusText(item.status)}
                    </span>
                    {item.status === 'failed' && item.error_message && (
                      <div className="error-tooltip" title={item.error_message}>
                        ⚠️
                      </div>
                    )}
                  </div>
                  
                  <div className="col-time">
                    <div className="time-info">
                      <span className="start-time" title={`开始时间: ${formatDateTime(item.start_time)}`}>
                        {formatDateTime(item.start_time)}
                      </span>
                      {item.end_time && (
                        <span className="end-time" title={`结束时间: ${formatDateTime(item.end_time)}`}>
                          结束: {formatDateTime(item.end_time)}
                        </span>
                      )}
                      {!item.end_time && item.status === 'running' && (
                        <span className="running-indicator">
                          🔄 运行中...
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="col-actions">
                    {item.status === 'completed' && (
                      <button
                        className="download-button"
                        onClick={() => handleDownload(item.id, item.file_path)}
                        disabled={downloading === item.id}
                        title="下载备份文件"
                      >
                        {downloading === item.id ? '下载中...' : '📥 下载'}
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 统计信息 */}
      {history.length > 0 && (
        <div className="history-stats">
          <div className="stats-grid">
            <div className="stat-item">
              <span className="stat-label">总备份次数</span>
              <span className="stat-value">{history.length}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">成功次数</span>
              <span className="stat-value success">
                {history.filter(h => h.status === 'completed').length}
              </span>
            </div>
            <div className="stat-item">
              <span className="stat-label">失败次数</span>
              <span className="stat-value failed">
                {history.filter(h => h.status === 'failed').length}
              </span>
            </div>
            <div className="stat-item">
              <span className="stat-label">总备份大小</span>
              <span className="stat-value">
                {formatFileSize(
                  history
                    .filter(h => h.status === 'completed')
                    .reduce((total, h) => total + (h.file_size || 0), 0)
                )}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BackupHistory;
