const fs = require('fs');
const path = require('path');

// 创建图标说明文件
const iconReadme = `# MySQL增量备份管理器 - 图标文件

## 图标文件说明

### 主图标文件
- \`mysql-backup-icon.svg\` - 矢量图标源文件
- \`mysql-backup-icon.ico\` - Windows应用图标 (需要手动转换)
- \`mysql-backup-icon.png\` - PNG格式图标

### 图标设计理念
1. **数据库符号**: 圆柱形数据库图标，代表MySQL数据库
2. **增量箭头**: 向上的绿色箭头，象征增量备份的概念
3. **专业配色**: 蓝色主色调，体现专业性和可靠性
4. **清晰识别**: 在不同尺寸下都能清晰识别

### 图标尺寸要求
- Windows应用图标: 256x256, 128x128, 64x64, 48x48, 32x32, 16x16
- 安装程序图标: 256x256
- 任务栏图标: 32x32, 16x16

### 转换工具推荐
1. **在线转换**:
   - https://convertio.co/svg-ico/
   - https://cloudconvert.com/svg-to-ico
   - https://www.icoconverter.com/

2. **本地工具**:
   - ImageMagick
   - GIMP
   - Adobe Illustrator

### 使用说明
1. 使用SVG文件作为源文件
2. 转换为ICO格式用于Windows应用
3. 确保ICO文件包含多种尺寸
4. 将生成的ICO文件放在public目录下

## 图标设计变体建议

### 变体1: 盾牌保护风格
- 主色调: 绿色 (#4CAF50)
- 设计元素: 盾牌 + 数据库
- 寓意: 数据保护和安全

### 变体2: 齿轮自动化风格  
- 主色调: 橙色 (#FF9800)
- 设计元素: 齿轮 + 数据库 + 时钟
- 寓意: 自动化备份

### 变体3: 文件夹版本风格
- 主色调: 紫色 (#9C27B0)  
- 设计元素: 多层文件夹
- 寓意: 版本控制和增量存储

### 变体4: 简约线条风格
- 主色调: 深蓝 (#1565C0)
- 设计元素: 极简线条数据库
- 寓意: 现代简洁

### 变体5: 云备份风格
- 主色调: 天蓝 (#03A9F4)
- 设计元素: 云朵 + 数据库 + 箭头
- 寓意: 云端备份
`;

// 写入图标说明文件
fs.writeFileSync(path.join(__dirname, '../public/ICON_README.md'), iconReadme);

console.log('图标说明文件已生成: public/ICON_README.md');
console.log('');
console.log('下一步操作:');
console.log('1. 使用在线工具将 mysql-backup-icon.svg 转换为 mysql-backup-icon.ico');
console.log('2. 确保ICO文件包含多种尺寸 (256, 128, 64, 48, 32, 16)');
console.log('3. 将ICO文件放在 public/ 目录下');
console.log('4. 运行 npm run dist:win 进行打包');
