/**
 * 企业级角色权限管理系统
 * 支持基于角色的访问控制(RBAC)、细粒度权限、权限继承
 */

export enum Permission {
  // 用户管理权限
  USER_CREATE = 'user:create',
  USER_READ = 'user:read',
  USER_UPDATE = 'user:update',
  USER_DELETE = 'user:delete',
  USER_LIST = 'user:list',

  // 服务器管理权限
  SERVER_CREATE = 'server:create',
  SERVER_READ = 'server:read',
  SERVER_UPDATE = 'server:update',
  SERVER_DELETE = 'server:delete',
  SERVER_LIST = 'server:list',
  SERVER_TEST = 'server:test',

  // 备份任务权限
  BACKUP_TASK_CREATE = 'backup_task:create',
  BACKUP_TASK_READ = 'backup_task:read',
  BACKUP_TASK_UPDATE = 'backup_task:update',
  BACKUP_TASK_DELETE = 'backup_task:delete',
  BACKUP_TASK_LIST = 'backup_task:list',
  BACKUP_TASK_EXECUTE = 'backup_task:execute',

  // 备份历史权限
  BACKUP_HISTORY_READ = 'backup_history:read',
  BACKUP_HISTORY_LIST = 'backup_history:list',
  BACKUP_HISTORY_DOWNLOAD = 'backup_history:download',
  BACKUP_HISTORY_DELETE = 'backup_history:delete',

  // 系统管理权限
  SYSTEM_CONFIG = 'system:config',
  SYSTEM_MONITOR = 'system:monitor',
  SYSTEM_LOGS = 'system:logs',
  SYSTEM_AUDIT = 'system:audit',

  // 高级权限
  ADMIN_FULL = 'admin:full',
  SECURITY_MANAGE = 'security:manage',
  ROLE_MANAGE = 'role:manage'
}

export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
  inherits?: string[]; // 继承其他角色
  isSystem: boolean; // 系统角色不可删除
  createdAt: Date;
  updatedAt: Date;
}

export interface UserRole {
  userId: number;
  roleId: string;
  assignedBy: number;
  assignedAt: Date;
  expiresAt?: Date;
}

/**
 * 预定义系统角色
 */
export const SystemRoles: Record<string, Role> = {
  SUPER_ADMIN: {
    id: 'super_admin',
    name: '超级管理员',
    description: '拥有所有权限的系统管理员',
    permissions: Object.values(Permission),
    isSystem: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },

  ADMIN: {
    id: 'admin',
    name: '管理员',
    description: '系统管理员，拥有大部分管理权限',
    permissions: [
      Permission.USER_READ,
      Permission.USER_LIST,
      Permission.SERVER_CREATE,
      Permission.SERVER_READ,
      Permission.SERVER_UPDATE,
      Permission.SERVER_DELETE,
      Permission.SERVER_LIST,
      Permission.SERVER_TEST,
      Permission.BACKUP_TASK_CREATE,
      Permission.BACKUP_TASK_READ,
      Permission.BACKUP_TASK_UPDATE,
      Permission.BACKUP_TASK_DELETE,
      Permission.BACKUP_TASK_LIST,
      Permission.BACKUP_TASK_EXECUTE,
      Permission.BACKUP_HISTORY_READ,
      Permission.BACKUP_HISTORY_LIST,
      Permission.BACKUP_HISTORY_DOWNLOAD,
      Permission.BACKUP_HISTORY_DELETE,
      Permission.SYSTEM_MONITOR,
      Permission.SYSTEM_LOGS
    ],
    isSystem: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },

  OPERATOR: {
    id: 'operator',
    name: '操作员',
    description: '备份操作员，可以执行备份任务和查看历史',
    permissions: [
      Permission.SERVER_READ,
      Permission.SERVER_LIST,
      Permission.SERVER_TEST,
      Permission.BACKUP_TASK_READ,
      Permission.BACKUP_TASK_LIST,
      Permission.BACKUP_TASK_EXECUTE,
      Permission.BACKUP_HISTORY_READ,
      Permission.BACKUP_HISTORY_LIST,
      Permission.BACKUP_HISTORY_DOWNLOAD
    ],
    isSystem: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },

  VIEWER: {
    id: 'viewer',
    name: '查看者',
    description: '只读权限，可以查看备份状态和历史',
    permissions: [
      Permission.SERVER_READ,
      Permission.SERVER_LIST,
      Permission.BACKUP_TASK_READ,
      Permission.BACKUP_TASK_LIST,
      Permission.BACKUP_HISTORY_READ,
      Permission.BACKUP_HISTORY_LIST
    ],
    isSystem: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },

  USER: {
    id: 'user',
    name: '普通用户',
    description: '普通用户，只能管理自己的备份任务',
    permissions: [
      Permission.SERVER_CREATE,
      Permission.SERVER_READ,
      Permission.SERVER_UPDATE,
      Permission.SERVER_DELETE,
      Permission.SERVER_LIST,
      Permission.SERVER_TEST,
      Permission.BACKUP_TASK_CREATE,
      Permission.BACKUP_TASK_READ,
      Permission.BACKUP_TASK_UPDATE,
      Permission.BACKUP_TASK_DELETE,
      Permission.BACKUP_TASK_LIST,
      Permission.BACKUP_TASK_EXECUTE,
      Permission.BACKUP_HISTORY_READ,
      Permission.BACKUP_HISTORY_LIST,
      Permission.BACKUP_HISTORY_DOWNLOAD
    ],
    isSystem: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
};

/**
 * 角色权限管理器
 */
export class RoleManager {
  private static instance: RoleManager;
  private roles: Map<string, Role> = new Map();
  private userRoles: Map<number, UserRole[]> = new Map();

  private constructor() {
    this.initializeSystemRoles();
  }

  public static getInstance(): RoleManager {
    if (!RoleManager.instance) {
      RoleManager.instance = new RoleManager();
    }
    return RoleManager.instance;
  }

  private initializeSystemRoles(): void {
    Object.values(SystemRoles).forEach(role => {
      this.roles.set(role.id, role);
    });
  }

  /**
   * 创建自定义角色
   */
  public createRole(role: Omit<Role, 'id' | 'createdAt' | 'updatedAt'>): Role {
    const newRole: Role = {
      ...role,
      id: this.generateRoleId(),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.roles.set(newRole.id, newRole);
    return newRole;
  }

  /**
   * 更新角色
   */
  public updateRole(roleId: string, updates: Partial<Role>): Role | null {
    const role = this.roles.get(roleId);
    if (!role) {
      return null;
    }

    if (role.isSystem) {
      throw new Error('Cannot modify system roles');
    }

    const updatedRole: Role = {
      ...role,
      ...updates,
      id: roleId, // 确保ID不被修改
      updatedAt: new Date()
    };

    this.roles.set(roleId, updatedRole);
    return updatedRole;
  }

  /**
   * 删除角色
   */
  public deleteRole(roleId: string): boolean {
    const role = this.roles.get(roleId);
    if (!role) {
      return false;
    }

    if (role.isSystem) {
      throw new Error('Cannot delete system roles');
    }

    // 检查是否有用户使用此角色
    const usersWithRole = Array.from(this.userRoles.values())
      .flat()
      .filter(ur => ur.roleId === roleId);

    if (usersWithRole.length > 0) {
      throw new Error('Cannot delete role that is assigned to users');
    }

    return this.roles.delete(roleId);
  }

  /**
   * 获取角色
   */
  public getRole(roleId: string): Role | null {
    return this.roles.get(roleId) || null;
  }

  /**
   * 获取所有角色
   */
  public getAllRoles(): Role[] {
    return Array.from(this.roles.values());
  }

  /**
   * 为用户分配角色
   */
  public assignRoleToUser(userId: number, roleId: string, assignedBy: number, expiresAt?: Date): void {
    const role = this.roles.get(roleId);
    if (!role) {
      throw new Error('Role not found');
    }

    const userRole: UserRole = {
      userId,
      roleId,
      assignedBy,
      assignedAt: new Date(),
      expiresAt
    };

    const userRoles = this.userRoles.get(userId) || [];
    
    // 检查是否已经有此角色
    const existingRoleIndex = userRoles.findIndex(ur => ur.roleId === roleId);
    if (existingRoleIndex >= 0) {
      userRoles[existingRoleIndex] = userRole; // 更新现有角色
    } else {
      userRoles.push(userRole); // 添加新角色
    }

    this.userRoles.set(userId, userRoles);
  }

  /**
   * 移除用户角色
   */
  public removeRoleFromUser(userId: number, roleId: string): boolean {
    const userRoles = this.userRoles.get(userId);
    if (!userRoles) {
      return false;
    }

    const filteredRoles = userRoles.filter(ur => ur.roleId !== roleId);
    if (filteredRoles.length === userRoles.length) {
      return false; // 没有找到要移除的角色
    }

    this.userRoles.set(userId, filteredRoles);
    return true;
  }

  /**
   * 获取用户角色
   */
  public getUserRoles(userId: number): Role[] {
    const userRoles = this.userRoles.get(userId) || [];
    const currentTime = new Date();

    return userRoles
      .filter(ur => !ur.expiresAt || ur.expiresAt > currentTime) // 过滤过期角色
      .map(ur => this.roles.get(ur.roleId))
      .filter(role => role !== undefined) as Role[];
  }

  /**
   * 获取用户所有权限（包括继承的权限）
   */
  public getUserPermissions(userId: number): Permission[] {
    const roles = this.getUserRoles(userId);
    const permissions = new Set<Permission>();

    const addRolePermissions = (role: Role) => {
      // 添加角色直接权限
      role.permissions.forEach(permission => permissions.add(permission));

      // 添加继承的权限
      if (role.inherits) {
        role.inherits.forEach(inheritedRoleId => {
          const inheritedRole = this.roles.get(inheritedRoleId);
          if (inheritedRole) {
            addRolePermissions(inheritedRole);
          }
        });
      }
    };

    roles.forEach(addRolePermissions);
    return Array.from(permissions);
  }

  /**
   * 检查用户是否有特定权限
   */
  public hasPermission(userId: number, permission: Permission): boolean {
    const userPermissions = this.getUserPermissions(userId);
    return userPermissions.includes(permission) || userPermissions.includes(Permission.ADMIN_FULL);
  }

  /**
   * 检查用户是否有任一权限
   */
  public hasAnyPermission(userId: number, permissions: Permission[]): boolean {
    return permissions.some(permission => this.hasPermission(userId, permission));
  }

  /**
   * 检查用户是否有所有权限
   */
  public hasAllPermissions(userId: number, permissions: Permission[]): boolean {
    return permissions.every(permission => this.hasPermission(userId, permission));
  }

  private generateRoleId(): string {
    return 'role_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
}

export const roleManager = RoleManager.getInstance();
