import React from 'react'
import <PERSON>actDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)

// Use contextBridge (only in Electron environment)
if (window.electronAPI && typeof window.electronAPI.on === 'function') {
  window.electronAPI.on('main-process-message', (_event, message) => {
    console.log(message)
  })
}
