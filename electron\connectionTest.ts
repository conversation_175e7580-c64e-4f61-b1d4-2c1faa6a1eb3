import mysql from 'mysql2/promise';
import { Client } from 'ssh2';

interface ServerConfig {
  host: string;
  port: number;
  username: string;
  password: string;
  sshHost?: string;
  sshPort?: number;
  sshUsername?: string;
  sshPassword?: string;
  sshPrivateKey?: string;
}

interface TestResult {
  success: boolean;
  message: string;
  details?: any;
}

// 测试直接MySQL连接
export async function testDirectMySQLConnection(config: ServerConfig): Promise<TestResult> {
  let connection: mysql.Connection | null = null;
  
  try {
    console.log('正在测试MySQL直连...', { host: config.host, port: config.port, username: config.username });
    
    connection = await mysql.createConnection({
      host: config.host,
      port: config.port,
      user: config.username,
      password: config.password,
      connectTimeout: 10000, // 10秒超时
      acquireTimeout: 10000,
      timeout: 10000
    });

    // 测试查询
    const [rows] = await connection.execute('SELECT VERSION() as version, NOW() as current_time');
    const result = rows as any[];
    
    console.log('MySQL连接测试成功:', result[0]);
    
    return {
      success: true,
      message: '连接成功',
      details: {
        version: result[0].version,
        currentTime: result[0].current_time,
        connectionType: 'direct'
      }
    };
    
  } catch (error: any) {
    console.error('MySQL直连测试失败:', error);
    
    let message = '连接失败';
    if (error.code === 'ECONNREFUSED') {
      message = '连接被拒绝，请检查主机地址和端口';
    } else if (error.code === 'ENOTFOUND') {
      message = '无法解析主机名，请检查主机地址';
    } else if (error.code === 'ETIMEDOUT') {
      message = '连接超时，请检查网络连接';
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      message = '访问被拒绝，请检查用户名和密码';
    } else if (error.message) {
      message = error.message;
    }
    
    return {
      success: false,
      message,
      details: {
        errorCode: error.code,
        errorMessage: error.message
      }
    };
  } finally {
    if (connection) {
      try {
        await connection.end();
      } catch (error) {
        console.error('关闭MySQL连接时出错:', error);
      }
    }
  }
}

// 测试SSH隧道连接
export async function testSSHTunnelConnection(config: ServerConfig): Promise<TestResult> {
  return new Promise((resolve) => {
    if (!config.sshHost || !config.sshUsername) {
      resolve({
        success: false,
        message: 'SSH配置不完整'
      });
      return;
    }

    console.log('正在测试SSH连接...', { 
      sshHost: config.sshHost, 
      sshPort: config.sshPort, 
      sshUsername: config.sshUsername 
    });

    const sshClient = new Client();
    let mysqlConnection: mysql.Connection | null = null;

    const cleanup = () => {
      if (mysqlConnection) {
        mysqlConnection.end().catch(console.error);
      }
      sshClient.end();
    };

    // 设置超时
    const timeout = setTimeout(() => {
      cleanup();
      resolve({
        success: false,
        message: 'SSH连接超时'
      });
    }, 15000); // 15秒超时

    sshClient.on('ready', () => {
      console.log('SSH连接建立成功，正在创建MySQL隧道...');
      
      // 创建SSH隧道
      sshClient.forwardOut(
        '127.0.0.1', // 本地地址
        0, // 本地端口（自动分配）
        config.host, // 远程MySQL主机
        config.port, // 远程MySQL端口
        async (err, stream) => {
          if (err) {
            clearTimeout(timeout);
            cleanup();
            resolve({
              success: false,
              message: `SSH隧道创建失败: ${err.message}`
            });
            return;
          }

          try {
            console.log('SSH隧道创建成功，正在测试MySQL连接...');
            
            // 通过SSH隧道连接MySQL
            mysqlConnection = await mysql.createConnection({
              user: config.username,
              password: config.password,
              stream: stream,
              connectTimeout: 10000
            });

            // 测试查询
            const [rows] = await mysqlConnection.execute('SELECT VERSION() as version, NOW() as current_time');
            const result = rows as any[];
            
            console.log('通过SSH隧道的MySQL连接测试成功:', result[0]);
            
            clearTimeout(timeout);
            cleanup();
            
            resolve({
              success: true,
              message: '通过SSH隧道连接成功',
              details: {
                version: result[0].version,
                currentTime: result[0].current_time,
                connectionType: 'ssh_tunnel'
              }
            });
            
          } catch (mysqlError: any) {
            console.error('通过SSH隧道的MySQL连接失败:', mysqlError);
            clearTimeout(timeout);
            cleanup();
            
            let message = 'MySQL连接失败';
            if (mysqlError.code === 'ER_ACCESS_DENIED_ERROR') {
              message = '访问被拒绝，请检查MySQL用户名和密码';
            } else if (mysqlError.message) {
              message = mysqlError.message;
            }
            
            resolve({
              success: false,
              message,
              details: {
                errorCode: mysqlError.code,
                errorMessage: mysqlError.message
              }
            });
          }
        }
      );
    });

    sshClient.on('error', (err) => {
      console.error('SSH连接错误:', err);
      clearTimeout(timeout);
      cleanup();
      
      let message = 'SSH连接失败';
      if (err.message.includes('ECONNREFUSED')) {
        message = 'SSH连接被拒绝，请检查SSH主机地址和端口';
      } else if (err.message.includes('ENOTFOUND')) {
        message = '无法解析SSH主机名';
      } else if (err.message.includes('Authentication')) {
        message = 'SSH认证失败，请检查用户名和密码';
      } else if (err.message) {
        message = err.message;
      }
      
      resolve({
        success: false,
        message,
        details: {
          errorMessage: err.message
        }
      });
    });

    // 连接SSH
    const sshConfig: any = {
      host: config.sshHost,
      port: config.sshPort || 22,
      username: config.sshUsername,
      readyTimeout: 10000
    };

    if (config.sshPassword) {
      sshConfig.password = config.sshPassword;
    } else if (config.sshPrivateKey) {
      sshConfig.privateKey = config.sshPrivateKey;
    } else {
      clearTimeout(timeout);
      resolve({
        success: false,
        message: 'SSH认证配置不完整，需要密码或私钥'
      });
      return;
    }

    sshClient.connect(sshConfig);
  });
}

// 综合连接测试
export async function testServerConnection(config: ServerConfig): Promise<TestResult> {
  console.log('开始服务器连接测试...', { 
    host: config.host, 
    hasSSH: !!(config.sshHost && config.sshUsername) 
  });

  // 如果配置了SSH，优先测试SSH隧道连接
  if (config.sshHost && config.sshUsername) {
    console.log('检测到SSH配置，使用SSH隧道连接...');
    return await testSSHTunnelConnection(config);
  } else {
    console.log('使用直接连接...');
    return await testDirectMySQLConnection(config);
  }
}

// 测试数据库是否存在
export async function testDatabaseExists(config: ServerConfig, databaseName: string): Promise<TestResult> {
  let connection: mysql.Connection | null = null;
  
  try {
    // 首先测试基本连接
    const connectionTest = await testServerConnection(config);
    if (!connectionTest.success) {
      return connectionTest;
    }

    // 创建连接测试数据库
    if (config.sshHost && config.sshUsername) {
      // SSH隧道连接 - 这里需要重新实现，因为我们需要保持连接
      return {
        success: false,
        message: '暂不支持通过SSH隧道测试数据库存在性'
      };
    } else {
      // 直接连接
      connection = await mysql.createConnection({
        host: config.host,
        port: config.port,
        user: config.username,
        password: config.password,
        connectTimeout: 10000
      });

      const [rows] = await connection.execute(
        'SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?',
        [databaseName]
      );
      
      const result = rows as any[];
      
      if (result.length > 0) {
        return {
          success: true,
          message: `数据库 '${databaseName}' 存在`,
          details: { databaseExists: true }
        };
      } else {
        return {
          success: false,
          message: `数据库 '${databaseName}' 不存在`,
          details: { databaseExists: false }
        };
      }
    }
    
  } catch (error: any) {
    console.error('测试数据库存在性失败:', error);
    return {
      success: false,
      message: `测试数据库失败: ${error.message}`,
      details: {
        errorCode: error.code,
        errorMessage: error.message
      }
    };
  } finally {
    if (connection) {
      try {
        await connection.end();
      } catch (error) {
        console.error('关闭连接时出错:', error);
      }
    }
  }
}
