-- MySQL dump 10.13  Distrib 8.0.24, for Linux (x86_64)
--
-- Host: ***************    Database: user
-- ------------------------------------------------------
-- Server version	8.0.24
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `backup_history`
--

DROP TABLE IF EXISTS `backup_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `backup_history` (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_id` int NOT NULL,
  `backup_type` enum('full','incremental') COLLATE utf8mb4_general_ci NOT NULL,
  `file_path` varchar(500) COLLATE utf8mb4_general_ci NOT NULL,
  `file_size` bigint DEFAULT '0',
  `start_time` timestamp NOT NULL,
  `end_time` timestamp NULL DEFAULT NULL,
  `status` enum('running','completed','failed') COLLATE utf8mb4_general_ci DEFAULT 'running',
  `error_message` text COLLATE utf8mb4_general_ci,
  `binlog_file` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `binlog_position` bigint DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `task_id` (`task_id`),
  CONSTRAINT `backup_history_ibfk_1` FOREIGN KEY (`task_id`) REFERENCES `backup_tasks` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `backup_history`
--

INSERT INTO `backup_history` (`id`, `task_id`, `backup_type`, `file_path`, `file_size`, `start_time`, `end_time`, `status`, `error_message`, `binlog_file`, `binlog_position`, `created_at`) VALUES (34,6,'full','/www/wwwroot/mysql/user_full_2025-07-31T09-17-58-587Z.sql',NULL,'2025-07-31 09:18:00','2025-07-31 09:18:01','completed',NULL,NULL,NULL,'2025-07-31 09:18:01'),(35,6,'full','/www/wwwroot/mysql/user_incremental_2025-07-31T09-18-58-568Z.sql',NULL,'2025-07-31 09:19:00','2025-07-31 09:19:00','completed',NULL,NULL,NULL,'2025-07-31 09:19:00');

--
-- Table structure for table `backup_tasks`
--

DROP TABLE IF EXISTS `backup_tasks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `backup_tasks` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `server_id` int NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `database_name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `backup_type` enum('full','incremental') COLLATE utf8mb4_general_ci DEFAULT 'incremental',
  `schedule_type` enum('manual','hourly','daily','weekly','monthly','custom') COLLATE utf8mb4_general_ci DEFAULT 'manual',
  `schedule_time` time DEFAULT NULL,
  `schedule_day` int DEFAULT NULL,
  `backup_path` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `retention_days` int DEFAULT '30',
  `status` enum('active','inactive','running','error') COLLATE utf8mb4_general_ci DEFAULT 'active',
  `last_backup_time` timestamp NULL DEFAULT NULL,
  `last_backup_size` bigint DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `custom_interval_minutes` int DEFAULT NULL COMMENT '自定义备份间隔（分钟）',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `server_id` (`server_id`),
  CONSTRAINT `backup_tasks_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `backup_tasks_ibfk_2` FOREIGN KEY (`server_id`) REFERENCES `servers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `backup_tasks`
--

INSERT INTO `backup_tasks` (`id`, `user_id`, `server_id`, `name`, `database_name`, `backup_type`, `schedule_type`, `schedule_time`, `schedule_day`, `backup_path`, `retention_days`, `status`, `last_backup_time`, `last_backup_size`, `created_at`, `updated_at`, `custom_interval_minutes`) VALUES (6,2,4,'1111111111','user','incremental','custom',NULL,1,'/www/wwwroot/mysql',30,'active',NULL,0,'2025-07-31 09:17:57','2025-07-31 09:17:57',1);

--
-- Table structure for table `servers`
--

DROP TABLE IF EXISTS `servers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `servers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `host` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `port` int DEFAULT '3306',
  `username` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `password` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `ssh_host` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ssh_port` int DEFAULT '22',
  `ssh_username` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ssh_password` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ssh_private_key` text COLLATE utf8mb4_general_ci,
  `status` enum('active','inactive','error') COLLATE utf8mb4_general_ci DEFAULT 'inactive',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `servers_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `servers`
--

INSERT INTO `servers` (`id`, `user_id`, `name`, `host`, `port`, `username`, `password`, `ssh_host`, `ssh_port`, `ssh_username`, `ssh_password`, `ssh_private_key`, `status`, `created_at`, `updated_at`) VALUES (3,2,'************','************',3306,'user','sharewharf','************',22,'root','Chr123456',NULL,'active','2025-07-30 01:37:38','2025-07-30 01:45:48'),(4,2,'***************','***************',3306,'user-hiram','user-hiram','***************',22,'root','Chr123456',NULL,'active','2025-07-31 09:16:03','2025-07-31 09:16:29');

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password_hash` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `email`, `password_hash`, `created_at`, `updated_at`) VALUES (1,'123','<EMAIL>','$2b$10$NrUtsdlHyEn15WsnqHZ3gugX6GDFP20GiNhZUgGxP9sV79lE/ncCe','2025-07-29 13:43:54','2025-07-29 13:43:54'),(2,'hiram','<EMAIL>','$2b$10$QBhKltYSxroVo8vhWKq6X.qCshnvLrtZkUGwJQUg8ymlx.BJa3GE2','2025-07-29 13:44:26','2025-07-29 13:44:26');

--
-- Dumping events for database 'user'
--

--
-- Dumping routines for database 'user'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-31 17:20:00
