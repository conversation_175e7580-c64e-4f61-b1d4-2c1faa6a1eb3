# MySQL增量备份管理系统

<div align="center">

![MySQL Backup Manager](public/mysql-backup-icon.svg)

**企业级MySQL数据库增量备份解决方案**

[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Node.js](https://img.shields.io/badge/node.js-18+-green.svg)](https://nodejs.org/)
[![Electron](https://img.shields.io/badge/electron-30+-blue.svg)](https://electronjs.org/)
[![TypeScript](https://img.shields.io/badge/typescript-5.2+-blue.svg)](https://www.typescriptlang.org/)

</div>

## 📋 项目概述

MySQL增量备份管理系统是一个基于Electron + React + TypeScript开发的企业级数据库备份解决方案。系统采用真正的MySQL binlog增量备份技术，提供完整的备份链管理、可视化监控、自动化调度等功能。

### 🎯 核心特性

- **🔄 真正的增量备份**: 基于MySQL binlog实现真正的增量备份，而非简单的时间戳对比
- **📊 备份链可视化**: 直观显示完整备份和增量备份的依赖关系
- **⏰ 智能调度**: 支持手动、每小时、每日、每周、每月及自定义间隔调度
- **🔐 企业级安全**: 多层身份认证、角色权限控制、数据加密传输
- **📈 实时监控**: 系统性能监控、备份状态跟踪、告警通知
- **🔧 一键恢复**: 支持恢复到任意时间点，自动合并备份链
- **📱 现代化UI**: 响应式设计，支持明暗主题切换

## 🏗️ 技术架构

### 前端技术栈
- **Electron 30+**: 跨平台桌面应用框架
- **React 18**: 用户界面构建
- **TypeScript 5.2+**: 类型安全的JavaScript
- **Vite**: 快速构建工具
- **CSS3**: 现代化样式设计

### 后端技术栈
- **Node.js 18+**: 服务端运行环境
- **MySQL2**: 数据库连接和操作
- **node-ssh**: SSH连接和远程命令执行
- **bcryptjs**: 密码加密
- **jsonwebtoken**: JWT认证

### 核心组件
```
src/
├── components/          # React组件
│   ├── Dashboard.tsx    # 主仪表板
│   ├── ServerManagement.tsx    # 服务器管理
│   ├── BackupManagement.tsx    # 备份任务管理
│   ├── BackupHistory.tsx       # 备份历史
│   └── BackupChainVisualization.tsx  # 备份链可视化
├── backup/              # 备份核心模块
│   ├── IncrementalBackup.ts    # 增量备份管理器
│   └── BackupPolicyManager.ts  # 备份策略管理
├── security/            # 安全模块
│   └── RoleManager.ts   # 角色权限管理
├── monitoring/          # 监控模块
│   └── SystemMonitor.ts # 系统监控
├── config/              # 配置管理
│   └── AppConfig.ts     # 应用配置
└── utils/               # 工具类
    └── Logger.ts        # 日志系统
```

## 🚀 快速开始

### 系统要求

- **操作系统**: Windows 10/11 (64位)
- **Node.js**: 18.0.0 或更高版本
- **npm**: 8.0.0 或更高版本
- **MySQL**: 5.7 或更高版本（支持binlog）

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/mysql-backup-manager/mysql-backup-manager.git
cd mysql-backup-manager
```

2. **安装依赖**
```bash
npm install
```

3. **配置数据库**
```bash
# 修改 electron/database.ts 中的数据库配置
const DB_CONFIG = {
  host: 'your-mysql-host',
  user: 'your-username', 
  password: 'your-password',
  database: 'backup_system',
  port: 3306
};
```

4. **启动开发环境**
```bash
# 启动前端开发服务器
npm run dev

# 启动Electron应用（新终端）
npm run electron:dev
```

### 生产环境部署

1. **构建应用**
```bash
npm run build
```

2. **打包Windows安装程序**
```bash
npm run dist:win
```

3. **生成的文件**
- `dist/MySQL增量备份管理器-Windows-1.0.0-Setup.exe` - 安装程序
- `dist/win-unpacked/` - 免安装版本

## 📖 使用指南

### 1. 用户注册与登录
- 首次使用需要注册管理员账户
- 支持用户名/邮箱登录
- JWT令牌自动管理会话

### 2. 服务器配置
- 添加MySQL服务器连接信息
- 配置SSH隧道（可选）
- 测试连接状态

### 3. 备份任务创建
- 选择备份类型（完整/增量）
- 设置调度策略
- 配置备份路径和保留策略

### 4. 监控与管理
- 实时查看备份状态
- 备份链依赖关系可视化
- 系统性能监控

### 5. 数据恢复
- 选择恢复时间点
- 自动生成完整SQL文件
- 一键恢复到目标数据库

## 🔧 配置说明

### 环境变量
```bash
# JWT密钥
JWT_SECRET=your-jwt-secret

# 加密密钥  
ENCRYPTION_KEY=your-encryption-key

# 配置文件路径
CONFIG_PATH=./config.json
```

### 配置文件示例
```json
{
  "environment": "production",
  "database": {
    "host": "localhost",
    "port": 3306,
    "connectionLimit": 10
  },
  "security": {
    "jwtExpiresIn": "24h",
    "passwordMinLength": 8
  },
  "backup": {
    "defaultRetentionDays": 30,
    "maxConcurrentBackups": 3
  }
}
```

## 📊 功能特性详解

### 增量备份原理
系统使用MySQL binlog实现真正的增量备份：
1. **基础备份**: 使用`mysqldump`创建完整数据库备份
2. **增量备份**: 使用`mysqlbinlog`提取binlog变更
3. **备份链**: 维护完整的备份依赖关系
4. **恢复**: 自动合并基础备份和所有增量备份

### 备份链可视化
- SVG绘制备份节点和依赖关系
- 区分完整备份(F)和增量备份(I)
- 实时显示备份状态和大小
- 支持点击查看详细信息

### 安全特性
- **多层认证**: JWT + API密钥
- **角色权限**: RBAC权限控制
- **数据加密**: HTTPS/TLS传输加密
- **审计日志**: 完整操作记录
- **敏感数据脱敏**: 自动过滤密码信息

## 🔌 API接口

系统提供完整的RESTful API接口：

```typescript
// 健康检查
GET /api/health

// 认证
POST /api/auth/login
POST /api/auth/refresh

// 系统信息
GET /api/system/info
GET /api/system/metrics

// 备份管理
GET /api/backups
POST /api/backups
PUT /api/backups/:id
DELETE /api/backups/:id

// 恢复操作
POST /api/restore
```

## 🛠️ 开发指南

### 项目结构
```
mysql-backup-manager/
├── electron/           # Electron主进程
├── src/               # React前端源码
├── public/            # 静态资源
├── scripts/           # 构建脚本
├── dist/              # 构建输出
└── docs/              # 文档
```

### 开发命令
```bash
npm run dev              # 开发模式
npm run build           # 构建生产版本
npm run build:renderer  # 构建前端
npm run build:electron  # 构建Electron
npm run dist:win       # 打包Windows版本
npm run pack:win       # 生成便携版
```

### 代码规范
- 使用TypeScript严格模式
- ESLint代码检查
- 组件化开发
- 错误边界处理

## 📈 性能优化

- **并发备份**: 支持多任务并行执行
- **增量技术**: 只备份变更数据
- **压缩优化**: 自动压缩备份文件
- **资源管理**: 智能内存和CPU使用
- **缓存机制**: 减少重复计算

## 🔍 故障排除

### 常见问题

1. **备份失败**
   - 检查MySQL服务器连接
   - 验证用户权限
   - 确认binlog已启用

2. **SSH连接问题**
   - 验证SSH凭据
   - 检查防火墙设置
   - 确认SSH服务状态

3. **权限错误**
   - 检查MySQL用户权限
   - 验证文件系统权限
   - 确认备份目录可写

### 日志查看
```bash
# 应用日志
tail -f logs/app.log

# 错误日志  
tail -f logs/error.log

# 备份日志
tail -f logs/backup.log
```

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

本项目采用 [MIT许可证](LICENSE)

## 👥 团队

- **开发团队**: MySQL Backup Manager Team
- **技术支持**: <EMAIL>
- **官方网站**: https://mysql-backup-manager.com

## 🙏 致谢

感谢以下开源项目的支持：
- [Electron](https://electronjs.org/)
- [React](https://reactjs.org/)
- [MySQL](https://mysql.com/)
- [Node.js](https://nodejs.org/)

---

<div align="center">

**如果这个项目对您有帮助，请给我们一个⭐️**

[报告问题](https://github.com/mysql-backup-manager/issues) · [功能请求](https://github.com/mysql-backup-manager/issues) · [文档](https://docs.mysql-backup-manager.com)

</div>
