# MySQL增量备份系统 - Windows卸载脚本
# 企业级自动化卸载程序

param(
    [string]$InstallPath = "C:\Program Files\MySQL Backup System",
    [string]$ServiceName = "MySQLBackupSystem",
    [switch]$Silent = $false,
    [switch]$KeepData = $false,
    [switch]$KeepConfig = $false
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 日志函数
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    Add-Content -Path "$env:TEMP\mysql-backup-uninstall.log" -Value $logMessage
}

# 检查管理员权限
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 停止并删除Windows服务
function Remove-WindowsService {
    Write-Log "停止并删除Windows服务..."
    
    try {
        $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
        if ($service) {
            if ($service.Status -eq "Running") {
                Write-Log "停止服务: $ServiceName"
                Stop-Service -Name $ServiceName -Force -ErrorAction SilentlyContinue
                
                # 等待服务完全停止
                $timeout = 30
                while ($timeout -gt 0 -and (Get-Service -Name $ServiceName).Status -eq "Running") {
                    Start-Sleep -Seconds 1
                    $timeout--
                }
            }
            
            Write-Log "删除服务: $ServiceName"
            sc.exe delete $ServiceName
            
            if ($LASTEXITCODE -eq 0) {
                Write-Log "服务删除成功"
            } else {
                Write-Log "服务删除失败，错误代码: $LASTEXITCODE" "WARNING"
            }
        } else {
            Write-Log "服务不存在，跳过删除"
        }
    }
    catch {
        Write-Log "删除服务时发生错误: $_" "ERROR"
    }
}

# 停止相关进程
function Stop-BackupProcesses {
    Write-Log "停止相关进程..."
    
    $processNames = @("mysql-backup-system", "node", "mysqldump")
    
    foreach ($processName in $processNames) {
        try {
            $processes = Get-Process -Name $processName -ErrorAction SilentlyContinue
            if ($processes) {
                foreach ($process in $processes) {
                    # 检查进程是否与我们的应用相关
                    if ($process.Path -and $process.Path.StartsWith($InstallPath)) {
                        Write-Log "停止进程: $($process.Name) (PID: $($process.Id))"
                        $process.Kill()
                        $process.WaitForExit(5000)
                    }
                }
            }
        }
        catch {
            Write-Log "停止进程 $processName 时发生错误: $_" "WARNING"
        }
    }
}

# 删除防火墙规则
function Remove-FirewallRules {
    Write-Log "删除防火墙规则..."
    
    try {
        Remove-NetFirewallRule -DisplayName "MySQL Backup System" -ErrorAction SilentlyContinue
        Remove-NetFirewallRule -DisplayName "MySQL Backup System API" -ErrorAction SilentlyContinue
        Write-Log "防火墙规则删除完成"
    }
    catch {
        Write-Log "删除防火墙规则时发生错误: $_" "WARNING"
    }
}

# 删除桌面快捷方式
function Remove-DesktopShortcut {
    Write-Log "删除桌面快捷方式..."
    
    $shortcutPath = "$env:USERPROFILE\Desktop\MySQL备份系统.lnk"
    if (Test-Path $shortcutPath) {
        Remove-Item $shortcutPath -Force
        Write-Log "桌面快捷方式删除完成"
    } else {
        Write-Log "桌面快捷方式不存在"
    }
}

# 删除开始菜单项
function Remove-StartMenuShortcut {
    Write-Log "删除开始菜单项..."
    
    $startMenuPath = "$env:APPDATA\Microsoft\Windows\Start Menu\Programs\MySQL备份系统"
    if (Test-Path $startMenuPath) {
        Remove-Item $startMenuPath -Recurse -Force
        Write-Log "开始菜单项删除完成"
    } else {
        Write-Log "开始菜单项不存在"
    }
}

# 删除注册表项
function Remove-RegistryEntries {
    Write-Log "删除注册表项..."
    
    try {
        $uninstallKey = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\MySQLBackupSystem"
        if (Test-Path $uninstallKey) {
            Remove-Item $uninstallKey -Force
            Write-Log "卸载注册表项删除完成"
        }
        
        # 删除其他可能的注册表项
        $appKey = "HKLM:\SOFTWARE\MySQLBackupSystem"
        if (Test-Path $appKey) {
            Remove-Item $appKey -Recurse -Force
            Write-Log "应用程序注册表项删除完成"
        }
    }
    catch {
        Write-Log "删除注册表项时发生错误: $_" "WARNING"
    }
}

# 备份用户数据
function Backup-UserData {
    if ($KeepData) {
        Write-Log "保留用户数据，跳过备份"
        return
    }
    
    Write-Log "备份用户数据..."
    
    try {
        $backupPath = "$env:USERPROFILE\Documents\MySQL Backup System Backup"
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $backupDir = "$backupPath\Backup_$timestamp"
        
        New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
        
        # 备份配置文件
        if (Test-Path "$InstallPath\config") {
            Copy-Item "$InstallPath\config\*" "$backupDir\config\" -Recurse -Force
            Write-Log "配置文件已备份到: $backupDir\config"
        }
        
        # 备份日志文件
        if (Test-Path "$InstallPath\logs") {
            Copy-Item "$InstallPath\logs\*" "$backupDir\logs\" -Recurse -Force
            Write-Log "日志文件已备份到: $backupDir\logs"
        }
        
        # 备份数据库文件（如果有）
        if (Test-Path "$InstallPath\data") {
            Copy-Item "$InstallPath\data\*" "$backupDir\data\" -Recurse -Force
            Write-Log "数据文件已备份到: $backupDir\data"
        }
        
        Write-Log "用户数据备份完成: $backupDir"
        
        if (-not $Silent) {
            Write-Host "用户数据已备份到: $backupDir" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Log "备份用户数据时发生错误: $_" "ERROR"
    }
}

# 删除应用程序文件
function Remove-ApplicationFiles {
    Write-Log "删除应用程序文件..."
    
    if (-not (Test-Path $InstallPath)) {
        Write-Log "安装目录不存在: $InstallPath"
        return
    }
    
    try {
        # 如果选择保留配置，先备份配置文件
        $tempConfigPath = $null
        if ($KeepConfig -and (Test-Path "$InstallPath\config")) {
            $tempConfigPath = "$env:TEMP\mysql-backup-config-$(Get-Date -Format 'yyyyMMddHHmmss')"
            Copy-Item "$InstallPath\config" $tempConfigPath -Recurse -Force
            Write-Log "临时备份配置文件到: $tempConfigPath"
        }
        
        # 删除安装目录
        Remove-Item $InstallPath -Recurse -Force
        Write-Log "应用程序文件删除完成"
        
        # 如果选择保留配置，恢复配置文件
        if ($KeepConfig -and $tempConfigPath) {
            New-Item -ItemType Directory -Path "$InstallPath\config" -Force | Out-Null
            Copy-Item "$tempConfigPath\*" "$InstallPath\config\" -Recurse -Force
            Remove-Item $tempConfigPath -Recurse -Force
            Write-Log "配置文件已恢复"
        }
    }
    catch {
        Write-Log "删除应用程序文件时发生错误: $_" "ERROR"
        throw
    }
}

# 清理临时文件
function Clear-TempFiles {
    Write-Log "清理临时文件..."
    
    try {
        $tempPaths = @(
            "$env:TEMP\mysql-backup-*",
            "$env:LOCALAPPDATA\MySQL Backup System",
            "$env:APPDATA\MySQL Backup System"
        )
        
        foreach ($tempPath in $tempPaths) {
            if (Test-Path $tempPath) {
                Remove-Item $tempPath -Recurse -Force -ErrorAction SilentlyContinue
            }
        }
        
        Write-Log "临时文件清理完成"
    }
    catch {
        Write-Log "清理临时文件时发生错误: $_" "WARNING"
    }
}

# 验证卸载完成
function Test-UninstallComplete {
    Write-Log "验证卸载完成..."
    
    $issues = @()
    
    # 检查服务是否还存在
    if (Get-Service -Name $ServiceName -ErrorAction SilentlyContinue) {
        $issues += "Windows服务仍然存在"
    }
    
    # 检查安装目录是否还存在
    if (Test-Path $InstallPath) {
        $issues += "安装目录仍然存在"
    }
    
    # 检查注册表项是否还存在
    if (Test-Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\MySQLBackupSystem") {
        $issues += "注册表项仍然存在"
    }
    
    if ($issues.Count -eq 0) {
        Write-Log "卸载验证通过" "SUCCESS"
        return $true
    } else {
        Write-Log "卸载验证发现问题:" "WARNING"
        foreach ($issue in $issues) {
            Write-Log "  - $issue" "WARNING"
        }
        return $false
    }
}

# 主卸载流程
function Start-Uninstallation {
    try {
        Write-Log "开始卸载MySQL增量备份系统..."
        Write-Log "安装路径: $InstallPath"
        
        # 检查管理员权限
        if (-not (Test-Administrator)) {
            throw "需要管理员权限才能卸载服务"
        }
        
        # 确认卸载
        if (-not $Silent) {
            Write-Host "即将卸载MySQL增量备份系统" -ForegroundColor Yellow
            Write-Host "安装路径: $InstallPath" -ForegroundColor White
            
            if (-not $KeepData) {
                Write-Host "警告: 所有数据将被删除！" -ForegroundColor Red
            }
            
            $response = Read-Host "确定要继续吗? (y/N)"
            if ($response -ne "y" -and $response -ne "Y") {
                Write-Log "用户取消卸载"
                exit 0
            }
        }
        
        # 执行卸载步骤
        Stop-BackupProcesses
        Remove-WindowsService
        Remove-FirewallRules
        Remove-DesktopShortcut
        Remove-StartMenuShortcut
        Remove-RegistryEntries
        Backup-UserData
        Remove-ApplicationFiles
        Clear-TempFiles
        
        # 验证卸载
        $uninstallComplete = Test-UninstallComplete
        
        if ($uninstallComplete) {
            Write-Log "卸载完成！" "SUCCESS"
            
            if (-not $Silent) {
                Write-Host "`n卸载成功完成！" -ForegroundColor Green
                
                if (-not $KeepData) {
                    Write-Host "所有应用程序文件和数据已被删除。" -ForegroundColor White
                } else {
                    Write-Host "应用程序已卸载，用户数据已保留。" -ForegroundColor White
                }
                
                Write-Host "感谢您使用MySQL增量备份系统！" -ForegroundColor Cyan
            }
        } else {
            Write-Log "卸载可能未完全完成，请检查日志" "WARNING"
            
            if (-not $Silent) {
                Write-Host "`n卸载可能未完全完成" -ForegroundColor Yellow
                Write-Host "请检查日志文件: $env:TEMP\mysql-backup-uninstall.log" -ForegroundColor White
            }
        }
        
    }
    catch {
        Write-Log "卸载失败: $_" "ERROR"
        Write-Host "卸载失败: $_" -ForegroundColor Red
        exit 1
    }
}

# 显示帮助信息
function Show-Help {
    Write-Host @"
MySQL增量备份系统 - 卸载脚本

用法: .\uninstall.ps1 [参数]

参数:
  -InstallPath <路径>        安装路径 (默认: C:\Program Files\MySQL Backup System)
  -ServiceName <名称>        Windows服务名称 (默认: MySQLBackupSystem)
  -Silent                    静默卸载，不显示交互提示
  -KeepData                  保留用户数据和备份文件
  -KeepConfig                保留配置文件
  -Help                      显示此帮助信息

示例:
  .\uninstall.ps1
  .\uninstall.ps1 -Silent -KeepData
  .\uninstall.ps1 -InstallPath "D:\MySQL Backup" -KeepConfig

"@ -ForegroundColor Cyan
}

# 脚本入口点
if ($args -contains "-Help" -or $args -contains "--help" -or $args -contains "/?") {
    Show-Help
    exit 0
}

# 开始卸载
Start-Uninstallation
