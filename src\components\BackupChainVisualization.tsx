import React, { useEffect, useRef, useState } from 'react';
import './BackupChainVisualization.css';

interface BackupNode {
  id: number;
  type: 'full' | 'incremental';
  startTime: string;
  endTime?: string;
  status: 'completed' | 'failed' | 'running';
  fileSize?: number;
  binlogFile?: string;
  binlogPosition?: number;
  dependencies: number[]; // 依赖的备份ID
}

interface BackupChainVisualizationProps {
  taskId: number;
  userId: number;
  history: any[];
  onNodeClick?: (node: BackupNode) => void;
  onRestoreClick?: (nodeId: number) => void;
}

export const BackupChainVisualization: React.FC<BackupChainVisualizationProps> = ({
  taskId,
  userId,
  history,
  onNodeClick,
  onRestoreClick
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [nodes, setNodes] = useState<BackupNode[]>([]);
  const [selectedNode, setSelectedNode] = useState<number | null>(null);

  useEffect(() => {
    // 转换历史记录为节点数据
    const backupNodes = convertHistoryToNodes(history);
    setNodes(backupNodes);
  }, [history]);

  useEffect(() => {
    if (nodes.length > 0 && svgRef.current) {
      renderBackupChain();
    }
  }, [nodes]);

  const convertHistoryToNodes = (historyData: any[]): BackupNode[] => {
    const sortedHistory = [...historyData].sort((a, b) => 
      new Date(a.start_time).getTime() - new Date(b.start_time).getTime()
    );

    return sortedHistory.map((item, index) => {
      const dependencies: number[] = [];
      
      if (item.backup_type === 'incremental') {
        // 增量备份依赖于前一个备份
        if (index > 0) {
          dependencies.push(sortedHistory[index - 1].id);
        }
      }

      return {
        id: item.id,
        type: item.backup_type,
        startTime: item.start_time,
        endTime: item.end_time,
        status: item.status,
        fileSize: item.file_size,
        binlogFile: item.binlog_file,
        binlogPosition: item.binlog_position,
        dependencies
      };
    });
  };

  const renderBackupChain = () => {
    const svg = svgRef.current;
    if (!svg) return;

    // 清空SVG内容
    svg.innerHTML = '';

    const width = svg.clientWidth || 800;
    const height = svg.clientHeight || 400;
    const nodeRadius = 30;
    const levelHeight = 80;

    // 按类型分组节点
    const fullBackups = nodes.filter(n => n.type === 'full');
    const incrementalBackups = nodes.filter(n => n.type === 'incremental');

    // 计算节点位置
    const nodePositions = new Map<number, { x: number; y: number }>();

    // 放置完整备份节点
    fullBackups.forEach((node, index) => {
      const x = (width / (fullBackups.length + 1)) * (index + 1);
      const y = levelHeight;
      nodePositions.set(node.id, { x, y });
    });

    // 放置增量备份节点
    incrementalBackups.forEach((node, index) => {
      const x = (width / (incrementalBackups.length + 1)) * (index + 1);
      const y = levelHeight * 2;
      nodePositions.set(node.id, { x, y });
    });

    // 绘制连接线
    nodes.forEach(node => {
      const nodePos = nodePositions.get(node.id);
      if (!nodePos) return;

      node.dependencies.forEach(depId => {
        const depPos = nodePositions.get(depId);
        if (!depPos) return;

        // 创建连接线
        const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        line.setAttribute('x1', depPos.x.toString());
        line.setAttribute('y1', (depPos.y + nodeRadius).toString());
        line.setAttribute('x2', nodePos.x.toString());
        line.setAttribute('y2', (nodePos.y - nodeRadius).toString());
        line.setAttribute('stroke', '#666');
        line.setAttribute('stroke-width', '2');
        line.setAttribute('marker-end', 'url(#arrowhead)');
        svg.appendChild(line);
      });
    });

    // 添加箭头标记定义
    const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
    const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
    marker.setAttribute('id', 'arrowhead');
    marker.setAttribute('markerWidth', '10');
    marker.setAttribute('markerHeight', '7');
    marker.setAttribute('refX', '9');
    marker.setAttribute('refY', '3.5');
    marker.setAttribute('orient', 'auto');
    
    const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
    polygon.setAttribute('points', '0 0, 10 3.5, 0 7');
    polygon.setAttribute('fill', '#666');
    
    marker.appendChild(polygon);
    defs.appendChild(marker);
    svg.appendChild(defs);

    // 绘制节点
    nodes.forEach(node => {
      const pos = nodePositions.get(node.id);
      if (!pos) return;

      // 创建节点组
      const group = document.createElementNS('http://www.w3.org/2000/svg', 'g');
      group.setAttribute('class', 'backup-node');
      group.setAttribute('data-node-id', node.id.toString());

      // 节点圆圈
      const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
      circle.setAttribute('cx', pos.x.toString());
      circle.setAttribute('cy', pos.y.toString());
      circle.setAttribute('r', nodeRadius.toString());
      circle.setAttribute('class', `node-${node.type} node-${node.status}`);
      
      // 节点点击事件
      circle.addEventListener('click', () => {
        setSelectedNode(node.id);
        onNodeClick?.(node);
      });

      group.appendChild(circle);

      // 节点标签
      const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      text.setAttribute('x', pos.x.toString());
      text.setAttribute('y', (pos.y + 5).toString());
      text.setAttribute('text-anchor', 'middle');
      text.setAttribute('class', 'node-label');
      text.textContent = node.type === 'full' ? 'F' : 'I';
      
      group.appendChild(text);

      // 时间标签
      const timeText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      timeText.setAttribute('x', pos.x.toString());
      timeText.setAttribute('y', (pos.y + nodeRadius + 20).toString());
      timeText.setAttribute('text-anchor', 'middle');
      timeText.setAttribute('class', 'time-label');
      timeText.textContent = new Date(node.startTime).toLocaleDateString();
      
      group.appendChild(timeText);

      svg.appendChild(group);
    });
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'N/A';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return '#28a745';
      case 'failed': return '#dc3545';
      case 'running': return '#ffc107';
      default: return '#6c757d';
    }
  };

  const selectedNodeData = selectedNode ? nodes.find(n => n.id === selectedNode) : null;

  return (
    <div className="backup-chain-visualization">
      <div className="visualization-header">
        <h3>备份链依赖关系图</h3>
        <div className="legend">
          <div className="legend-item">
            <div className="legend-circle full"></div>
            <span>完整备份</span>
          </div>
          <div className="legend-item">
            <div className="legend-circle incremental"></div>
            <span>增量备份</span>
          </div>
        </div>
      </div>

      <div className="visualization-content">
        <div className="chain-diagram">
          <svg
            ref={svgRef}
            width="100%"
            height="300"
            viewBox="0 0 800 300"
            preserveAspectRatio="xMidYMid meet"
          />
        </div>

        {selectedNodeData && (
          <div className="node-details">
            <h4>备份详情</h4>
            <div className="detail-grid">
              <div className="detail-item">
                <span className="label">类型:</span>
                <span className="value">
                  {selectedNodeData.type === 'full' ? '完整备份' : '增量备份'}
                </span>
              </div>
              <div className="detail-item">
                <span className="label">状态:</span>
                <span 
                  className="value status-badge"
                  style={{ color: getStatusColor(selectedNodeData.status) }}
                >
                  {selectedNodeData.status === 'completed' ? '已完成' : 
                   selectedNodeData.status === 'failed' ? '失败' : '运行中'}
                </span>
              </div>
              <div className="detail-item">
                <span className="label">开始时间:</span>
                <span className="value">
                  {new Date(selectedNodeData.startTime).toLocaleString()}
                </span>
              </div>
              {selectedNodeData.endTime && (
                <div className="detail-item">
                  <span className="label">结束时间:</span>
                  <span className="value">
                    {new Date(selectedNodeData.endTime).toLocaleString()}
                  </span>
                </div>
              )}
              <div className="detail-item">
                <span className="label">文件大小:</span>
                <span className="value">{formatFileSize(selectedNodeData.fileSize)}</span>
              </div>
              {selectedNodeData.binlogFile && (
                <>
                  <div className="detail-item">
                    <span className="label">Binlog文件:</span>
                    <span className="value">{selectedNodeData.binlogFile}</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">Binlog位置:</span>
                    <span className="value">{selectedNodeData.binlogPosition}</span>
                  </div>
                </>
              )}
              <div className="detail-item">
                <span className="label">依赖备份:</span>
                <span className="value">
                  {selectedNodeData.dependencies.length > 0 
                    ? selectedNodeData.dependencies.join(', ') 
                    : '无'}
                </span>
              </div>
            </div>
            
            {selectedNodeData.status === 'completed' && onRestoreClick && (
              <div className="node-actions">
                <button
                  className="btn btn-primary"
                  onClick={() => onRestoreClick(selectedNodeData.id)}
                >
                  恢复到此时间点
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
