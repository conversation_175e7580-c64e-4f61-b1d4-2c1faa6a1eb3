<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#065F46;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#10B981;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="shield2" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#34D399;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="inner2" cx="50%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#ECFDF5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#A7F3D0;stop-opacity:1" />
    </radialGradient>
  </defs>
  
  <!-- 背景圆角矩形 -->
  <rect width="256" height="256" rx="48" ry="48" fill="url(#bg2)"/>
  
  <!-- 盾牌主体 -->
  <g transform="translate(128, 128)">
    <path d="M 0 -80 C -60 -80 -60 -40 -60 0 C -60 40 -40 80 0 100 C 40 80 60 40 60 0 C 60 -40 60 -80 0 -80 Z" 
          fill="url(#shield2)" stroke="#047857" stroke-width="3"/>
    
    <!-- 盾牌内部区域 -->
    <path d="M 0 -65 C -45 -65 -45 -30 -45 5 C -45 35 -30 65 0 80 C 30 65 45 35 45 5 C 45 -30 45 -65 0 -65 Z" 
          fill="url(#inner2)" opacity="0.9"/>
    
    <!-- 数据库图标 -->
    <g transform="translate(0, -10)">
      <!-- 数据库顶部 -->
      <ellipse cx="0" cy="-20" rx="25" ry="6" fill="#047857"/>
      
      <!-- 数据库主体 -->
      <rect x="-25" y="-20" width="50" height="30" fill="#059669"/>
      
      <!-- 数据库底部 -->
      <ellipse cx="0" cy="10" rx="25" ry="6" fill="#047857"/>
      
      <!-- 数据库分层 -->
      <ellipse cx="0" cy="-10" rx="25" ry="6" fill="none" stroke="#065F46" stroke-width="1"/>
      <ellipse cx="0" cy="0" rx="25" ry="6" fill="none" stroke="#065F46" stroke-width="1"/>
    </g>
    
    <!-- 保护符号 -->
    <g transform="translate(0, 25)">
      <!-- 对勾 -->
      <path d="M -15 0 L -5 10 L 15 -10" stroke="#047857" stroke-width="4" 
            stroke-linecap="round" stroke-linejoin="round" fill="none"/>
    </g>
    
    <!-- 增量指示器 -->
    <g transform="translate(30, -30)">
      <circle cx="0" cy="0" r="8" fill="#FBBF24"/>
      <text x="0" y="4" text-anchor="middle" font-family="Arial, sans-serif" 
            font-size="10" font-weight="bold" fill="#92400E">+</text>
    </g>
    
    <!-- 标题 -->
    <text x="0" y="65" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="12" font-weight="bold" fill="#FFFFFF">BACKUP</text>
  </g>
</svg>
