/**
 * 企业级报告生成组件
 * 支持备份报告、性能报告、系统健康报告等多种报告类型
 */

import React, { useState, useEffect } from 'react';
import './ReportGenerator.css';

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  type: 'backup' | 'performance' | 'system' | 'security' | 'custom';
  format: 'pdf' | 'excel' | 'csv' | 'html';
  schedule?: {
    enabled: boolean;
    frequency: 'daily' | 'weekly' | 'monthly';
    time: string;
    recipients: string[];
  };
  parameters: {
    dateRange: { start: Date; end: Date };
    includeCharts: boolean;
    includeDetails: boolean;
    groupBy: string;
    filters: { [key: string]: any };
  };
}

interface ReportData {
  id: string;
  templateId: string;
  name: string;
  generatedAt: Date;
  status: 'generating' | 'completed' | 'failed';
  filePath?: string;
  fileSize?: number;
  error?: string;
  downloadUrl?: string;
}

const ReportGenerator: React.FC = () => {
  const [templates, setTemplates] = useState<ReportTemplate[]>([]);
  const [reports, setReports] = useState<ReportData[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showTemplateEditor, setShowTemplateEditor] = useState(false);

  useEffect(() => {
    loadTemplates();
    loadReports();
  }, []);

  const loadTemplates = async () => {
    // 模拟加载报告模板
    const defaultTemplates: ReportTemplate[] = [
      {
        id: 'backup_summary',
        name: '备份汇总报告',
        description: '显示指定时间范围内的备份执行情况、成功率、存储使用情况等',
        type: 'backup',
        format: 'pdf',
        parameters: {
          dateRange: {
            start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            end: new Date()
          },
          includeCharts: true,
          includeDetails: true,
          groupBy: 'daily',
          filters: {}
        }
      },
      {
        id: 'performance_analysis',
        name: '性能分析报告',
        description: '分析系统性能指标、资源使用情况、优化建议等',
        type: 'performance',
        format: 'excel',
        parameters: {
          dateRange: {
            start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            end: new Date()
          },
          includeCharts: true,
          includeDetails: false,
          groupBy: 'hourly',
          filters: {}
        }
      },
      {
        id: 'system_health',
        name: '系统健康报告',
        description: '系统整体健康状况、告警统计、维护建议等',
        type: 'system',
        format: 'html',
        schedule: {
          enabled: true,
          frequency: 'weekly',
          time: '09:00',
          recipients: ['<EMAIL>']
        },
        parameters: {
          dateRange: {
            start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            end: new Date()
          },
          includeCharts: true,
          includeDetails: true,
          groupBy: 'daily',
          filters: {}
        }
      }
    ];

    setTemplates(defaultTemplates);
  };

  const loadReports = async () => {
    // 模拟加载历史报告
    const sampleReports: ReportData[] = [
      {
        id: 'report_1',
        templateId: 'backup_summary',
        name: '备份汇总报告_2024-01-15',
        generatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
        status: 'completed',
        filePath: '/reports/backup_summary_20240115.pdf',
        fileSize: 2048576,
        downloadUrl: '#'
      },
      {
        id: 'report_2',
        templateId: 'performance_analysis',
        name: '性能分析报告_2024-01-14',
        generatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
        status: 'completed',
        filePath: '/reports/performance_20240114.xlsx',
        fileSize: 1536000,
        downloadUrl: '#'
      }
    ];

    setReports(sampleReports);
  };

  const generateReport = async (template: ReportTemplate) => {
    setIsGenerating(true);

    try {
      const reportData: ReportData = {
        id: `report_${Date.now()}`,
        templateId: template.id,
        name: `${template.name}_${new Date().toISOString().split('T')[0]}`,
        generatedAt: new Date(),
        status: 'generating'
      };

      setReports(prev => [reportData, ...prev]);

      // 模拟报告生成过程
      setTimeout(() => {
        const updatedReport: ReportData = {
          ...reportData,
          status: 'completed',
          filePath: `/reports/${reportData.name.replace(/\s+/g, '_')}.${template.format}`,
          fileSize: Math.floor(Math.random() * 5000000) + 1000000,
          downloadUrl: '#'
        };

        setReports(prev => prev.map(r => r.id === reportData.id ? updatedReport : r));
        setIsGenerating(false);
      }, 3000);

    } catch (error) {
      console.error('生成报告失败:', error);
      setIsGenerating(false);
    }
  };

  const downloadReport = (report: ReportData) => {
    // 实际实现中应该触发文件下载
    console.log('下载报告:', report.name);
  };

  const deleteReport = (reportId: string) => {
    setReports(prev => prev.filter(r => r.id !== reportId));
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusIcon = (status: string): string => {
    switch (status) {
      case 'generating': return '⏳';
      case 'completed': return '✅';
      case 'failed': return '❌';
      default: return '📄';
    }
  };

  const getTypeIcon = (type: string): string => {
    switch (type) {
      case 'backup': return '💾';
      case 'performance': return '📊';
      case 'system': return '🖥️';
      case 'security': return '🔒';
      default: return '📋';
    }
  };

  return (
    <div className="report-generator">
      <div className="report-header">
        <h2>报告生成器</h2>
        <button 
          className="btn-primary"
          onClick={() => setShowTemplateEditor(true)}
        >
          创建模板
        </button>
      </div>

      <div className="report-content">
        {/* 报告模板 */}
        <div className="report-section">
          <h3>报告模板</h3>
          <div className="template-grid">
            {templates.map(template => (
              <div key={template.id} className="template-card">
                <div className="template-header">
                  <span className="template-icon">{getTypeIcon(template.type)}</span>
                  <h4>{template.name}</h4>
                </div>
                <p className="template-description">{template.description}</p>
                <div className="template-info">
                  <span className="template-format">{template.format.toUpperCase()}</span>
                  {template.schedule?.enabled && (
                    <span className="template-scheduled">📅 已计划</span>
                  )}
                </div>
                <div className="template-actions">
                  <button 
                    className="btn-secondary"
                    onClick={() => setSelectedTemplate(template)}
                  >
                    配置
                  </button>
                  <button 
                    className="btn-primary"
                    onClick={() => generateReport(template)}
                    disabled={isGenerating}
                  >
                    生成报告
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 历史报告 */}
        <div className="report-section">
          <h3>历史报告</h3>
          <div className="reports-table">
            <div className="table-header">
              <div className="col-name">报告名称</div>
              <div className="col-status">状态</div>
              <div className="col-size">大小</div>
              <div className="col-date">生成时间</div>
              <div className="col-actions">操作</div>
            </div>
            {reports.map(report => (
              <div key={report.id} className="table-row">
                <div className="col-name">
                  <span className="report-icon">{getStatusIcon(report.status)}</span>
                  {report.name}
                </div>
                <div className="col-status">
                  <span className={`status-badge ${report.status}`}>
                    {report.status === 'generating' ? '生成中' :
                     report.status === 'completed' ? '已完成' : '失败'}
                  </span>
                </div>
                <div className="col-size">
                  {report.fileSize ? formatFileSize(report.fileSize) : '-'}
                </div>
                <div className="col-date">
                  {report.generatedAt.toLocaleString()}
                </div>
                <div className="col-actions">
                  {report.status === 'completed' && (
                    <button 
                      className="btn-link"
                      onClick={() => downloadReport(report)}
                    >
                      下载
                    </button>
                  )}
                  <button 
                    className="btn-link danger"
                    onClick={() => deleteReport(report.id)}
                  >
                    删除
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 模板配置对话框 */}
      {selectedTemplate && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>配置报告参数</h3>
              <button 
                className="modal-close"
                onClick={() => setSelectedTemplate(null)}
              >
                ×
              </button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label>时间范围</label>
                <div className="date-range">
                  <input 
                    type="date" 
                    value={selectedTemplate.parameters.dateRange.start.toISOString().split('T')[0]}
                    onChange={(e) => {
                      const newTemplate = { ...selectedTemplate };
                      newTemplate.parameters.dateRange.start = new Date(e.target.value);
                      setSelectedTemplate(newTemplate);
                    }}
                  />
                  <span>至</span>
                  <input 
                    type="date" 
                    value={selectedTemplate.parameters.dateRange.end.toISOString().split('T')[0]}
                    onChange={(e) => {
                      const newTemplate = { ...selectedTemplate };
                      newTemplate.parameters.dateRange.end = new Date(e.target.value);
                      setSelectedTemplate(newTemplate);
                    }}
                  />
                </div>
              </div>
              
              <div className="form-group">
                <label>
                  <input 
                    type="checkbox" 
                    checked={selectedTemplate.parameters.includeCharts}
                    onChange={(e) => {
                      const newTemplate = { ...selectedTemplate };
                      newTemplate.parameters.includeCharts = e.target.checked;
                      setSelectedTemplate(newTemplate);
                    }}
                  />
                  包含图表
                </label>
              </div>
              
              <div className="form-group">
                <label>
                  <input 
                    type="checkbox" 
                    checked={selectedTemplate.parameters.includeDetails}
                    onChange={(e) => {
                      const newTemplate = { ...selectedTemplate };
                      newTemplate.parameters.includeDetails = e.target.checked;
                      setSelectedTemplate(newTemplate);
                    }}
                  />
                  包含详细信息
                </label>
              </div>
              
              <div className="form-group">
                <label>分组方式</label>
                <select 
                  value={selectedTemplate.parameters.groupBy}
                  onChange={(e) => {
                    const newTemplate = { ...selectedTemplate };
                    newTemplate.parameters.groupBy = e.target.value;
                    setSelectedTemplate(newTemplate);
                  }}
                >
                  <option value="hourly">按小时</option>
                  <option value="daily">按天</option>
                  <option value="weekly">按周</option>
                  <option value="monthly">按月</option>
                </select>
              </div>
            </div>
            <div className="modal-footer">
              <button 
                className="btn-secondary"
                onClick={() => setSelectedTemplate(null)}
              >
                取消
              </button>
              <button 
                className="btn-primary"
                onClick={() => {
                  generateReport(selectedTemplate);
                  setSelectedTemplate(null);
                }}
                disabled={isGenerating}
              >
                生成报告
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReportGenerator;
