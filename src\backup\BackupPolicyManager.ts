/**
 * 企业级备份策略管理器
 * 管理备份策略模板、备份验证、清理策略等
 */

import { logger } from '../utils/Logger';
import { config } from '../config/AppConfig';
import { notificationManager } from '../notifications/NotificationManager';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';

export interface BackupPolicy {
  id: string;
  name: string;
  description: string;
  type: 'full' | 'incremental' | 'differential';
  schedule: {
    frequency: 'daily' | 'weekly' | 'monthly' | 'custom';
    time: string; // HH:MM format
    daysOfWeek?: number[]; // 0-6, Sunday = 0
    dayOfMonth?: number; // 1-31
    cronExpression?: string;
  };
  retention: {
    keepDays: number;
    keepWeeks: number;
    keepMonths: number;
    keepYears: number;
    maxBackups: number;
  };
  compression: {
    enabled: boolean;
    level: number; // 1-9
    algorithm: 'gzip' | 'bzip2' | 'xz';
  };
  encryption: {
    enabled: boolean;
    algorithm: 'aes-256-cbc' | 'aes-256-gcm';
    keyDerivation: 'pbkdf2' | 'scrypt';
  };
  verification: {
    enabled: boolean;
    checksumAlgorithm: 'md5' | 'sha256' | 'sha512';
    testRestore: boolean;
  };
  notification: {
    onSuccess: boolean;
    onFailure: boolean;
    onWarning: boolean;
    recipients: string[];
  };
  storage: {
    localPath: string;
    remoteEnabled: boolean;
    remoteType?: 's3' | 'ftp' | 'sftp' | 'azure' | 'gcs';
    remoteConfig?: any;
  };
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  isActive: boolean;
}

export interface BackupVerificationResult {
  backupId: string;
  filePath: string;
  checksum: string;
  expectedChecksum: string;
  isValid: boolean;
  fileSize: number;
  verifiedAt: Date;
  errors: string[];
}

export interface BackupCleanupResult {
  deletedFiles: string[];
  freedSpace: number;
  errors: string[];
  cleanupAt: Date;
}

/**
 * 备份策略管理器
 */
export class BackupPolicyManager {
  private static instance: BackupPolicyManager;
  private policies: Map<string, BackupPolicy> = new Map();
  private defaultPolicies: BackupPolicy[] = [];

  private constructor() {
    this.initializeDefaultPolicies();
    this.loadPolicies();
  }

  public static getInstance(): BackupPolicyManager {
    if (!BackupPolicyManager.instance) {
      BackupPolicyManager.instance = new BackupPolicyManager();
    }
    return BackupPolicyManager.instance;
  }

  /**
   * 初始化默认策略模板
   */
  private initializeDefaultPolicies(): void {
    this.defaultPolicies = [
      {
        id: 'daily_full',
        name: '每日全量备份',
        description: '每天凌晨2点执行全量备份，保留30天',
        type: 'full',
        schedule: {
          frequency: 'daily',
          time: '02:00'
        },
        retention: {
          keepDays: 30,
          keepWeeks: 4,
          keepMonths: 3,
          keepYears: 1,
          maxBackups: 50
        },
        compression: {
          enabled: true,
          level: 6,
          algorithm: 'gzip'
        },
        encryption: {
          enabled: false,
          algorithm: 'aes-256-cbc',
          keyDerivation: 'pbkdf2'
        },
        verification: {
          enabled: true,
          checksumAlgorithm: 'sha256',
          testRestore: false
        },
        notification: {
          onSuccess: false,
          onFailure: true,
          onWarning: true,
          recipients: []
        },
        storage: {
          localPath: './backups',
          remoteEnabled: false
        },
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: 'system',
        isActive: true
      },
      {
        id: 'weekly_full_daily_inc',
        name: '周全量+日增量',
        description: '每周日全量备份，每日增量备份',
        type: 'incremental',
        schedule: {
          frequency: 'daily',
          time: '03:00'
        },
        retention: {
          keepDays: 7,
          keepWeeks: 8,
          keepMonths: 6,
          keepYears: 2,
          maxBackups: 100
        },
        compression: {
          enabled: true,
          level: 9,
          algorithm: 'xz'
        },
        encryption: {
          enabled: true,
          algorithm: 'aes-256-gcm',
          keyDerivation: 'scrypt'
        },
        verification: {
          enabled: true,
          checksumAlgorithm: 'sha512',
          testRestore: true
        },
        notification: {
          onSuccess: true,
          onFailure: true,
          onWarning: true,
          recipients: []
        },
        storage: {
          localPath: './backups',
          remoteEnabled: true,
          remoteType: 's3'
        },
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: 'system',
        isActive: true
      }
    ];
  }

  /**
   * 加载策略配置
   */
  private loadPolicies(): void {
    try {
      const policiesPath = path.join(process.cwd(), 'config', 'backup-policies.json');
      
      if (fs.existsSync(policiesPath)) {
        const data = fs.readFileSync(policiesPath, 'utf8');
        const policies = JSON.parse(data);
        
        policies.forEach((policy: any) => {
          this.policies.set(policy.id, {
            ...policy,
            createdAt: new Date(policy.createdAt),
            updatedAt: new Date(policy.updatedAt)
          });
        });
        
        logger.info('备份策略加载完成', { count: this.policies.size });
      } else {
        // 使用默认策略
        this.defaultPolicies.forEach(policy => {
          this.policies.set(policy.id, policy);
        });
        
        this.savePolicies();
        logger.info('使用默认备份策略');
      }
    } catch (error) {
      logger.error('加载备份策略失败', error as Error);
      
      // 使用默认策略作为后备
      this.defaultPolicies.forEach(policy => {
        this.policies.set(policy.id, policy);
      });
    }
  }

  /**
   * 保存策略配置
   */
  private savePolicies(): void {
    try {
      const configDir = path.join(process.cwd(), 'config');
      if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true });
      }
      
      const policiesPath = path.join(configDir, 'backup-policies.json');
      const policies = Array.from(this.policies.values());
      
      fs.writeFileSync(policiesPath, JSON.stringify(policies, null, 2));
      logger.info('备份策略保存完成');
    } catch (error) {
      logger.error('保存备份策略失败', error as Error);
    }
  }

  /**
   * 创建备份策略
   */
  public createPolicy(policy: Omit<BackupPolicy, 'id' | 'createdAt' | 'updatedAt'>): string {
    const id = this.generatePolicyId();
    const newPolicy: BackupPolicy = {
      ...policy,
      id,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.policies.set(id, newPolicy);
    this.savePolicies();

    logger.info('备份策略已创建', { policyId: id, name: policy.name });
    return id;
  }

  /**
   * 更新备份策略
   */
  public updatePolicy(id: string, updates: Partial<BackupPolicy>): boolean {
    const policy = this.policies.get(id);
    if (!policy) {
      logger.warn('备份策略不存在', { policyId: id });
      return false;
    }

    const updatedPolicy: BackupPolicy = {
      ...policy,
      ...updates,
      id, // 确保ID不被修改
      updatedAt: new Date()
    };

    this.policies.set(id, updatedPolicy);
    this.savePolicies();

    logger.info('备份策略已更新', { policyId: id });
    return true;
  }

  /**
   * 删除备份策略
   */
  public deletePolicy(id: string): boolean {
    if (!this.policies.has(id)) {
      logger.warn('备份策略不存在', { policyId: id });
      return false;
    }

    this.policies.delete(id);
    this.savePolicies();

    logger.info('备份策略已删除', { policyId: id });
    return true;
  }

  /**
   * 获取备份策略
   */
  public getPolicy(id: string): BackupPolicy | undefined {
    return this.policies.get(id);
  }

  /**
   * 获取所有策略
   */
  public getAllPolicies(): BackupPolicy[] {
    return Array.from(this.policies.values());
  }

  /**
   * 获取活跃策略
   */
  public getActivePolicies(): BackupPolicy[] {
    return Array.from(this.policies.values()).filter(policy => policy.isActive);
  }

  /**
   * 验证备份文件
   */
  public async verifyBackup(filePath: string, expectedChecksum?: string): Promise<BackupVerificationResult> {
    const result: BackupVerificationResult = {
      backupId: path.basename(filePath),
      filePath,
      checksum: '',
      expectedChecksum: expectedChecksum || '',
      isValid: false,
      fileSize: 0,
      verifiedAt: new Date(),
      errors: []
    };

    try {
      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        result.errors.push('备份文件不存在');
        return result;
      }

      // 获取文件大小
      const stats = fs.statSync(filePath);
      result.fileSize = stats.size;

      // 计算校验和
      const hash = crypto.createHash('sha256');
      const stream = fs.createReadStream(filePath);
      
      for await (const chunk of stream) {
        hash.update(chunk);
      }
      
      result.checksum = hash.digest('hex');

      // 验证校验和
      if (expectedChecksum) {
        result.isValid = result.checksum === expectedChecksum;
        if (!result.isValid) {
          result.errors.push('校验和不匹配');
        }
      } else {
        result.isValid = true; // 没有期望值时认为有效
      }

      logger.info('备份验证完成', { 
        filePath, 
        isValid: result.isValid, 
        fileSize: result.fileSize 
      });

    } catch (error) {
      result.errors.push(`验证失败: ${(error as Error).message}`);
      logger.error('备份验证失败', error as Error, { filePath });
    }

    return result;
  }

  /**
   * 清理过期备份
   */
  public async cleanupExpiredBackups(policyId: string, backupDir: string): Promise<BackupCleanupResult> {
    const result: BackupCleanupResult = {
      deletedFiles: [],
      freedSpace: 0,
      errors: [],
      cleanupAt: new Date()
    };

    try {
      const policy = this.policies.get(policyId);
      if (!policy) {
        result.errors.push('备份策略不存在');
        return result;
      }

      if (!fs.existsSync(backupDir)) {
        result.errors.push('备份目录不存在');
        return result;
      }

      const files = fs.readdirSync(backupDir);
      const now = new Date();
      const cutoffDate = new Date(now.getTime() - policy.retention.keepDays * 24 * 60 * 60 * 1000);

      for (const file of files) {
        const filePath = path.join(backupDir, file);
        const stats = fs.statSync(filePath);

        // 检查文件是否过期
        if (stats.mtime < cutoffDate) {
          try {
            result.freedSpace += stats.size;
            fs.unlinkSync(filePath);
            result.deletedFiles.push(file);
            
            logger.info('删除过期备份', { file, size: stats.size });
          } catch (error) {
            result.errors.push(`删除文件失败: ${file} - ${(error as Error).message}`);
          }
        }
      }

      logger.info('备份清理完成', { 
        deletedCount: result.deletedFiles.length,
        freedSpace: result.freedSpace 
      });

    } catch (error) {
      result.errors.push(`清理失败: ${(error as Error).message}`);
      logger.error('备份清理失败', error as Error, { policyId, backupDir });
    }

    return result;
  }

  /**
   * 生成策略ID
   */
  private generatePolicyId(): string {
    return 'policy_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
}

export const backupPolicyManager = BackupPolicyManager.getInstance();
