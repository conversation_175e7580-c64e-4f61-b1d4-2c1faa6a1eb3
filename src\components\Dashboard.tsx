import React, { useState } from 'react';
import './Dashboard.css';
import ServerManagement from './ServerManagement';
import BackupManagement from './BackupManagement';
import BackupPlanDetailEnterprise from './BackupPlanDetailEnterprise';
import BackupHistory from './BackupHistory';
import SystemOverview from './SystemOverview';
import SystemOverviewSimple from './SystemOverviewSimple';
import TestComponent from './TestComponent';
import SystemOverviewFixed from './SystemOverviewFixed';
import ReportGenerator from './ReportGenerator';

interface User {
  id: number;
  username: string;
  email: string;
}

interface DashboardProps {
  user: User;
  onLogout: () => void;
}

const Dashboard: React.FC<DashboardProps> = ({ user, onLogout }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [currentView, setCurrentView] = useState<'dashboard' | 'backup-plan-detail'>('dashboard');
  const [selectedTaskId, setSelectedTaskId] = useState<number | null>(null);

  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    onLogout();
  };

  const handleViewBackupDetail = (taskId: number) => {
    setSelectedTaskId(taskId);
    setCurrentView('backup-plan-detail');
  };

  const handleBackToDashboard = () => {
    setCurrentView('dashboard');
    setSelectedTaskId(null);
  };

  // 如果是备份计划详情页面，直接渲染详情组件
  if (currentView === 'backup-plan-detail' && selectedTaskId) {
    return (
      <div className="dashboard">
        <BackupPlanDetailEnterprise
          taskId={selectedTaskId}
          userId={user.id}
          onBack={handleBackToDashboard}
        />
      </div>
    );
  }

  return (
    <div className="dashboard">
      <header className="dashboard-header">
        <div className="header-left">
          <div className="app-branding">
            <h1 className="app-title">MySQL 增量备份系统 - 修复版</h1>
            <span className="app-subtitle">企业级数据库备份解决方案 v1.0.0</span>
          </div>
        </div>
        <div className="header-right">
          <div className="user-section">
            <div className="user-info">
              <span className="welcome-text">欢迎，{user.username}</span>
              <span className="user-role">管理员</span>
            </div>
            <button className="logout-button" onClick={handleLogout}>
              退出登录
            </button>
          </div>
        </div>
      </header>

      <nav className="dashboard-nav">
        <button
          className={`nav-button ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          系统概览
        </button>
        <button
          className={`nav-button ${activeTab === 'backup' ? 'active' : ''}`}
          onClick={() => setActiveTab('backup')}
        >
          备份管理
        </button>
        <button
          className={`nav-button ${activeTab === 'servers' ? 'active' : ''}`}
          onClick={() => setActiveTab('servers')}
        >
          服务器管理
        </button>
        <button
          className={`nav-button ${activeTab === 'history' ? 'active' : ''}`}
          onClick={() => setActiveTab('history')}
        >
          备份历史
        </button>
        <button
          className={`nav-button ${activeTab === 'reports' ? 'active' : ''}`}
          onClick={() => setActiveTab('reports')}
        >
          报告生成
        </button>
        <button
          className={`nav-button ${activeTab === 'settings' ? 'active' : ''}`}
          onClick={() => setActiveTab('settings')}
        >
          设置
        </button>
      </nav>

      <main className="dashboard-content">
        {activeTab === 'overview' && (
          <div className="content-panel">
            <SystemOverviewFixed />
          </div>
        )}

        {activeTab === 'backup' && (
          <div className="content-panel">
            <BackupManagement userId={user.id} onViewDetail={handleViewBackupDetail} />
          </div>
        )}

        {activeTab === 'servers' && (
          <div className="content-panel">
            <ServerManagement userId={user.id} />
          </div>
        )}

        {activeTab === 'history' && (
          <div className="content-panel">
            <BackupHistory userId={user.id} />
          </div>
        )}

        {activeTab === 'reports' && (
          <div className="content-panel">
            <ReportGenerator />
          </div>
        )}

        {activeTab === 'settings' && (
          <div className="content-panel">
            <h2>设置</h2>
            <div className="settings-section">
              <div className="info-card">
                <h3>用户信息</h3>
                <div className="user-details">
                  <p><strong>用户名:</strong> {user.username}</p>
                  <p><strong>邮箱:</strong> {user.email}</p>
                  <p><strong>用户ID:</strong> {user.id}</p>
                </div>
              </div>
              
              <div className="info-card">
                <h3>应用设置</h3>
                <p>配置应用的基本设置和偏好。</p>
                <div className="action-buttons">
                  <button className="secondary-button">修改密码</button>
                  <button className="secondary-button">备份设置</button>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default Dashboard;
