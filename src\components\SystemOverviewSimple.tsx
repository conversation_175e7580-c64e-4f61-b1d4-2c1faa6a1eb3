import React, { useState, useEffect } from 'react';
import './SystemOverview.css';

const SystemOverviewSimple: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [systemData, setSystemData] = useState<any>(null);

  useEffect(() => {
    loadSystemData();
  }, []);

  const loadSystemData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('开始加载系统数据...');
      
      // 检查用户登录状态
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      if (!user.id) {
        throw new Error('用户未登录');
      }

      console.log('用户ID:', user.id);

      // 检查 electronAPI 是否可用
      if (!window.electronAPI) {
        throw new Error('electronAPI 不可用');
      }

      console.log('electronAPI 可用，开始获取数据...');

      // 获取系统指标
      const metricsResult = await window.electronAPI.getSystemMetrics();
      console.log('系统指标结果:', metricsResult);

      // 获取备份统计
      const statsResult = await window.electronAPI.getBackupStats(user.id);
      console.log('备份统计结果:', statsResult);

      setSystemData({
        metrics: metricsResult,
        stats: statsResult,
        timestamp: new Date()
      });

      setLoading(false);
    } catch (error) {
      console.error('加载系统数据失败:', error);
      setError(error instanceof Error ? error.message : '未知错误');
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="system-overview">
        <div className="overview-header">
          <h2>系统概览 - 简化版</h2>
        </div>
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>正在加载系统数据...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="system-overview">
        <div className="overview-header">
          <h2>系统概览 - 简化版</h2>
          <button onClick={loadSystemData} className="refresh-btn">
            🔄 重试
          </button>
        </div>
        <div className="error-container">
          <h3>❌ 加载失败</h3>
          <p>错误信息: {error}</p>
          <button onClick={loadSystemData} className="retry-btn">
            重新加载
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="system-overview">
      <div className="overview-header">
        <h2>系统概览 - 简化版</h2>
        <button onClick={loadSystemData} className="refresh-btn">
          🔄 刷新数据
        </button>
      </div>

      <div className="debug-info">
        <h3>调试信息</h3>
        <div className="debug-section">
          <h4>加载时间</h4>
          <p>{systemData?.timestamp?.toLocaleString()}</p>
        </div>

        <div className="debug-section">
          <h4>系统指标</h4>
          <pre>{JSON.stringify(systemData?.metrics, null, 2)}</pre>
        </div>

        <div className="debug-section">
          <h4>备份统计</h4>
          <pre>{JSON.stringify(systemData?.stats, null, 2)}</pre>
        </div>

        <div className="debug-section">
          <h4>用户信息</h4>
          <pre>{localStorage.getItem('user')}</pre>
        </div>

        <div className="debug-section">
          <h4>ElectronAPI 状态</h4>
          <p>可用: {window.electronAPI ? '✅ 是' : '❌ 否'}</p>
          {window.electronAPI && (
            <div>
              <p>getSystemMetrics: {typeof window.electronAPI.getSystemMetrics}</p>
              <p>getBackupStats: {typeof window.electronAPI.getBackupStats}</p>
            </div>
          )}
        </div>
      </div>

      <style jsx>{`
        .debug-info {
          margin-top: 20px;
          padding: 20px;
          background: #f5f5f5;
          border-radius: 8px;
        }
        .debug-section {
          margin-bottom: 20px;
          padding: 15px;
          background: white;
          border-radius: 6px;
          border: 1px solid #ddd;
        }
        .debug-section h4 {
          margin-top: 0;
          color: #333;
        }
        .debug-section pre {
          background: #f8f8f8;
          padding: 10px;
          border-radius: 4px;
          overflow-x: auto;
          font-size: 12px;
        }
        .loading-container, .error-container {
          text-align: center;
          padding: 40px;
        }
        .loading-spinner {
          width: 40px;
          height: 40px;
          border: 4px solid #f3f3f3;
          border-top: 4px solid #3498db;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 20px;
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        .retry-btn, .refresh-btn {
          background: #3498db;
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 5px;
          cursor: pointer;
          margin-top: 10px;
        }
        .retry-btn:hover, .refresh-btn:hover {
          background: #2980b9;
        }
      `}</style>
    </div>
  );
};

export default SystemOverviewSimple;
