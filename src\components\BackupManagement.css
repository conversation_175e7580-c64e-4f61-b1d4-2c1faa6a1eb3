/* 企业级备份管理样式 */

.backup-management {
  padding: var(--spacing-3xl);
  background: var(--bg-primary);
  min-height: 100vh;
}

.backup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-3xl);
  padding: var(--spacing-2xl) var(--spacing-3xl);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  border-radius: var(--radius-xl);
  color: var(--text-inverse);
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
}

.backup-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="backup-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23backup-pattern)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

.backup-title {
  margin: 0;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  position: relative;
  z-index: 1;
}

.backup-title::before {
  content: '💾';
  font-size: var(--font-size-xl);
}

.backup-stats {
  display: flex;
  gap: var(--spacing-xl);
  position: relative;
  z-index: 1;
}

.backup-stat {
  text-align: center;
  background: rgba(255, 255, 255, 0.15);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.backup-stat-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-xs);
}

.backup-stat-label {
  font-size: var(--font-size-sm);
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 备份操作区域 */
.backup-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-3xl);
}

.backup-action-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: var(--spacing-3xl);
  box-shadow: var(--shadow-card);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.backup-action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.backup-action-card.full-backup::before {
  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
}

.backup-action-card.incremental-backup::before {
  background: linear-gradient(90deg, var(--info-color), #0284c7);
}

.backup-action-card.restore::before {
  background: linear-gradient(90deg, var(--warning-color), var(--warning-hover));
}

.backup-action-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-card-hover);
}

.backup-action-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.backup-action-description {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: var(--line-height-relaxed);
}

.backup-action-button {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--transition-normal);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.backup-action-button.primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: var(--text-inverse);
  box-shadow: var(--shadow-md);
}

.backup-action-button.primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.backup-action-button.info {
  background: linear-gradient(135deg, var(--info-color), #0284c7);
  color: var(--text-inverse);
  box-shadow: var(--shadow-md);
}

.backup-action-button.info:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.backup-action-button.warning {
  background: linear-gradient(135deg, var(--warning-color), var(--warning-hover));
  color: var(--text-inverse);
  box-shadow: var(--shadow-md);
}

.backup-action-button.warning:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* 备份历史列表 */
.backup-history {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-card);
  overflow: hidden;
}

.backup-history-header {
  padding: var(--spacing-2xl) var(--spacing-3xl);
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
}

.backup-history-title {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.backup-history-title::before {
  content: '📋';
  font-size: var(--font-size-lg);
}

.backup-list {
  max-height: 600px;
  overflow-y: auto;
}

.backup-list::-webkit-scrollbar {
  width: 8px;
}

.backup-list::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

.backup-list::-webkit-scrollbar-thumb {
  background: var(--border-hover);
  border-radius: var(--radius-md);
}

.backup-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-3xl);
  border-bottom: 1px solid var(--border-color);
  transition: background-color var(--transition-fast);
}

.backup-item:hover {
  background: var(--bg-hover);
}

.backup-item:last-child {
  border-bottom: none;
}

.backup-info {
  flex: 1;
}

.backup-name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.backup-meta {
  display: flex;
  gap: var(--spacing-lg);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.backup-type {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.backup-type.full {
  background: var(--primary-light);
  color: var(--primary-color);
}

.backup-type.incremental {
  background: var(--info-light);
  color: var(--info-color);
}

.backup-type.differential {
  background: var(--warning-light);
  color: var(--warning-color);
}

.backup-actions-list {
  display: flex;
  gap: var(--spacing-sm);
}

.backup-action-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
  min-height: auto;
}

.backup-action-btn:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
  border-color: var(--border-hover);
}

.backup-action-btn.download {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.backup-action-btn.download:hover {
  background: var(--primary-light);
}

.backup-action-btn.restore {
  color: var(--warning-color);
  border-color: var(--warning-color);
}

.backup-action-btn.restore:hover {
  background: var(--warning-light);
}

.backup-action-btn.delete {
  color: var(--danger-color);
  border-color: var(--danger-color);
}

.backup-action-btn.delete:hover {
  background: var(--danger-light);
}

/* 表单样式 */
.add-task-form {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: var(--spacing-3xl);
  margin-bottom: var(--spacing-2xl);
  box-shadow: var(--shadow-card);
  position: relative;
  overflow: hidden;
}

.add-task-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover), var(--info-color));
  pointer-events: none;
  z-index: 0;
}

.add-task-form h4 {
  margin: 0 0 var(--spacing-2xl) 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.add-task-form h4::before {
  content: '📝';
  font-size: var(--font-size-lg);
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-xl);
  align-items: start;
  margin-bottom: var(--spacing-xl);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  position: relative;
  z-index: 1;
}

.form-group label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-xs);
}

.form-group input,
.form-group select {
  padding: var(--spacing-md) var(--spacing-lg);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  background: var(--bg-secondary);
  color: var(--text-primary);
  transition: all var(--transition-normal);
  min-height: 44px;
  font-family: inherit;
  outline: none;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  cursor: text;
  user-select: text;
}

.form-group input:focus,
.form-group select:focus {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.1);
  background: var(--bg-secondary);
}

.form-group input:hover:not(:focus):not(:disabled),
.form-group select:hover:not(:focus):not(:disabled) {
  border-color: var(--border-hover);
}

.form-group input:disabled,
.form-group select:disabled {
  background: var(--bg-disabled);
  color: var(--text-disabled);
  cursor: not-allowed;
  opacity: 0.7;
}

.form-group input::placeholder {
  color: var(--text-tertiary);
  opacity: 1;
}

/* 输入框与按钮组合样式 */
.input-with-button {
  display: flex;
  gap: var(--spacing-sm);
  align-items: stretch;
  position: relative;
}

.input-with-button input {
  flex: 1;
  border-radius: var(--radius-lg);
  position: relative;
  z-index: 1;
  pointer-events: auto;
}

.input-with-button button {
  border-radius: var(--radius-lg);
  white-space: nowrap;
  min-width: auto;
  padding: var(--spacing-md) var(--spacing-lg);
  border: 2px solid var(--border-color);
  background: var(--bg-secondary);
  color: var(--text-primary);
  position: relative;
  z-index: 1;
  pointer-events: auto;
  cursor: pointer;
}

.input-with-button button:hover:not(:disabled) {
  background: var(--bg-hover);
  border-color: var(--border-hover);
}

.input-with-button button:disabled {
  background: var(--bg-disabled);
  color: var(--text-disabled);
  cursor: not-allowed;
  opacity: 0.7;
}

/* 表单操作按钮 */
.form-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

/* 任务列表样式 */
.tasks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.task-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-card);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 200px;
}

.task-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--info-color));
}

.task-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-card-hover);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.task-header h4 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  flex: 1;
  line-height: 1.3;
}

.status-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-xs);
  flex-shrink: 0;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

.status-indicator {
  width: 6px;
  height: 6px;
  border-radius: var(--radius-full);
  display: inline-block;
  margin-right: var(--spacing-xs);
  flex-shrink: 0;
}

.status-badge.active .status-indicator {
  background: var(--success-color);
  animation: pulse 2s infinite;
}

.status-badge.inactive .status-indicator {
  background: var(--text-tertiary);
}

.status-badge.running .status-indicator {
  background: var(--warning-color);
  animation: pulse 1s infinite;
}

.status-badge.error .status-indicator {
  background: var(--danger-color);
  animation: pulse 1.5s infinite;
}

.status-badge.active {
  background: var(--success-light);
  color: var(--success-color);
}

.status-badge.inactive {
  background: var(--bg-disabled);
  color: var(--text-disabled);
}

.status-badge.running {
  background: var(--warning-light);
  color: var(--warning-color);
}

.status-badge.error {
  background: var(--danger-light);
  color: var(--danger-color);
}

.auto-backup-indicator {
  font-size: var(--font-size-xs);
  color: var(--success-color);
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  animation: fadeIn 0.5s ease-in;
}

.task-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.task-status {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.task-status.active {
  background: var(--success-light);
  color: var(--success-color);
}

.task-status.inactive {
  background: var(--secondary-light);
  color: var(--secondary-color);
}

.task-status.running {
  background: var(--info-light);
  color: var(--info-color);
}

.task-status.error {
  background: var(--danger-light);
  color: var(--danger-color);
}

.task-details {
  flex: 1;
  margin-bottom: var(--spacing-md);
}

.task-details p {
  margin: var(--spacing-xs) 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.4;
}

.task-details strong {
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
  margin-right: var(--spacing-xs);
}

.task-actions {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
  margin-top: auto;
  padding-top: var(--spacing-sm);
  border-top: 1px solid var(--border-color);
}

.task-actions button {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-xs);
  min-height: 32px;
  border-radius: var(--radius-sm);
  border: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
  flex: 1;
  min-width: 80px;
}

/* 按钮颜色样式 */
.task-actions .btn-primary {
  background: var(--primary-color);
  color: white;
}

.task-actions .btn-primary:hover {
  background: var(--primary-hover);
}

.task-actions .btn-success {
  background: var(--success-color);
  color: white;
}

.task-actions .btn-success:hover {
  background: var(--success-hover);
}

.task-actions .btn-warning {
  background: var(--warning-color);
  color: white;
}

.task-actions .btn-warning:hover {
  background: var(--warning-hover);
}

.task-actions .btn-danger {
  background: var(--danger-color);
  color: white;
}

.task-actions .btn-danger:hover {
  background: var(--danger-hover);
}

/* 时间节点按钮样式 */
.warning-button {
  background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-hover) 100%);
  color: var(--text-inverse);
  border: 1px solid var(--warning-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  text-decoration: none;
  min-height: 36px;
  box-shadow: var(--shadow-sm);
}

.warning-button:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--warning-hover) 0%, var(--warning-color) 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.warning-button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.warning-button:disabled {
  background: var(--bg-disabled);
  color: var(--text-disabled);
  border-color: var(--border-color);
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
  box-shadow: none;
}

.warning-button::before {
  content: '⏰';
  font-size: var(--font-size-sm);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .backup-actions {
    grid-template-columns: 1fr;
  }

  .backup-stats {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .backup-header {
    flex-direction: column;
    gap: var(--spacing-lg);
    text-align: center;
  }

  .backup-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  .backup-actions-list {
    width: 100%;
    justify-content: flex-end;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .form-actions {
    flex-direction: column;
  }

  .add-task-form {
    padding: var(--spacing-2xl);
  }

  .tasks-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  .task-card {
    padding: var(--spacing-md);
    min-height: auto;
  }

  .task-header h4 {
    font-size: var(--font-size-md);
  }

  .task-actions {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .task-actions button {
    min-width: auto;
    flex: none;
  }
}
