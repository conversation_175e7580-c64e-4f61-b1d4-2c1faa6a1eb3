import React from 'react';

const TestComponent: React.FC = () => {
  return (
    <div style={{ padding: '20px', background: '#f5f5f5', minHeight: '100vh' }}>
      <h1>测试组件</h1>
      <p>如果您能看到这个页面，说明基本的React渲染是正常的。</p>
      
      <div style={{ marginTop: '20px', padding: '15px', background: 'white', borderRadius: '8px' }}>
        <h2>系统检查</h2>
        <ul>
          <li>React 渲染: ✅ 正常</li>
          <li>CSS 样式: ✅ 正常</li>
          <li>ElectronAPI: {window.electronAPI ? '✅ 可用' : '❌ 不可用'}</li>
          <li>用户登录: {localStorage.getItem('user') ? '✅ 已登录' : '❌ 未登录'}</li>
        </ul>
      </div>

      <div style={{ marginTop: '20px', padding: '15px', background: 'white', borderRadius: '8px' }}>
        <h2>用户信息</h2>
        <pre style={{ background: '#f8f8f8', padding: '10px', borderRadius: '4px' }}>
          {localStorage.getItem('user') || '无用户信息'}
        </pre>
      </div>

      <div style={{ marginTop: '20px', padding: '15px', background: 'white', borderRadius: '8px' }}>
        <h2>ElectronAPI 方法</h2>
        {window.electronAPI ? (
          <ul>
            <li>getSystemMetrics: {typeof window.electronAPI.getSystemMetrics}</li>
            <li>getBackupStats: {typeof window.electronAPI.getBackupStats}</li>
            <li>getSystemInfo: {typeof window.electronAPI.getSystemInfo}</li>
          </ul>
        ) : (
          <p>ElectronAPI 不可用</p>
        )}
      </div>

      <button 
        onClick={() => window.location.reload()} 
        style={{
          marginTop: '20px',
          padding: '10px 20px',
          background: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '5px',
          cursor: 'pointer'
        }}
      >
        刷新页面
      </button>
    </div>
  );
};

export default TestComponent;
