/**
 * 企业级安装配置向导
 * 引导用户完成初始配置和系统设置
 */

import React, { useState, useEffect } from 'react';
import './SetupWizard.css';

interface SetupStep {
  id: string;
  title: string;
  description: string;
  component: React.ComponentType<any>;
  required: boolean;
  completed: boolean;
}

interface SystemConfig {
  // 基本设置
  organizationName: string;
  adminUser: {
    username: string;
    password: string;
    email: string;
    fullName: string;
  };
  
  // 数据库设置
  database: {
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
    ssl: boolean;
  };
  
  // 备份设置
  backup: {
    defaultPath: string;
    retentionDays: number;
    compressionEnabled: boolean;
    encryptionEnabled: boolean;
    encryptionKey?: string;
  };
  
  // 通知设置
  notifications: {
    email: {
      enabled: boolean;
      smtp: {
        host: string;
        port: number;
        secure: boolean;
        username: string;
        password: string;
      };
      from: string;
    };
    webhook: {
      enabled: boolean;
      url: string;
      secret: string;
    };
  };
  
  // 安全设置
  security: {
    sessionTimeout: number;
    passwordPolicy: {
      minLength: number;
      requireUppercase: boolean;
      requireLowercase: boolean;
      requireNumbers: boolean;
      requireSymbols: boolean;
    };
    twoFactorEnabled: boolean;
    auditLogging: boolean;
  };
  
  // 性能设置
  performance: {
    maxConcurrentBackups: number;
    compressionLevel: number;
    networkTimeout: number;
    memoryLimit: number;
  };
}

// 欢迎步骤组件
const WelcomeStep: React.FC<{ onNext: () => void }> = ({ onNext }) => {
  return (
    <div className="setup-step welcome-step">
      <div className="welcome-content">
        <h1>欢迎使用 MySQL 增量备份系统</h1>
        <p className="welcome-description">
          感谢您选择我们的企业级MySQL备份解决方案。本向导将帮助您完成系统的初始配置，
          包括管理员账户设置、数据库连接、备份策略、通知配置等重要设置。
        </p>
        
        <div className="features-grid">
          <div className="feature-item">
            <div className="feature-icon">🔒</div>
            <h3>企业级安全</h3>
            <p>多层安全防护，支持加密和审计</p>
          </div>
          <div className="feature-item">
            <div className="feature-icon">⚡</div>
            <h3>高性能备份</h3>
            <p>增量备份技术，节省时间和存储</p>
          </div>
          <div className="feature-item">
            <div className="feature-icon">📊</div>
            <h3>智能监控</h3>
            <p>实时监控和告警，确保数据安全</p>
          </div>
          <div className="feature-item">
            <div className="feature-icon">🔄</div>
            <h3>自动化管理</h3>
            <p>自动备份策略和恢复功能</p>
          </div>
        </div>
        
        <div className="setup-requirements">
          <h3>系统要求</h3>
          <ul>
            <li>Windows 10 或更高版本</li>
            <li>Node.js 16.0 或更高版本</li>
            <li>MySQL 5.7 或更高版本</li>
            <li>至少 4GB 可用内存</li>
            <li>至少 10GB 可用磁盘空间</li>
          </ul>
        </div>
        
        <button className="btn-primary btn-large" onClick={onNext}>
          开始配置
        </button>
      </div>
    </div>
  );
};

// 管理员账户设置步骤
const AdminSetupStep: React.FC<{
  config: SystemConfig;
  onUpdate: (config: Partial<SystemConfig>) => void;
  onNext: () => void;
}> = ({ config, onUpdate, onNext }) => {
  const [formData, setFormData] = useState(config.adminUser);
  const [confirmPassword, setConfirmPassword] = useState('');
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};
    
    if (!formData.username) newErrors.username = '用户名不能为空';
    if (formData.username.length < 3) newErrors.username = '用户名至少3个字符';
    
    if (!formData.password) newErrors.password = '密码不能为空';
    if (formData.password.length < 8) newErrors.password = '密码至少8个字符';
    
    if (formData.password !== confirmPassword) {
      newErrors.confirmPassword = '密码确认不匹配';
    }
    
    if (!formData.email) newErrors.email = '邮箱不能为空';
    if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = '邮箱格式不正确';
    
    if (!formData.fullName) newErrors.fullName = '姓名不能为空';
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      onUpdate({ adminUser: formData });
      onNext();
    }
  };

  return (
    <div className="setup-step admin-setup">
      <h2>创建管理员账户</h2>
      <p>请创建系统管理员账户，该账户将拥有系统的完全访问权限。</p>
      
      <div className="form-grid">
        <div className="form-group">
          <label>用户名 *</label>
          <input
            type="text"
            value={formData.username}
            onChange={(e) => setFormData({ ...formData, username: e.target.value })}
            placeholder="输入管理员用户名"
            className={errors.username ? 'error' : ''}
          />
          {errors.username && <span className="error-text">{errors.username}</span>}
        </div>
        
        <div className="form-group">
          <label>姓名 *</label>
          <input
            type="text"
            value={formData.fullName}
            onChange={(e) => setFormData({ ...formData, fullName: e.target.value })}
            placeholder="输入管理员姓名"
            className={errors.fullName ? 'error' : ''}
          />
          {errors.fullName && <span className="error-text">{errors.fullName}</span>}
        </div>
        
        <div className="form-group">
          <label>邮箱 *</label>
          <input
            type="email"
            value={formData.email}
            onChange={(e) => setFormData({ ...formData, email: e.target.value })}
            placeholder="输入管理员邮箱"
            className={errors.email ? 'error' : ''}
          />
          {errors.email && <span className="error-text">{errors.email}</span>}
        </div>
        
        <div className="form-group">
          <label>密码 *</label>
          <input
            type="password"
            value={formData.password}
            onChange={(e) => setFormData({ ...formData, password: e.target.value })}
            placeholder="输入密码（至少8位）"
            className={errors.password ? 'error' : ''}
          />
          {errors.password && <span className="error-text">{errors.password}</span>}
        </div>
        
        <div className="form-group">
          <label>确认密码 *</label>
          <input
            type="password"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            placeholder="再次输入密码"
            className={errors.confirmPassword ? 'error' : ''}
          />
          {errors.confirmPassword && <span className="error-text">{errors.confirmPassword}</span>}
        </div>
      </div>
      
      <div className="password-requirements">
        <h4>密码要求：</h4>
        <ul>
          <li className={formData.password.length >= 8 ? 'valid' : ''}>至少8个字符</li>
          <li className={/[A-Z]/.test(formData.password) ? 'valid' : ''}>包含大写字母</li>
          <li className={/[a-z]/.test(formData.password) ? 'valid' : ''}>包含小写字母</li>
          <li className={/\d/.test(formData.password) ? 'valid' : ''}>包含数字</li>
          <li className={/[!@#$%^&*]/.test(formData.password) ? 'valid' : ''}>包含特殊字符</li>
        </ul>
      </div>
      
      <div className="step-actions">
        <button className="btn-primary" onClick={handleSubmit}>
          下一步
        </button>
      </div>
    </div>
  );
};

// 数据库连接设置步骤
const DatabaseSetupStep: React.FC<{
  config: SystemConfig;
  onUpdate: (config: Partial<SystemConfig>) => void;
  onNext: () => void;
}> = ({ config, onUpdate, onNext }) => {
  const [formData, setFormData] = useState(config.database);
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);

  const testConnection = async () => {
    setTesting(true);
    setTestResult(null);
    
    try {
      // 模拟连接测试
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 模拟成功结果
      setTestResult({
        success: true,
        message: '数据库连接成功！'
      });
    } catch (error) {
      setTestResult({
        success: false,
        message: '连接失败：' + (error as Error).message
      });
    } finally {
      setTesting(false);
    }
  };

  const handleSubmit = () => {
    onUpdate({ database: formData });
    onNext();
  };

  return (
    <div className="setup-step database-setup">
      <h2>数据库连接设置</h2>
      <p>配置MySQL数据库连接信息，系统将使用此连接进行备份操作。</p>
      
      <div className="form-grid">
        <div className="form-group">
          <label>主机地址</label>
          <input
            type="text"
            value={formData.host}
            onChange={(e) => setFormData({ ...formData, host: e.target.value })}
            placeholder="localhost"
          />
        </div>
        
        <div className="form-group">
          <label>端口</label>
          <input
            type="number"
            value={formData.port}
            onChange={(e) => setFormData({ ...formData, port: parseInt(e.target.value) })}
            placeholder="3306"
          />
        </div>
        
        <div className="form-group">
          <label>用户名</label>
          <input
            type="text"
            value={formData.username}
            onChange={(e) => setFormData({ ...formData, username: e.target.value })}
            placeholder="数据库用户名"
          />
        </div>
        
        <div className="form-group">
          <label>密码</label>
          <input
            type="password"
            value={formData.password}
            onChange={(e) => setFormData({ ...formData, password: e.target.value })}
            placeholder="数据库密码"
          />
        </div>
        
        <div className="form-group">
          <label>数据库名</label>
          <input
            type="text"
            value={formData.database}
            onChange={(e) => setFormData({ ...formData, database: e.target.value })}
            placeholder="要备份的数据库名"
          />
        </div>
        
        <div className="form-group">
          <label>
            <input
              type="checkbox"
              checked={formData.ssl}
              onChange={(e) => setFormData({ ...formData, ssl: e.target.checked })}
            />
            启用SSL连接
          </label>
        </div>
      </div>
      
      <div className="connection-test">
        <button 
          className="btn-secondary" 
          onClick={testConnection}
          disabled={testing}
        >
          {testing ? '测试中...' : '测试连接'}
        </button>
        
        {testResult && (
          <div className={`test-result ${testResult.success ? 'success' : 'error'}`}>
            {testResult.message}
          </div>
        )}
      </div>
      
      <div className="step-actions">
        <button 
          className="btn-primary" 
          onClick={handleSubmit}
          disabled={!testResult?.success}
        >
          下一步
        </button>
      </div>
    </div>
  );
};

// 完成步骤组件
const CompletionStep: React.FC<{
  config: SystemConfig;
  onComplete: () => void;
}> = ({ config, onComplete }) => {
  const [installing, setInstalling] = useState(false);
  const [progress, setProgress] = useState(0);

  const startInstallation = async () => {
    setInstalling(true);
    
    // 模拟安装过程
    for (let i = 0; i <= 100; i += 10) {
      setProgress(i);
      await new Promise(resolve => setTimeout(resolve, 200));
    }
    
    setTimeout(() => {
      onComplete();
    }, 1000);
  };

  return (
    <div className="setup-step completion-step">
      <h2>配置完成</h2>
      <p>恭喜！您已完成所有必要的配置。点击下面的按钮开始安装系统。</p>
      
      <div className="config-summary">
        <h3>配置摘要</h3>
        <div className="summary-grid">
          <div className="summary-item">
            <strong>管理员账户：</strong>
            <span>{config.adminUser.username} ({config.adminUser.email})</span>
          </div>
          <div className="summary-item">
            <strong>数据库连接：</strong>
            <span>{config.database.host}:{config.database.port}</span>
          </div>
          <div className="summary-item">
            <strong>备份路径：</strong>
            <span>{config.backup.defaultPath}</span>
          </div>
          <div className="summary-item">
            <strong>保留天数：</strong>
            <span>{config.backup.retentionDays} 天</span>
          </div>
        </div>
      </div>
      
      {installing ? (
        <div className="installation-progress">
          <h3>正在安装系统...</h3>
          <div className="progress-bar">
            <div 
              className="progress-fill" 
              style={{ width: `${progress}%` }}
            ></div>
          </div>
          <p>{progress}% 完成</p>
        </div>
      ) : (
        <div className="step-actions">
          <button className="btn-primary btn-large" onClick={startInstallation}>
            开始安装
          </button>
        </div>
      )}
    </div>
  );
};

// 主向导组件
const SetupWizard: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [config, setConfig] = useState<SystemConfig>({
    organizationName: '',
    adminUser: {
      username: '',
      password: '',
      email: '',
      fullName: ''
    },
    database: {
      host: 'localhost',
      port: 3306,
      username: '',
      password: '',
      database: '',
      ssl: false
    },
    backup: {
      defaultPath: 'C:\\MySQL Backups',
      retentionDays: 30,
      compressionEnabled: true,
      encryptionEnabled: false
    },
    notifications: {
      email: {
        enabled: false,
        smtp: {
          host: '',
          port: 587,
          secure: false,
          username: '',
          password: ''
        },
        from: ''
      },
      webhook: {
        enabled: false,
        url: '',
        secret: ''
      }
    },
    security: {
      sessionTimeout: 30,
      passwordPolicy: {
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSymbols: true
      },
      twoFactorEnabled: false,
      auditLogging: true
    },
    performance: {
      maxConcurrentBackups: 3,
      compressionLevel: 6,
      networkTimeout: 30,
      memoryLimit: 1024
    }
  });

  const steps: SetupStep[] = [
    {
      id: 'welcome',
      title: '欢迎',
      description: '欢迎使用MySQL增量备份系统',
      component: WelcomeStep,
      required: true,
      completed: currentStep > 0
    },
    {
      id: 'admin',
      title: '管理员账户',
      description: '创建系统管理员账户',
      component: AdminSetupStep,
      required: true,
      completed: currentStep > 1
    },
    {
      id: 'database',
      title: '数据库连接',
      description: '配置MySQL数据库连接',
      component: DatabaseSetupStep,
      required: true,
      completed: currentStep > 2
    },
    {
      id: 'completion',
      title: '完成',
      description: '完成配置并安装系统',
      component: CompletionStep,
      required: true,
      completed: false
    }
  ];

  const updateConfig = (updates: Partial<SystemConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    // 这里应该保存配置并启动系统
    console.log('安装完成，配置：', config);
    alert('系统安装完成！');
  };

  const CurrentStepComponent = steps[currentStep].component;

  return (
    <div className="setup-wizard">
      <div className="wizard-header">
        <h1>MySQL 增量备份系统 - 安装向导</h1>
        <div className="step-indicator">
          {steps.map((step, index) => (
            <div 
              key={step.id}
              className={`step-item ${index === currentStep ? 'active' : ''} ${step.completed ? 'completed' : ''}`}
            >
              <div className="step-number">{index + 1}</div>
              <div className="step-info">
                <div className="step-title">{step.title}</div>
                <div className="step-description">{step.description}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      <div className="wizard-content">
        <CurrentStepComponent
          config={config}
          onUpdate={updateConfig}
          onNext={nextStep}
          onPrev={prevStep}
          onComplete={handleComplete}
        />
      </div>
      
      {currentStep > 0 && currentStep < steps.length - 1 && (
        <div className="wizard-footer">
          <button className="btn-secondary" onClick={prevStep}>
            上一步
          </button>
        </div>
      )}
    </div>
  );
};

export default SetupWizard;
