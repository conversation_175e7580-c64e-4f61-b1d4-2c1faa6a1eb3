<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#C2410C;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F97316;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gear3" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#FED7AA;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FB923C;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="db3" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#DBEAFE;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆角矩形 -->
  <rect width="256" height="256" rx="48" ry="48" fill="url(#bg3)"/>
  
  <!-- 齿轮背景 -->
  <g transform="translate(128, 128)">
    <!-- 齿轮外圈 -->
    <g fill="url(#gear3)" stroke="#C2410C" stroke-width="2">
      <!-- 齿轮齿 -->
      <rect x="-8" y="-60" width="16" height="20" rx="2"/>
      <rect x="-8" y="40" width="16" height="20" rx="2"/>
      <rect x="-60" y="-8" width="20" height="16" rx="2"/>
      <rect x="40" y="-8" width="20" height="16" rx="2"/>
      
      <!-- 对角齿 -->
      <rect x="-45" y="-45" width="16" height="16" rx="2" transform="rotate(45)"/>
      <rect x="29" y="-45" width="16" height="16" rx="2" transform="rotate(45)"/>
      <rect x="-45" y="29" width="16" height="16" rx="2" transform="rotate(45)"/>
      <rect x="29" y="29" width="16" height="16" rx="2" transform="rotate(45)"/>
      
      <!-- 齿轮主体 -->
      <circle cx="0" cy="0" r="40" fill="url(#gear3)" stroke="#C2410C" stroke-width="3"/>
    </g>
    
    <!-- 中心数据库 -->
    <g transform="translate(0, -5)">
      <!-- 数据库顶部 -->
      <ellipse cx="0" cy="-15" rx="20" ry="5" fill="#DBEAFE" stroke="#2563EB" stroke-width="1.5"/>
      
      <!-- 数据库主体 -->
      <rect x="-20" y="-15" width="40" height="20" fill="url(#db3)" stroke="#2563EB" stroke-width="1.5"/>
      
      <!-- 数据库底部 -->
      <ellipse cx="0" cy="5" rx="20" ry="5" fill="#93C5FD" stroke="#2563EB" stroke-width="1.5"/>
      
      <!-- 数据库分层 -->
      <ellipse cx="0" cy="-8" rx="20" ry="5" fill="none" stroke="#DBEAFE" stroke-width="1"/>
      <ellipse cx="0" cy="-1" rx="20" ry="5" fill="none" stroke="#DBEAFE" stroke-width="1"/>
    </g>
    
    <!-- 时钟指针 -->
    <g stroke="#FFFFFF" stroke-width="3" stroke-linecap="round">
      <line x1="0" y1="0" x2="0" y2="-25" opacity="0.8"/>
      <line x1="0" y1="0" x2="15" y2="0" opacity="0.6"/>
    </g>
    
    <!-- 中心点 -->
    <circle cx="0" cy="0" r="4" fill="#FFFFFF"/>
    
    <!-- 增量指示 -->
    <g transform="translate(35, -35)">
      <path d="M 0 8 L 8 0 L 16 8 L 12 8 L 12 16 L 4 16 L 4 8 Z" 
            fill="#10B981" stroke="#047857" stroke-width="1"/>
      <path d="M 4 12 L 8 8 L 12 12 L 10 12 L 10 14 L 6 14 L 6 12 Z" 
            fill="#34D399"/>
    </g>
    
    <!-- 标题 -->
    <text x="0" y="65" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="12" font-weight="bold" fill="#FFFFFF">AUTO</text>
  </g>
</svg>
