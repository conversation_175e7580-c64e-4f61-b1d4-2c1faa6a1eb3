# MySQL增量备份系统 - Windows安装脚本
# 企业级自动化安装程序

param(
    [string]$InstallPath = "C:\Program Files\MySQL Backup System",
    [string]$ServiceName = "MySQLBackupSystem",
    [switch]$Silent = $false,
    [switch]$CreateDesktopShortcut = $true,
    [switch]$StartService = $true
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 日志函数
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    Add-Content -Path "$env:TEMP\mysql-backup-install.log" -Value $logMessage
}

# 检查管理员权限
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 检查系统要求
function Test-SystemRequirements {
    Write-Log "检查系统要求..."
    
    # 检查操作系统版本
    $osVersion = [System.Environment]::OSVersion.Version
    if ($osVersion.Major -lt 10) {
        throw "需要Windows 10或更高版本"
    }
    
    # 检查.NET Framework
    $dotnetVersion = Get-ItemProperty "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" -Name Release -ErrorAction SilentlyContinue
    if (-not $dotnetVersion -or $dotnetVersion.Release -lt 461808) {
        Write-Log "需要安装.NET Framework 4.7.2或更高版本" "WARNING"
    }
    
    # 检查可用磁盘空间 (至少需要500MB)
    $drive = (Get-Item $InstallPath).PSDrive.Name
    $freeSpace = (Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='${drive}:'").FreeSpace
    if ($freeSpace -lt 500MB) {
        throw "安装驱动器空间不足，至少需要500MB"
    }
    
    Write-Log "系统要求检查通过"
}

# 下载依赖项
function Install-Dependencies {
    Write-Log "安装依赖项..."
    
    # 检查Node.js
    try {
        $nodeVersion = node --version
        Write-Log "检测到Node.js版本: $nodeVersion"
    }
    catch {
        Write-Log "未检测到Node.js，开始下载安装..." "WARNING"
        
        $nodeUrl = "https://nodejs.org/dist/v18.17.0/node-v18.17.0-x64.msi"
        $nodeInstaller = "$env:TEMP\nodejs-installer.msi"
        
        Invoke-WebRequest -Uri $nodeUrl -OutFile $nodeInstaller
        Start-Process msiexec.exe -ArgumentList "/i `"$nodeInstaller`" /quiet" -Wait
        
        # 更新PATH环境变量
        $env:PATH = [System.Environment]::GetEnvironmentVariable("PATH", "Machine") + ";" + [System.Environment]::GetEnvironmentVariable("PATH", "User")
        
        Remove-Item $nodeInstaller -Force
        Write-Log "Node.js安装完成"
    }
    
    # 检查MySQL客户端工具
    try {
        $mysqlVersion = mysql --version
        Write-Log "检测到MySQL客户端: $mysqlVersion"
    }
    catch {
        Write-Log "未检测到MySQL客户端工具，请手动安装MySQL客户端" "WARNING"
    }
}

# 创建安装目录
function New-InstallDirectory {
    Write-Log "创建安装目录: $InstallPath"
    
    if (Test-Path $InstallPath) {
        if (-not $Silent) {
            $response = Read-Host "安装目录已存在，是否覆盖? (y/N)"
            if ($response -ne "y" -and $response -ne "Y") {
                throw "安装已取消"
            }
        }
        Remove-Item $InstallPath -Recurse -Force
    }
    
    New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null
    
    # 创建子目录
    @("bin", "config", "logs", "backups", "temp") | ForEach-Object {
        New-Item -ItemType Directory -Path "$InstallPath\$_" -Force | Out-Null
    }
}

# 复制应用程序文件
function Copy-ApplicationFiles {
    Write-Log "复制应用程序文件..."
    
    $sourceDir = Split-Path -Parent $PSScriptRoot
    
    # 复制主要文件
    Copy-Item "$sourceDir\dist\*" "$InstallPath\bin\" -Recurse -Force
    Copy-Item "$sourceDir\package.json" "$InstallPath\" -Force
    Copy-Item "$sourceDir\README.md" "$InstallPath\" -Force
    
    # 复制配置文件模板
    if (Test-Path "$sourceDir\config\app.config.template.json") {
        Copy-Item "$sourceDir\config\app.config.template.json" "$InstallPath\config\app.config.json" -Force
    }
    
    Write-Log "应用程序文件复制完成"
}

# 安装Node.js依赖
function Install-NodeDependencies {
    Write-Log "安装Node.js依赖包..."
    
    Push-Location $InstallPath
    try {
        npm install --production --silent
        Write-Log "依赖包安装完成"
    }
    catch {
        Write-Log "依赖包安装失败: $_" "ERROR"
        throw
    }
    finally {
        Pop-Location
    }
}

# 创建Windows服务
function New-WindowsService {
    Write-Log "创建Windows服务..."
    
    # 检查服务是否已存在
    $existingService = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
    if ($existingService) {
        Write-Log "停止现有服务..."
        Stop-Service -Name $ServiceName -Force
        sc.exe delete $ServiceName
        Start-Sleep -Seconds 2
    }
    
    # 创建服务启动脚本
    $serviceScript = @"
@echo off
cd /d "$InstallPath"
node bin\main.js --service
"@
    
    $serviceScriptPath = "$InstallPath\service.bat"
    Set-Content -Path $serviceScriptPath -Value $serviceScript
    
    # 使用NSSM创建服务
    $nssmPath = "$InstallPath\bin\nssm.exe"
    if (-not (Test-Path $nssmPath)) {
        Write-Log "下载NSSM服务管理工具..."
        $nssmUrl = "https://nssm.cc/release/nssm-2.24.zip"
        $nssmZip = "$env:TEMP\nssm.zip"
        Invoke-WebRequest -Uri $nssmUrl -OutFile $nssmZip
        Expand-Archive -Path $nssmZip -DestinationPath "$env:TEMP\nssm" -Force
        Copy-Item "$env:TEMP\nssm\nssm-2.24\win64\nssm.exe" $nssmPath -Force
        Remove-Item $nssmZip -Force
        Remove-Item "$env:TEMP\nssm" -Recurse -Force
    }
    
    # 创建服务
    & $nssmPath install $ServiceName $serviceScriptPath
    & $nssmPath set $ServiceName DisplayName "MySQL增量备份系统"
    & $nssmPath set $ServiceName Description "企业级MySQL数据库增量备份解决方案"
    & $nssmPath set $ServiceName Start SERVICE_AUTO_START
    
    Write-Log "Windows服务创建完成"
}

# 配置防火墙
function Set-FirewallRules {
    Write-Log "配置防火墙规则..."
    
    try {
        # 允许应用程序通过防火墙
        New-NetFirewallRule -DisplayName "MySQL Backup System" -Direction Inbound -Program "$InstallPath\bin\main.exe" -Action Allow -ErrorAction SilentlyContinue
        New-NetFirewallRule -DisplayName "MySQL Backup System API" -Direction Inbound -Protocol TCP -LocalPort 3001 -Action Allow -ErrorAction SilentlyContinue
        
        Write-Log "防火墙规则配置完成"
    }
    catch {
        Write-Log "防火墙配置失败，请手动配置: $_" "WARNING"
    }
}

# 创建桌面快捷方式
function New-DesktopShortcut {
    if (-not $CreateDesktopShortcut) {
        return
    }
    
    Write-Log "创建桌面快捷方式..."
    
    $shell = New-Object -ComObject WScript.Shell
    $shortcut = $shell.CreateShortcut("$env:USERPROFILE\Desktop\MySQL备份系统.lnk")
    $shortcut.TargetPath = "$InstallPath\bin\mysql-backup-system.exe"
    $shortcut.WorkingDirectory = $InstallPath
    $shortcut.IconLocation = "$InstallPath\bin\icon.ico"
    $shortcut.Description = "MySQL增量备份系统"
    $shortcut.Save()
    
    Write-Log "桌面快捷方式创建完成"
}

# 创建开始菜单项
function New-StartMenuShortcut {
    Write-Log "创建开始菜单项..."
    
    $startMenuPath = "$env:APPDATA\Microsoft\Windows\Start Menu\Programs\MySQL备份系统"
    New-Item -ItemType Directory -Path $startMenuPath -Force | Out-Null
    
    $shell = New-Object -ComObject WScript.Shell
    $shortcut = $shell.CreateShortcut("$startMenuPath\MySQL备份系统.lnk")
    $shortcut.TargetPath = "$InstallPath\bin\mysql-backup-system.exe"
    $shortcut.WorkingDirectory = $InstallPath
    $shortcut.IconLocation = "$InstallPath\bin\icon.ico"
    $shortcut.Description = "MySQL增量备份系统"
    $shortcut.Save()
    
    # 创建卸载快捷方式
    $uninstallShortcut = $shell.CreateShortcut("$startMenuPath\卸载.lnk")
    $uninstallShortcut.TargetPath = "powershell.exe"
    $uninstallShortcut.Arguments = "-ExecutionPolicy Bypass -File `"$InstallPath\scripts\uninstall.ps1`""
    $uninstallShortcut.WorkingDirectory = $InstallPath
    $uninstallShortcut.Description = "卸载MySQL备份系统"
    $uninstallShortcut.Save()
    
    Write-Log "开始菜单项创建完成"
}

# 注册卸载信息
function Register-UninstallInfo {
    Write-Log "注册卸载信息..."
    
    $uninstallKey = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\MySQLBackupSystem"
    New-Item -Path $uninstallKey -Force | Out-Null
    
    Set-ItemProperty -Path $uninstallKey -Name "DisplayName" -Value "MySQL增量备份系统"
    Set-ItemProperty -Path $uninstallKey -Name "DisplayVersion" -Value "1.0.0"
    Set-ItemProperty -Path $uninstallKey -Name "Publisher" -Value "MySQL Backup Solutions"
    Set-ItemProperty -Path $uninstallKey -Name "InstallLocation" -Value $InstallPath
    Set-ItemProperty -Path $uninstallKey -Name "UninstallString" -Value "powershell.exe -ExecutionPolicy Bypass -File `"$InstallPath\scripts\uninstall.ps1`""
    Set-ItemProperty -Path $uninstallKey -Name "NoModify" -Value 1
    Set-ItemProperty -Path $uninstallKey -Name "NoRepair" -Value 1
    
    Write-Log "卸载信息注册完成"
}

# 启动服务
function Start-BackupService {
    if (-not $StartService) {
        return
    }
    
    Write-Log "启动备份服务..."
    
    try {
        Start-Service -Name $ServiceName
        Write-Log "服务启动成功"
    }
    catch {
        Write-Log "服务启动失败: $_" "ERROR"
    }
}

# 主安装流程
function Start-Installation {
    try {
        Write-Log "开始安装MySQL增量备份系统..."
        Write-Log "安装路径: $InstallPath"
        
        # 检查管理员权限
        if (-not (Test-Administrator)) {
            throw "需要管理员权限才能安装服务"
        }
        
        # 执行安装步骤
        Test-SystemRequirements
        Install-Dependencies
        New-InstallDirectory
        Copy-ApplicationFiles
        Install-NodeDependencies
        New-WindowsService
        Set-FirewallRules
        New-DesktopShortcut
        New-StartMenuShortcut
        Register-UninstallInfo
        Start-BackupService
        
        Write-Log "安装完成！" "SUCCESS"
        Write-Log "应用程序已安装到: $InstallPath"
        Write-Log "服务名称: $ServiceName"
        
        if (-not $Silent) {
            Write-Host "`n安装成功完成！" -ForegroundColor Green
            Write-Host "您可以通过以下方式启动应用程序：" -ForegroundColor Yellow
            Write-Host "1. 双击桌面快捷方式" -ForegroundColor White
            Write-Host "2. 从开始菜单启动" -ForegroundColor White
            Write-Host "3. 访问 http://localhost:3000" -ForegroundColor White
            
            $response = Read-Host "`n是否现在启动应用程序? (Y/n)"
            if ($response -ne "n" -and $response -ne "N") {
                Start-Process "$InstallPath\bin\mysql-backup-system.exe"
            }
        }
        
    }
    catch {
        Write-Log "安装失败: $_" "ERROR"
        Write-Host "安装失败: $_" -ForegroundColor Red
        exit 1
    }
}

# 显示帮助信息
function Show-Help {
    Write-Host @"
MySQL增量备份系统 - 安装脚本

用法: .\install.ps1 [参数]

参数:
  -InstallPath <路径>        安装路径 (默认: C:\Program Files\MySQL Backup System)
  -ServiceName <名称>        Windows服务名称 (默认: MySQLBackupSystem)
  -Silent                    静默安装，不显示交互提示
  -CreateDesktopShortcut     创建桌面快捷方式 (默认: true)
  -StartService              安装后启动服务 (默认: true)
  -Help                      显示此帮助信息

示例:
  .\install.ps1
  .\install.ps1 -InstallPath "D:\MySQL Backup" -Silent
  .\install.ps1 -ServiceName "MyBackupService" -StartService:$false

"@ -ForegroundColor Cyan
}

# 脚本入口点
if ($args -contains "-Help" -or $args -contains "--help" -or $args -contains "/?") {
    Show-Help
    exit 0
}

# 开始安装
Start-Installation
