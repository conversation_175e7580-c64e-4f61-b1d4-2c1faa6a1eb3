<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg4" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#581C87;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#A855F7;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="folder1" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#E9D5FF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#A855F7;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="folder2" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#DDD6FE;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="folder3" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#C4B5FD;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆角矩形 -->
  <rect width="256" height="256" rx="48" ry="48" fill="url(#bg4)"/>
  
  <!-- 文件夹层叠 -->
  <g transform="translate(128, 128)">
    <!-- 后层文件夹 -->
    <g transform="translate(15, 15)">
      <path d="M -50 -20 L -30 -35 L 50 -35 L 50 25 L -50 25 Z" 
            fill="url(#folder3)" stroke="#6D28D9" stroke-width="2" opacity="0.7"/>
      <rect x="-30" y="-35" width="20" height="15" rx="3" 
            fill="url(#folder3)" stroke="#6D28D9" stroke-width="2" opacity="0.7"/>
    </g>
    
    <!-- 中层文件夹 -->
    <g transform="translate(8, 8)">
      <path d="M -50 -20 L -30 -35 L 50 -35 L 50 25 L -50 25 Z" 
            fill="url(#folder2)" stroke="#7C3AED" stroke-width="2" opacity="0.8"/>
      <rect x="-30" y="-35" width="20" height="15" rx="3" 
            fill="url(#folder2)" stroke="#7C3AED" stroke-width="2" opacity="0.8"/>
    </g>
    
    <!-- 前层文件夹 -->
    <g>
      <path d="M -50 -20 L -30 -35 L 50 -35 L 50 25 L -50 25 Z" 
            fill="url(#folder1)" stroke="#8B5CF6" stroke-width="2"/>
      <rect x="-30" y="-35" width="20" height="15" rx="3" 
            fill="url(#folder1)" stroke="#8B5CF6" stroke-width="2"/>
      
      <!-- 文件夹内容指示 -->
      <g transform="translate(0, -5)">
        <!-- 数据库图标 -->
        <ellipse cx="0" cy="-8" rx="15" ry="3" fill="#581C87"/>
        <rect x="-15" y="-8" width="30" height="12" fill="#6D28D9"/>
        <ellipse cx="0" cy="4" rx="15" ry="3" fill="#581C87"/>
        
        <!-- 分层线 -->
        <ellipse cx="0" cy="-3" rx="15" ry="3" fill="none" stroke="#E9D5FF" stroke-width="1"/>
        <ellipse cx="0" cy="1" rx="15" ry="3" fill="none" stroke="#E9D5FF" stroke-width="1"/>
      </g>
    </g>
    
    <!-- 增量箭头 -->
    <g transform="translate(60, -40)">
      <path d="M 0 15 L 10 0 L 20 15 L 15 15 L 15 25 L 5 25 L 5 15 Z" 
            fill="#10B981" stroke="#047857" stroke-width="2"/>
      
      <!-- 版本号指示 -->
      <circle cx="10" cy="30" r="8" fill="#FBBF24" stroke="#D97706" stroke-width="1"/>
      <text x="10" y="34" text-anchor="middle" font-family="Arial, sans-serif" 
            font-size="8" font-weight="bold" fill="#92400E">v3</text>
    </g>
    
    <!-- 连接线 -->
    <g stroke="#E9D5FF" stroke-width="2" stroke-dasharray="3,3" opacity="0.6">
      <line x1="8" y1="8" x2="15" y2="15"/>
      <line x1="15" y1="15" x2="22" y2="22"/>
    </g>
    
    <!-- 标题 -->
    <text x="0" y="55" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="12" font-weight="bold" fill="#FFFFFF">VERSIONS</text>
  </g>
</svg>
