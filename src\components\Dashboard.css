.dashboard {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--bg-primary);
  font-family: 'Segoe UI', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
  overflow: hidden;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 50%, var(--primary-active) 100%);
  color: var(--text-inverse);
  padding: var(--spacing-2xl) var(--spacing-4xl);
  box-shadow: var(--shadow-lg);
  min-height: 88px;
  position: relative;
  z-index: 10;
}

.dashboard-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

.header-left {
  flex: 1;
  position: relative;
  z-index: 1;
}

.app-branding {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.app-title {
  margin: 0;
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-inverse);
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.5px;
  line-height: var(--line-height-tight);
}

.app-subtitle {
  font-size: var(--font-size-base);
  color: rgba(255, 255, 255, 0.85);
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.app-subtitle::before {
  content: '●';
  color: rgba(255, 255, 255, 0.6);
  font-size: var(--font-size-sm);
}

.header-right {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
}

.user-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
  background: rgba(255, 255, 255, 0.1);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-xs);
}

.welcome-text {
  font-size: var(--font-size-base);
  color: var(--text-inverse);
  font-weight: var(--font-weight-semibold);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.user-role {
  font-size: var(--font-size-xs);
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.15);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: var(--font-weight-semibold);
}

.logout-button {
  background: rgba(255, 255, 255, 0.15);
  color: var(--text-inverse);
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-lg);
  cursor: pointer;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  transition: all var(--transition-normal);
  backdrop-filter: blur(20px);
  min-height: auto;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.logout-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.logout-button:hover::before {
  left: 100%;
}

.logout-button:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.dashboard-nav {
  display: flex;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: 0 var(--spacing-4xl);
  overflow-x: auto;
  min-height: 56px;
  box-shadow: var(--shadow-md);
  position: relative;
  z-index: 5;
}

.nav-button {
  background: none;
  border: none;
  padding: var(--spacing-lg) var(--spacing-xl);
  cursor: pointer;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  border-bottom: 3px solid transparent;
  transition: all var(--transition-normal);
  white-space: nowrap;
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  min-height: auto;
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  margin: 0 var(--spacing-xs);
}

.nav-button::before {
  content: "";
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  background: transparent;
  transition: all var(--transition-normal);
  flex-shrink: 0;
}

.nav-button:hover {
  color: var(--primary-color);
  background: var(--primary-light);
  transform: translateY(-1px);
}

.nav-button:hover::before {
  background: var(--primary-color);
  box-shadow: 0 0 8px rgba(0, 120, 212, 0.4);
}

.nav-button.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background: var(--primary-light);
  font-weight: var(--font-weight-semibold);
  box-shadow: inset 0 -3px 0 var(--primary-color);
}

.nav-button.active::before {
  background: var(--primary-color);
  box-shadow: 0 0 12px rgba(0, 120, 212, 0.6);
}

/* 导航按钮图标 */
.nav-button .icon {
  font-size: var(--font-size-md);
  transition: transform var(--transition-normal);
}

.nav-button:hover .icon {
  transform: scale(1.1);
}

.nav-button.active .icon {
  transform: scale(1.15);
}

.dashboard-content {
  flex: 1;
  padding: var(--spacing-3xl) var(--spacing-4xl);
  overflow-y: auto;
  background: var(--bg-primary);
  position: relative;
}

.dashboard-content::-webkit-scrollbar {
  width: 8px;
}

.dashboard-content::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
}

.dashboard-content::-webkit-scrollbar-thumb {
  background: var(--border-hover);
  border-radius: var(--radius-md);
  border: 2px solid var(--bg-primary);
}

.dashboard-content::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

.content-panel {
  max-width: 1400px;
  margin: 0 auto;
  animation: fadeInUp var(--transition-slow);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.content-panel h2 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-2xl);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.content-panel h2::before {
  content: '';
  width: 4px;
  height: 24px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  border-radius: var(--radius-sm);
}

.backup-section,
.servers-section,
.history-section,
.settings-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
}

.info-card {
  background: var(--bg-secondary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-3xl);
  box-shadow: var(--shadow-card);
  border: 1px solid var(--border-color);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover), var(--info-color));
}

.info-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-card-hover);
  border-color: var(--border-hover);
}

.info-card h3 {
  color: var(--text-primary);
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.info-card h3::before {
  content: '●';
  color: var(--primary-color);
  font-size: var(--font-size-sm);
}

.info-card p {
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-xl) 0;
  line-height: var(--line-height-relaxed);
  font-size: var(--font-size-base);
}

.action-buttons {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
  align-items: center;
}

/* 按钮样式已在 index.css 中定义，这里只需要特定的覆盖 */

.user-details {
  background: var(--bg-tertiary);
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
  border: 1px solid var(--border-color);
}

.user-details p {
  margin: 6px 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.user-details strong {
  color: var(--text-primary);
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-header {
    padding: 15px 20px;
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .header-left h1 {
    font-size: 20px;
  }

  .dashboard-nav {
    padding: 0 20px;
  }

  .nav-button {
    padding: 12px 16px;
    font-size: 13px;
  }

  .dashboard-content {
    padding: 20px;
  }

  .content-panel h2 {
    font-size: 24px;
  }

  .info-card {
    padding: 20px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .primary-button,
  .secondary-button {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    padding: 12px 15px;
  }

  .dashboard-nav {
    padding: 0 15px;
  }

  .dashboard-content {
    padding: 15px;
  }

  .info-card {
    padding: 15px;
  }
}

/* 服务器管理样式 */
.server-management {
  padding: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  color: #333;
}

.add-server-form {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.add-server-form h4 {
  margin: 0 0 20px 0;
  color: #333;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 15px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group select {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.ssh-section {
  border-top: 1px solid #e9ecef;
  padding-top: 20px;
  margin-top: 20px;
}

.ssh-section h5 {
  margin: 0 0 15px 0;
  color: #666;
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.servers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.server-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.server-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.server-header h4 {
  margin: 0;
  color: #333;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.active {
  background: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background: #f8d7da;
  color: #721c24;
}

.status-badge.error {
  background: #f8d7da;
  color: #721c24;
}

.server-details p {
  margin: 8px 0;
  color: #666;
  font-size: 14px;
}

.server-actions {
  margin-top: 15px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.server-actions button {
  padding: 6px 12px;
  font-size: 12px;
  min-width: 70px;
}

/* 输入框与按钮组合 */
.input-with-button {
  display: flex;
  gap: 8px;
  align-items: center;
}

.input-with-button input {
  flex: 1;
}

.input-with-button button {
  padding: 8px 12px;
  font-size: 12px;
  white-space: nowrap;
}

/* 备份任务管理样式 */
.backup-management {
  padding: 20px;
}

.add-task-form {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.tasks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.task-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.task-header h4 {
  margin: 0;
  color: #333;
}

.status-badge.running {
  background: #fff3cd;
  color: #856404;
}

.task-details p {
  margin: 8px 0;
  color: #666;
  font-size: 14px;
}

.task-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

/* 备份历史样式 */
.backup-history {
  padding: 20px;
}

.controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.limit-select {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.history-table {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1.5fr 0.8fr 0.8fr 0.8fr 1fr 1.5fr;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 15px;
  font-weight: 600;
  color: #333;
}

.table-body {
  max-height: 600px;
  overflow-y: auto;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1.5fr 0.8fr 0.8fr 0.8fr 1fr 1.5fr;
  padding: 15px;
  border-bottom: 1px solid #f1f3f4;
  align-items: center;
}

.table-row:hover {
  background: #f8f9fa;
}

.task-info,
.server-info,
.time-info {
  display: flex;
  flex-direction: column;
}

.task-name,
.server-name,
.start-time {
  font-weight: 500;
  color: #333;
}

.file-path,
.server-host,
.end-time {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.backup-type {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

.backup-type.full {
  background: #e3f2fd;
  color: #1565c0;
}

.backup-type.incremental {
  background: #f3e5f5;
  color: #7b1fa2;
}

.status-badge.status-running {
  background: #fff3cd;
  color: #856404;
}

.status-badge.status-completed {
  background: #d4edda;
  color: #155724;
}

.status-badge.status-failed {
  background: #f8d7da;
  color: #721c24;
}

.error-tooltip {
  display: inline-block;
  margin-left: 5px;
  cursor: help;
}

.history-stats {
  margin-top: 20px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.stat-value {
  display: block;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.stat-value.success {
  color: #28a745;
}

.stat-value.failed {
  color: #dc3545;
}

/* 通用样式 */
.empty-state {
  text-align: center;
  padding: 40px;
  color: #666;
}

.empty-state p {
  margin: 10px 0;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
  border: 1px solid #f5c6cb;
}

.success-message {
  background: #d4edda;
  color: #155724;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
  border: 1px solid #c3e6cb;
}

.warning-message {
  background: #fff3cd;
  color: #856404;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
  border: 1px solid #ffeaa7;
}

.danger-button {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.danger-button:hover {
  background: #c82333;
  transform: translateY(-1px);
}

.danger-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.info-button {
  background: #17a2b8;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.info-button:hover {
  background: #138496;
  transform: translateY(-1px);
}

.info-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}
