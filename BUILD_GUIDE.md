# MySQL增量备份管理器 - 构建指南

## 环境要求

### 系统要求
- Windows 10/11 (64位)
- Node.js 18+ 
- npm 8+

### 开发工具
- Visual Studio Code (推荐)
- Git

## 安装依赖

```bash
# 安装项目依赖
npm install

# 安装开发依赖 (如果需要)
npm install --save-dev concurrently wait-on
```

## 开发模式

```bash
# 启动开发服务器
npm run dev

# 启动Electron开发模式 (需要另开终端)
npm run electron:dev
```

## 图标准备

### 1. 图标文件要求
- 主图标: `public/mysql-backup-icon.ico` (Windows应用图标)
- 源文件: `public/mysql-backup-icon.svg` (已提供)
- 备用: `public/mysql-backup-icon.png`

### 2. 图标转换步骤
1. 打开在线转换工具: https://convertio.co/svg-ico/
2. 上传 `public/mysql-backup-icon.svg`
3. 选择输出格式为 ICO
4. 设置包含多种尺寸: 256, 128, 64, 48, 32, 16
5. 下载转换后的文件
6. 重命名为 `mysql-backup-icon.ico`
7. 放入 `public/` 目录

### 3. 图标设计建议
参考 `public/ICON_README.md` 中的设计方案，选择最适合的风格。

## 构建打包

### 1. 构建渲染进程
```bash
npm run build:renderer
```

### 2. 构建主进程
```bash
npm run build:electron
```

### 3. 完整构建
```bash
# 构建所有并打包
npm run build

# 仅构建Windows版本
npm run dist:win

# 构建64位Windows版本
npm run dist:win64

# 构建便携版
npm run pack:win
```

## 打包输出

### 输出目录
- `dist/` - 打包后的安装程序
- `dist-electron/` - 编译后的Electron代码
- `release/` - 发布版本 (如果使用electron-builder.json5配置)

### 输出文件
- `MySQL增量备份管理器-Windows-1.0.0-Setup.exe` - 安装程序
- `MySQL增量备份管理器-Windows-1.0.0-Portable.exe` - 便携版

## 安装程序特性

### NSIS安装程序功能
- ✅ 支持自定义安装目录
- ✅ 创建桌面快捷方式
- ✅ 创建开始菜单快捷方式
- ✅ 支持卸载程序
- ✅ 多语言支持 (中文/英文)
- ✅ 检测并卸载旧版本
- ✅ 用户数据保护

### 便携版特性
- ✅ 无需安装，直接运行
- ✅ 配置文件保存在程序目录
- ✅ 适合临时使用或测试

## 常见问题

### 1. 构建失败
```bash
# 清理缓存
npm run clean
rm -rf node_modules
npm install

# 重新构建
npm run build
```

### 2. 图标不显示
- 确保 `mysql-backup-icon.ico` 文件存在
- 检查文件路径是否正确
- 确保ICO文件包含多种尺寸

### 3. 安装程序问题
- 检查 `LICENSE` 文件是否存在
- 确保 `build/installer.nsh` 文件存在
- 检查electron-builder配置

### 4. 依赖问题
```bash
# 重建原生模块
npm run postinstall

# 或手动重建
./node_modules/.bin/electron-builder install-app-deps
```

## 发布流程

### 1. 版本更新
```bash
# 更新版本号
npm version patch  # 1.0.0 -> 1.0.1
npm version minor  # 1.0.0 -> 1.1.0
npm version major  # 1.0.0 -> 2.0.0
```

### 2. 构建发布版
```bash
npm run dist:win
```

### 3. 测试安装程序
1. 在干净的Windows系统上测试安装
2. 验证所有功能正常
3. 测试卸载程序

### 4. 发布
1. 上传到发布平台
2. 编写发布说明
3. 通知用户更新

## 自定义配置

### 修改应用信息
编辑 `package.json` 中的以下字段:
- `name`: 应用名称
- `version`: 版本号
- `description`: 应用描述
- `author`: 作者信息

### 修改构建配置
编辑 `electron-builder.json5` 或 `package.json` 中的 `build` 字段。

### 添加自定义安装脚本
编辑 `build/installer.nsh` 文件。

## 技术支持

如遇到构建问题，请检查:
1. Node.js 版本是否符合要求
2. 依赖是否正确安装
3. 图标文件是否准备完整
4. 系统环境是否满足要求
