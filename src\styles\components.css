/* 企业级通用组件样式 */

/* 面包屑导航 */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.breadcrumb-item:not(:last-child)::after {
  content: '/';
  color: var(--text-tertiary);
  font-weight: var(--font-weight-normal);
}

.breadcrumb-link {
  color: var(--text-link);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.breadcrumb-link:hover {
  color: var(--text-link-hover);
}

.breadcrumb-current {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

/* 分页组件 */
.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  margin: var(--spacing-2xl) 0;
}

.pagination-button {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
  min-width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-button:hover:not(:disabled) {
  background: var(--bg-hover);
  color: var(--text-primary);
  border-color: var(--border-hover);
}

.pagination-button.active {
  background: var(--primary-color);
  color: var(--text-inverse);
  border-color: var(--primary-color);
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0 var(--spacing-lg);
}

/* 搜索框 */
.search-box {
  position: relative;
  display: inline-block;
  width: 100%;
  max-width: 400px;
}

.search-input {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg) var(--spacing-md) var(--spacing-5xl);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  background: var(--bg-secondary);
  color: var(--text-primary);
  transition: all var(--transition-normal);
}

.search-input:focus {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.1);
  outline: none;
}

.search-icon {
  position: absolute;
  left: var(--spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-tertiary);
  font-size: var(--font-size-lg);
  pointer-events: none;
}

.search-clear {
  position: absolute;
  right: var(--spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.search-clear:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

/* 标签页 */
.tabs {
  border-bottom: 1px solid var(--border-color);
  margin-bottom: var(--spacing-2xl);
}

.tabs-list {
  display: flex;
  gap: 0;
  list-style: none;
  margin: 0;
  padding: 0;
}

.tab-button {
  padding: var(--spacing-lg) var(--spacing-xl);
  border: none;
  background: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  border-bottom: 3px solid transparent;
  transition: all var(--transition-normal);
  position: relative;
}

.tab-button:hover {
  color: var(--text-primary);
  background: var(--bg-hover);
}

.tab-button.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background: var(--primary-light);
}

.tab-content {
  animation: fadeInUp var(--transition-normal) ease-out;
}

/* 折叠面板 */
.accordion {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.accordion-item {
  border-bottom: 1px solid var(--border-color);
}

.accordion-item:last-child {
  border-bottom: none;
}

.accordion-header {
  padding: var(--spacing-lg) var(--spacing-xl);
  background: var(--bg-tertiary);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color var(--transition-fast);
}

.accordion-header:hover {
  background: var(--bg-hover);
}

.accordion-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.accordion-icon {
  color: var(--text-secondary);
  transition: transform var(--transition-normal);
}

.accordion-item.open .accordion-icon {
  transform: rotate(180deg);
}

.accordion-content {
  padding: var(--spacing-xl);
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
}

/* 步骤指示器 */
.stepper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: var(--spacing-2xl) 0;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 16px;
  left: 60%;
  right: -40%;
  height: 2px;
  background: var(--border-color);
  z-index: 1;
}

.step.completed:not(:last-child)::after {
  background: var(--primary-color);
}

.step-indicator {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  background: var(--bg-tertiary);
  border: 2px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  position: relative;
  z-index: 2;
  transition: all var(--transition-normal);
}

.step.active .step-indicator {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--text-inverse);
}

.step.completed .step-indicator {
  background: var(--success-color);
  border-color: var(--success-color);
  color: var(--text-inverse);
}

.step-label {
  margin-top: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  text-align: center;
}

.step.active .step-label {
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
}

/* 状态指示器 */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-indicator::before {
  content: '';
  width: 6px;
  height: 6px;
  border-radius: var(--radius-full);
}

.status-indicator.online {
  background: var(--success-light);
  color: var(--success-color);
}

.status-indicator.online::before {
  background: var(--success-color);
  animation: pulse 2s infinite;
}

.status-indicator.offline {
  background: var(--secondary-light);
  color: var(--secondary-color);
}

.status-indicator.offline::before {
  background: var(--secondary-color);
}

.status-indicator.error {
  background: var(--danger-light);
  color: var(--danger-color);
}

.status-indicator.error::before {
  background: var(--danger-color);
  animation: pulse 2s infinite;
}

.status-indicator.warning {
  background: var(--warning-light);
  color: var(--warning-color);
}

.status-indicator.warning::before {
  background: var(--warning-color);
  animation: pulse 2s infinite;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: var(--spacing-5xl);
  color: var(--text-secondary);
}

.empty-state-icon {
  font-size: 64px;
  margin-bottom: var(--spacing-lg);
  opacity: 0.5;
}

.empty-state-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.empty-state-description {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: var(--line-height-relaxed);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.empty-state-action {
  margin-top: var(--spacing-xl);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pagination {
    flex-wrap: wrap;
    gap: var(--spacing-xs);
  }
  
  .tabs-list {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .stepper {
    flex-direction: column;
    gap: var(--spacing-lg);
  }
  
  .step:not(:last-child)::after {
    display: none;
  }
  
  .search-box {
    max-width: 100%;
  }
}
