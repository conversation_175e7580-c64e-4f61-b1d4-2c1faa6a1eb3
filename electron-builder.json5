// @see - https://www.electron.build/configuration/configuration
{
  "$schema": "https://raw.githubusercontent.com/electron-userland/electron-builder/master/packages/app-builder-lib/scheme.json",
  "appId": "com.mysqlbackup.manager",
  "asar": true,
  "productName": "MySQL增量备份管理器",
  "copyright": "Copyright © 2024 MySQL Backup Manager Team",
  "directories": {
    "buildResources": "build",
    "output": "dist"
  },
  "files": [
    "dist/**/*",
    "dist-electron/**/*",
    "public/mysql-backup-icon.png",
    "node_modules/**/*",
    "!node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}",
    "!node_modules/*/{test,__tests__,tests,powered-test,example,examples}",
    "!node_modules/*.d.ts",
    "!node_modules/.bin",
    "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}",
    "!.editorconfig",
    "!**/._*",
    "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}",
    "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}",
    "!**/{appveyor.yml,.travis.yml,circle.yml}",
    "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"
  ],
  "extraResources": [
    {
      "from": "public/",
      "to": "public/",
      "filter": ["*.png", "*.ico", "*.svg", "*.bmp"]
    }
  ],
  "mac": {
    "target": [
      "dmg"
    ],
    "artifactName": "${productName}-Mac-${version}-Installer.${ext}"
  },
  "win": {
    "icon": "public/mysql-backup-icon_256x256.ico",
    "target": [
      {
        "target": "nsis",
        "arch": ["x64"]
      },
      {
        "target": "portable",
        "arch": ["x64"]
      }
    ],
    "requestedExecutionLevel": "asInvoker",
    "artifactName": "${productName}-Windows-${version}-Setup.${ext}",
    "publisherName": "MySQL Backup Manager Team",
    "verifyUpdateCodeSignature": false
  },
  "nsis": {
    "oneClick": false,
    "perMachine": false,
    "allowToChangeInstallationDirectory": true,
    "deleteAppDataOnUninstall": false,
    "createDesktopShortcut": true,
    "createStartMenuShortcut": true,
    "installerIcon": "public/mysql-backup-icon_256x256.ico",
    "uninstallerIcon": "public/mysql-backup-icon_256x256.ico",
    "installerHeaderIcon": "public/mysql-backup-icon_256x256.ico",
    "license": "LICENSE",
    "warningsAsErrors": false,
    "displayLanguageSelector": true,
    "installerLanguages": ["zh_CN", "en_US"],
    "language": "2052"
  },
  "portable": {
    "artifactName": "${productName}-Windows-${version}-Portable.${ext}"
  },
  "linux": {
    "target": [
      "AppImage"
    ],
    "artifactName": "${productName}-Linux-${version}.${ext}"
  }
}
