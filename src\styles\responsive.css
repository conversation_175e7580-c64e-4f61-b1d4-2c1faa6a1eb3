/* 企业级响应式布局样式 */

/* 断点定义 */
:root {
  --breakpoint-xs: 480px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* 容器样式 */
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.container-sm {
  max-width: 640px;
}

.container-md {
  max-width: 768px;
}

.container-lg {
  max-width: 1024px;
}

.container-xl {
  max-width: 1280px;
}

.container-2xl {
  max-width: 1536px;
}

/* 网格系统 */
.grid {
  display: grid;
  gap: var(--spacing-lg);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
.grid-cols-12 { grid-template-columns: repeat(12, 1fr); }

.col-span-1 { grid-column: span 1; }
.col-span-2 { grid-column: span 2; }
.col-span-3 { grid-column: span 3; }
.col-span-4 { grid-column: span 4; }
.col-span-5 { grid-column: span 5; }
.col-span-6 { grid-column: span 6; }
.col-span-full { grid-column: 1 / -1; }

/* Flexbox 工具类 */
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.items-center { align-items: center; }
.items-baseline { align-items: baseline; }
.items-stretch { align-items: stretch; }

.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-initial { flex: 0 1 auto; }
.flex-none { flex: none; }

/* 间距工具类 */
.gap-0 { gap: 0; }
.gap-1 { gap: var(--spacing-xs); }
.gap-2 { gap: var(--spacing-sm); }
.gap-3 { gap: var(--spacing-md); }
.gap-4 { gap: var(--spacing-lg); }
.gap-5 { gap: var(--spacing-xl); }
.gap-6 { gap: var(--spacing-2xl); }
.gap-8 { gap: var(--spacing-3xl); }

/* 显示/隐藏工具类 */
.hidden { display: none; }
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }

/* 移动端优先响应式 */
@media (min-width: 480px) {
  .xs\:block { display: block; }
  .xs\:hidden { display: none; }
  .xs\:flex { display: flex; }
  .xs\:grid { display: grid; }
  
  .xs\:grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
  .xs\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .xs\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  
  .xs\:flex-col { flex-direction: column; }
  .xs\:flex-row { flex-direction: row; }
  
  .xs\:justify-start { justify-content: flex-start; }
  .xs\:justify-center { justify-content: center; }
  .xs\:justify-end { justify-content: flex-end; }
  .xs\:justify-between { justify-content: space-between; }
}

@media (min-width: 640px) {
  .sm\:block { display: block; }
  .sm\:hidden { display: none; }
  .sm\:flex { display: flex; }
  .sm\:grid { display: grid; }
  
  .sm\:grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .sm\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .sm\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  
  .sm\:col-span-1 { grid-column: span 1; }
  .sm\:col-span-2 { grid-column: span 2; }
  .sm\:col-span-3 { grid-column: span 3; }
  .sm\:col-span-4 { grid-column: span 4; }
  
  .sm\:flex-col { flex-direction: column; }
  .sm\:flex-row { flex-direction: row; }
  
  .sm\:justify-start { justify-content: flex-start; }
  .sm\:justify-center { justify-content: center; }
  .sm\:justify-end { justify-content: flex-end; }
  .sm\:justify-between { justify-content: space-between; }
  
  .sm\:items-start { align-items: flex-start; }
  .sm\:items-center { align-items: center; }
  .sm\:items-end { align-items: flex-end; }
}

@media (min-width: 768px) {
  .md\:block { display: block; }
  .md\:hidden { display: none; }
  .md\:flex { display: flex; }
  .md\:grid { display: grid; }
  
  .md\:grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .md\:grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
  
  .md\:col-span-1 { grid-column: span 1; }
  .md\:col-span-2 { grid-column: span 2; }
  .md\:col-span-3 { grid-column: span 3; }
  .md\:col-span-4 { grid-column: span 4; }
  .md\:col-span-6 { grid-column: span 6; }
  
  .md\:flex-col { flex-direction: column; }
  .md\:flex-row { flex-direction: row; }
  
  .md\:justify-start { justify-content: flex-start; }
  .md\:justify-center { justify-content: center; }
  .md\:justify-end { justify-content: flex-end; }
  .md\:justify-between { justify-content: space-between; }
  
  .md\:items-start { align-items: flex-start; }
  .md\:items-center { align-items: center; }
  .md\:items-end { align-items: flex-end; }
}

@media (min-width: 1024px) {
  .lg\:block { display: block; }
  .lg\:hidden { display: none; }
  .lg\:flex { display: flex; }
  .lg\:grid { display: grid; }
  
  .lg\:grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
  .lg\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .lg\:grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
  .lg\:grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
  
  .lg\:col-span-1 { grid-column: span 1; }
  .lg\:col-span-2 { grid-column: span 2; }
  .lg\:col-span-3 { grid-column: span 3; }
  .lg\:col-span-4 { grid-column: span 4; }
  .lg\:col-span-5 { grid-column: span 5; }
  .lg\:col-span-6 { grid-column: span 6; }
  
  .lg\:flex-col { flex-direction: column; }
  .lg\:flex-row { flex-direction: row; }
  
  .lg\:justify-start { justify-content: flex-start; }
  .lg\:justify-center { justify-content: center; }
  .lg\:justify-end { justify-content: flex-end; }
  .lg\:justify-between { justify-content: space-between; }
  
  .lg\:items-start { align-items: flex-start; }
  .lg\:items-center { align-items: center; }
  .lg\:items-end { align-items: flex-end; }
}

@media (min-width: 1280px) {
  .xl\:block { display: block; }
  .xl\:hidden { display: none; }
  .xl\:flex { display: flex; }
  .xl\:grid { display: grid; }
  
  .xl\:grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
  .xl\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .xl\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .xl\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .xl\:grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
  .xl\:grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
  .xl\:grid-cols-12 { grid-template-columns: repeat(12, 1fr); }
  
  .xl\:col-span-1 { grid-column: span 1; }
  .xl\:col-span-2 { grid-column: span 2; }
  .xl\:col-span-3 { grid-column: span 3; }
  .xl\:col-span-4 { grid-column: span 4; }
  .xl\:col-span-5 { grid-column: span 5; }
  .xl\:col-span-6 { grid-column: span 6; }
  .xl\:col-span-12 { grid-column: span 12; }
  
  .xl\:flex-col { flex-direction: column; }
  .xl\:flex-row { flex-direction: row; }
  
  .xl\:justify-start { justify-content: flex-start; }
  .xl\:justify-center { justify-content: center; }
  .xl\:justify-end { justify-content: flex-end; }
  .xl\:justify-between { justify-content: space-between; }
  
  .xl\:items-start { align-items: flex-start; }
  .xl\:items-center { align-items: center; }
  .xl\:items-end { align-items: flex-end; }
}

/* 特定组件的响应式优化 */
@media (max-width: 768px) {
  /* Dashboard 响应式 */
  .dashboard-header {
    flex-direction: column;
    gap: var(--spacing-lg);
    text-align: center;
    padding: var(--spacing-2xl);
  }
  
  .dashboard-nav {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    padding: 0 var(--spacing-lg);
  }
  
  .nav-button {
    white-space: nowrap;
    min-width: max-content;
  }
  
  .dashboard-content {
    padding: var(--spacing-lg);
  }
  
  /* 表格响应式 */
  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .table {
    min-width: 600px;
  }
  
  /* 卡片网格响应式 */
  .servers-grid,
  .backup-actions {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  /* 表单响应式 */
  .form-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .modal-content {
    margin: var(--spacing-lg);
    max-width: calc(100vw - var(--spacing-2xl));
    min-width: auto;
  }
}

@media (max-width: 480px) {
  /* 超小屏幕优化 */
  .dashboard-header {
    padding: var(--spacing-lg);
  }
  
  .app-title {
    font-size: var(--font-size-2xl);
  }
  
  .user-section {
    flex-direction: column;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
  }
  
  .backup-stats {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .backup-stat {
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  .enterprise-form {
    padding: var(--spacing-lg);
  }
  
  .modal-content {
    padding: var(--spacing-lg);
  }
}

/* 打印样式 */
@media print {
  .dashboard-nav,
  .user-section,
  .logout-button,
  .add-server-button,
  .backup-action-button,
  .server-actions,
  .backup-actions-list {
    display: none !important;
  }
  
  .dashboard-content {
    padding: 0;
  }
  
  .card,
  .info-card,
  .server-card {
    box-shadow: none;
    border: 1px solid #ccc;
    break-inside: avoid;
  }
}
