/* 企业级MySQL增量备份系统样式 */

/* 导入字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* 导入动画和组件样式 */
@import './styles/animations.css';
@import './styles/components.css';
@import './styles/responsive.css';

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* 企业级主题色彩 - 基于Microsoft Fluent Design */
  --primary-color: #0078d4;
  --primary-hover: #106ebe;
  --primary-active: #005a9e;
  --primary-light: #deecf9;
  --primary-dark: #004578;

  --secondary-color: #6c757d;
  --secondary-hover: #5a6268;
  --secondary-light: #f8f9fa;

  --success-color: #107c10;
  --success-hover: #0e6e0e;
  --success-light: #dff6dd;

  --warning-color: #ff8c00;
  --warning-hover: #e67e00;
  --warning-light: #fff4e5;

  --danger-color: #d13438;
  --danger-hover: #b02e31;
  --danger-light: #fdf2f2;

  --error-color: #dc2626;
  --info-color: #0ea5e9;
  --info-light: #e0f2fe;

  /* 企业级背景色系 */
  --bg-primary: #f3f2f1;
  --bg-secondary: #ffffff;
  --bg-tertiary: #faf9f8;
  --bg-quaternary: #f5f5f5;
  --bg-hover: #f8f8f8;
  --bg-active: #edebe9;
  --bg-selected: #e3f2fd;
  --bg-disabled: #f0f0f0;

  /* 企业级文字颜色 */
  --text-primary: #323130;
  --text-secondary: #605e5c;
  --text-tertiary: #8a8886;
  --text-disabled: #a19f9d;
  --text-inverse: #ffffff;
  --text-link: #0078d4;
  --text-link-hover: #106ebe;

  /* 企业级边框颜色 */
  --border-color: #e1dfdd;
  --border-hover: #c8c6c4;
  --border-focus: #0078d4;
  --border-error: #dc2626;
  --border-success: #107c10;
  --border-warning: #ff8c00;

  /* 企业级阴影系统 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.04);
  --shadow-md: 0 2px 4px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.12);
  --shadow-xl: 0 8px 16px rgba(0, 0, 0, 0.16);
  --shadow-2xl: 0 16px 32px rgba(0, 0, 0, 0.20);
  --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-card-hover: 0 4px 16px rgba(0, 0, 0, 0.12);
  --shadow-modal: 0 20px 40px rgba(0, 0, 0, 0.25);

  /* 企业级间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-2xl: 24px;
  --spacing-3xl: 32px;
  --spacing-4xl: 40px;
  --spacing-5xl: 48px;

  /* 企业级圆角系统 */
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 12px;
  --radius-2xl: 16px;
  --radius-full: 50%;

  /* 企业级字体系统 */
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 28px;
  --font-size-4xl: 32px;

  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* 企业级过渡动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.2s ease;
  --transition-slow: 0.3s ease;
  --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* 企业级Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;

  /* 企业级字体配置 */
  font-family: 'Segoe UI', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
  line-height: var(--line-height-normal);
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-base);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: 'kern' 1, 'liga' 1;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  overflow-x: hidden;
  background: var(--bg-primary);
}

#root {
  width: 100%;
  height: 100vh;
  background: var(--bg-primary);
}

/* 链接样式 */
a {
  font-weight: 500;
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

/* 企业级按钮基础样式 */
button {
  border-radius: var(--radius-md);
  border: 1px solid transparent;
  font-family: inherit;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  padding: var(--spacing-sm) var(--spacing-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  outline: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  min-height: 36px;
  position: relative;
  user-select: none;
  text-decoration: none;
  white-space: nowrap;
  vertical-align: middle;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

button:focus-visible {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

/* 按钮尺寸变体 */
.btn-sm {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
  min-height: 28px;
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-md);
  min-height: 44px;
}

.btn-xl {
  padding: var(--spacing-lg) var(--spacing-2xl);
  font-size: var(--font-size-lg);
  min-height: 52px;
}

/* 企业级按钮变体 */

/* 主要按钮 */
.primary-button, .btn-primary {
  background: var(--primary-color);
  color: var(--text-inverse);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
}

.primary-button:hover:not(:disabled), .btn-primary:hover:not(:disabled) {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.primary-button:active:not(:disabled), .btn-primary:active:not(:disabled) {
  background: var(--primary-active);
  border-color: var(--primary-active);
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* 次要按钮 */
.secondary-button, .btn-secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border-color: var(--border-color);
  box-shadow: var(--shadow-sm);
}

.secondary-button:hover:not(:disabled), .btn-secondary:hover:not(:disabled) {
  background: var(--bg-hover);
  border-color: var(--border-hover);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.secondary-button:active:not(:disabled), .btn-secondary:active:not(:disabled) {
  background: var(--bg-active);
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* 轮廓按钮 */
.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
  background: var(--primary-color);
  color: var(--text-inverse);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 幽灵按钮 */
.btn-ghost {
  background: transparent;
  color: var(--text-primary);
  border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
  background: var(--bg-hover);
  color: var(--primary-color);
}

/* 危险按钮 */
.danger-button, .btn-danger {
  background: var(--danger-color);
  color: var(--text-inverse);
  border-color: var(--danger-color);
  box-shadow: var(--shadow-sm);
}

.danger-button:hover:not(:disabled), .btn-danger:hover:not(:disabled) {
  background: var(--danger-hover);
  border-color: var(--danger-hover);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* 成功按钮 */
.success-button, .btn-success {
  background: var(--success-color);
  color: var(--text-inverse);
  border-color: var(--success-color);
  box-shadow: var(--shadow-sm);
}

.success-button:hover:not(:disabled), .btn-success:hover:not(:disabled) {
  background: var(--success-hover);
  border-color: var(--success-hover);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* 警告按钮 */
.btn-warning {
  background: var(--warning-color);
  color: var(--text-inverse);
  border-color: var(--warning-color);
  box-shadow: var(--shadow-sm);
}

.btn-warning:hover:not(:disabled) {
  background: var(--warning-hover);
  border-color: var(--warning-hover);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* 信息按钮 */
.btn-info {
  background: var(--info-color);
  color: var(--text-inverse);
  border-color: var(--info-color);
  box-shadow: var(--shadow-sm);
}

.btn-info:hover:not(:disabled) {
  background: #0284c7;
  border-color: #0284c7;
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* 企业级输入框样式 */
input, textarea, select {
  font-family: inherit;
  font-size: var(--font-size-base);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-secondary);
  color: var(--text-primary);
  outline: none;
  transition: all var(--transition-normal);
  min-height: 36px;
  line-height: var(--line-height-normal);
}

input:focus, textarea:focus, select:focus {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
  background: var(--bg-secondary);
}

input:hover:not(:disabled):not(:focus),
textarea:hover:not(:disabled):not(:focus),
select:hover:not(:disabled):not(:focus) {
  border-color: var(--border-hover);
}

input:disabled, textarea:disabled, select:disabled {
  background: var(--bg-disabled);
  color: var(--text-disabled);
  cursor: not-allowed;
  border-color: var(--border-color);
  opacity: 0.7;
}

input::placeholder, textarea::placeholder {
  color: var(--text-tertiary);
  opacity: 1;
}

/* 输入框尺寸变体 */
.input-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-sm);
  min-height: 28px;
}

.input-lg {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-md);
  min-height: 44px;
}

/* 输入框状态样式 */
.input-error {
  border-color: var(--border-error);
  box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
}

.input-success {
  border-color: var(--border-success);
  box-shadow: 0 0 0 2px rgba(16, 124, 16, 0.2);
}

.input-warning {
  border-color: var(--border-warning);
  box-shadow: 0 0 0 2px rgba(255, 140, 0, 0.2);
}

/* 滚动条样式 - 现代化 */
::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: var(--border-hover);
  border-radius: 6px;
  border: 2px solid var(--bg-tertiary);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

::-webkit-scrollbar-corner {
  background: var(--bg-tertiary);
}

/* 企业级模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal-backdrop);
  animation: fadeIn var(--transition-normal);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-content {
  background: var(--bg-secondary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-3xl);
  min-width: 400px;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-modal);
  border: 1px solid var(--border-color);
  z-index: var(--z-modal);
  animation: slideUp var(--transition-normal);
  position: relative;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.modal-title {
  margin: 0;
  color: var(--text-primary);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}

.modal-close {
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  font-size: var(--font-size-lg);
  min-height: auto;
}

.modal-close:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.modal-body {
  margin-bottom: var(--spacing-2xl);
}

.modal-body p {
  margin-bottom: var(--spacing-lg);
  color: var(--text-secondary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
}

.modal-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

/* 模态框尺寸变体 */
.modal-sm .modal-content {
  min-width: 300px;
  max-width: 400px;
}

.modal-lg .modal-content {
  min-width: 600px;
  max-width: 800px;
}

.modal-xl .modal-content {
  min-width: 800px;
  max-width: 1200px;
}

/* 备份历史样式 */
.backup-history {
  padding: var(--spacing-lg, 24px);
}

.col-actions {
  min-width: 100px;
  text-align: center;
}

.download-button {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  min-height: 28px;
}

.download-button:hover:not(:disabled) {
  background: var(--primary-hover);
  transform: translateY(-1px);
}

.download-button:disabled {
  background: var(--border-color);
  cursor: not-allowed;
  transform: none;
}

/* 企业级卡片样式 */
.card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-card);
  transition: all var(--transition-normal);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-card-hover);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--spacing-xl) var(--spacing-2xl);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-tertiary);
}

.card-title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.card-subtitle {
  margin: var(--spacing-xs) 0 0 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.card-body {
  padding: var(--spacing-2xl);
}

.card-footer {
  padding: var(--spacing-lg) var(--spacing-2xl);
  border-top: 1px solid var(--border-color);
  background: var(--bg-tertiary);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 企业级表格样式 */
.table {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-card);
}

.table th {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
  text-align: left;
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid var(--border-color);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table td {
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
  font-size: var(--font-size-base);
  vertical-align: middle;
}

.table tr:hover {
  background: var(--bg-hover);
}

.table tr:last-child td {
  border-bottom: none;
}

/* 表格变体 */
.table-striped tr:nth-child(even) {
  background: var(--bg-tertiary);
}

.table-bordered {
  border: 1px solid var(--border-color);
}

.table-bordered th,
.table-bordered td {
  border-right: 1px solid var(--border-color);
}

.table-bordered th:last-child,
.table-bordered td:last-child {
  border-right: none;
}

/* 企业级状态徽章 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-radius: var(--radius-full);
  border: 1px solid transparent;
}

.badge-primary {
  background: var(--primary-light);
  color: var(--primary-dark);
  border-color: var(--primary-color);
}

.badge-success {
  background: var(--success-light);
  color: var(--success-color);
  border-color: var(--success-color);
}

.badge-warning {
  background: var(--warning-light);
  color: var(--warning-color);
  border-color: var(--warning-color);
}

.badge-danger {
  background: var(--danger-light);
  color: var(--danger-color);
  border-color: var(--danger-color);
}

.badge-info {
  background: var(--info-light);
  color: var(--info-color);
  border-color: var(--info-color);
}

.badge-secondary {
  background: var(--secondary-light);
  color: var(--secondary-color);
  border-color: var(--secondary-color);
}
