import React, { useState, useEffect } from 'react';
import './BackupTimelineViewer.css';

interface BackupHistoryItem {
  id: number;
  task_id: number;
  backup_type: 'full' | 'incremental';
  file_path: string;
  file_size: number;
  start_time: string;
  end_time?: string;
  status: 'completed' | 'failed' | 'running';
  error_message?: string;
  binlog_file?: string;
  binlog_position?: number;
  task_name: string;
  server_name: string;
  server_host: string;
}

interface BackupTimelineViewerProps {
  userId: number;
  taskId: number;
  taskName: string;
  onClose: () => void;
}

const BackupTimelineViewer: React.FC<BackupTimelineViewerProps> = ({ 
  userId, 
  taskId, 
  taskName, 
  onClose 
}) => {
  const [history, setHistory] = useState<BackupHistoryItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [downloading, setDownloading] = useState<number | null>(null);

  // 加载备份历史
  const loadTaskHistory = async () => {
    try {
      setLoading(true);
      setError('');
      
      console.log('Loading task history for:', { taskId, userId });
      const result = await window.electronAPI.getBackupTaskHistory(taskId, userId);
      console.log('Task history result:', result);

      if (result.success) {
        console.log('History data:', result.history);
        setHistory(result.history || []);
      } else {
        console.error('Failed to load history:', result.message);
        setError(result.message || '获取备份历史失败');
      }
    } catch (error: any) {
      console.error('Load task history error:', error);
      setError(`加载备份历史失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 下载单个备份文件
  const downloadBackupFile = async (historyId: number, fileName: string) => {
    try {
      setDownloading(historyId);
      setError('');

      console.log('Downloading backup file:', { historyId, fileName });
      const result = await window.electronAPI.downloadBackupFile(historyId, userId, fileName);

      if (result.success) {
        console.log(`文件下载成功: ${result.localPath}`);
        // 可以选择性地显示成功消息或打开文件所在目录
        if (result.localPath) {
          const showResult = await window.electronAPI.showItemInFolder(result.localPath);
          if (!showResult.success) {
            console.warn('无法打开文件所在目录:', showResult.message);
          }
        }
      } else {
        setError(result.message || '下载备份文件失败');
      }
    } catch (error: any) {
      console.error('Download backup file error:', error);
      setError(`下载备份文件失败: ${error.message}`);
    } finally {
      setDownloading(null);
    }
  };



  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化时间
  const formatDateTime = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return '无效日期';
      }
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (error) {
      console.error('日期格式化错误:', error);
      return '日期错误';
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return '已完成';
      case 'failed': return '失败';
      case 'running': return '运行中';
      default: return '未知';
    }
  };

  // 获取状态样式类
  const getStatusClass = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'failed': return 'error';
      case 'running': return 'warning';
      default: return 'secondary';
    }
  };

  useEffect(() => {
    loadTaskHistory();
  }, [taskId, userId]);

  return (
    <div className="backup-timeline-overlay">
      <div className="backup-timeline-modal">
        <div className="backup-timeline-header">
          <h2>备份时间节点 - {taskName}</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <div className="backup-timeline-content">
          {loading && (
            <div className="loading-state">
              <div className="loading-spinner"></div>
              <p>正在加载备份时间节点...</p>
            </div>
          )}

          {error && (
            <div className="error-state">
              <p className="error-message">{error}</p>
              <button onClick={loadTaskHistory} className="retry-button">
                重试
              </button>
            </div>
          )}

          {!loading && !error && history.length === 0 && (
            <div className="empty-state">
              <p>该任务暂无备份历史记录</p>
            </div>
          )}

          {!loading && !error && history.length > 0 && (
            <div className="timeline-list">
              <div className="timeline-header">
                <span>时间节点</span>
                <span>类型</span>
                <span>状态</span>
                <span>文件大小</span>
                <span>Binlog位置</span>
                <span>操作</span>
              </div>

              {history.map((item, index) => (
                <div key={item.id} className={`timeline-item ${getStatusClass(item.status)}`}>
                  <span className="timeline-time">
                    <div className="start-time">{formatDateTime(item.start_time)}</div>
                    {item.end_time && (
                      <div className="end-time">结束: {formatDateTime(item.end_time)}</div>
                    )}
                  </span>
                  
                  <span className={`backup-type ${item.backup_type}`}>
                    {item.backup_type === 'full' ? '完整备份' : '增量备份'}
                  </span>
                  
                  <span className={`status-badge ${getStatusClass(item.status)}`}>
                    {getStatusText(item.status)}
                  </span>
                  
                  <span className="file-size">
                    {formatFileSize(item.file_size)}
                  </span>
                  
                  <span className="binlog-info">
                    {item.binlog_file ? (
                      <div>
                        <div>{item.binlog_file}</div>
                        <div>位置: {item.binlog_position}</div>
                      </div>
                    ) : (
                      <span className="no-binlog">无Binlog信息</span>
                    )}
                  </span>
                  
                  <span className="timeline-actions">
                    <button
                      onClick={() => downloadBackupFile(item.id, `${item.task_name}_${item.start_time}.sql`)}
                      disabled={downloading === item.id || item.status !== 'completed'}
                      className="download-button"
                      title="下载此备份文件"
                    >
                      {downloading === item.id ? '下载中...' : '下载文件'}
                    </button>
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="backup-timeline-footer">
          <button onClick={loadTaskHistory} className="refresh-button">
            刷新列表
          </button>
          <button onClick={onClose} className="close-footer-button">
            关闭
          </button>
        </div>
      </div>
    </div>
  );
};

export default BackupTimelineViewer;
