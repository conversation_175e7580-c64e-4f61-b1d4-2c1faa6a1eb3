import React, { useState, useEffect } from 'react';
import './BackupPlanDetailEnterprise.css';

interface BackupHistoryItem {
  id: number;
  backup_type: 'full' | 'incremental';
  status: 'running' | 'completed' | 'failed';
  start_time: string;
  end_time?: string;
  file_size: number;
  file_path: string;
  error_message?: string;
  binlog_file?: string;
  binlog_position?: number;
  duration?: number;
  compression_ratio?: number;
  records_count?: number;
}

interface BackupTask {
  id: number;
  name: string;
  database_name: string;
  server_name?: string;
  schedule_type: string;
  schedule_time?: string;
  status: string;
  created_at: string;
  last_backup_time?: string;
  total_backups?: number;
  success_rate?: number;
}

interface BackupStats {
  totalBackups: number;
  successfulBackups: number;
  failedBackups: number;
  totalSize: number;
  avgDuration: number;
  lastFullBackup?: string;
  nextScheduledBackup?: string;
  successRate: number;
}

interface BackupPlanDetailEnterpriseProps {
  taskId: number;
  userId: number;
  onBack: () => void;
}

const BackupPlanDetailEnterprise: React.FC<BackupPlanDetailEnterpriseProps> = ({ taskId, userId, onBack }) => {
  const [task, setTask] = useState<BackupTask | null>(null);
  const [history, setHistory] = useState<BackupHistoryItem[]>([]);
  const [stats, setStats] = useState<BackupStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [generatingSQL, setGeneratingSQL] = useState<number | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState('7d');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [viewMode, setViewMode] = useState<'timeline' | 'table'>('timeline');

  useEffect(() => {
    loadTaskHistory();
    
    // 自动刷新
    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(() => {
        loadTaskHistory(true);
      }, 30000); // 每30秒刷新一次
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [taskId, autoRefresh]);

  const loadTaskHistory = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      if (!window.electronAPI) {
        throw new Error('electronAPI 不可用');
      }

      const result = await window.electronAPI.getBackupTaskHistory(taskId, userId);
      
      if (result.success) {
        setTask(result.task || null);
        const historyData = result.history || [];
        setHistory(historyData);
        
        // 计算统计信息
        const totalBackups = historyData.length;
        const successfulBackups = historyData.filter(h => h.status === 'completed').length;
        const failedBackups = historyData.filter(h => h.status === 'failed').length;
        const totalSize = historyData.reduce((sum, h) => sum + (h.file_size || 0), 0);
        const completedBackups = historyData.filter(h => h.status === 'completed' && h.end_time && h.start_time);
        const avgDuration = completedBackups.length > 0 
          ? completedBackups.reduce((sum, h) => {
              const duration = new Date(h.end_time!).getTime() - new Date(h.start_time).getTime();
              return sum + duration;
            }, 0) / completedBackups.length / 1000 // 转换为秒
          : 0;
        const lastFullBackup = historyData.find(h => h.backup_type === 'full' && h.status === 'completed')?.start_time;
        const successRate = totalBackups > 0 ? (successfulBackups / totalBackups) * 100 : 0;
        
        setStats({
          totalBackups,
          successfulBackups,
          failedBackups,
          totalSize,
          avgDuration,
          lastFullBackup,
          nextScheduledBackup: calculateNextBackup(result.task),
          successRate
        });
      } else {
        setError(result.message || '获取备份历史失败');
      }
    } catch (error) {
      console.error('获取备份历史失败:', error);
      setError('获取备份历史失败');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const calculateNextBackup = (task: BackupTask): string | undefined => {
    if (!task || task.status !== 'active') return undefined;
    
    const now = new Date();
    const scheduleTime = task.schedule_time || '00:00';
    const [hours, minutes] = scheduleTime.split(':').map(Number);
    
    let nextBackup = new Date(now);
    
    switch (task.schedule_type) {
      case 'hourly':
        nextBackup.setMinutes(minutes, 0, 0);
        if (nextBackup <= now) {
          nextBackup.setHours(nextBackup.getHours() + 1);
        }
        break;
      case 'daily':
        nextBackup.setHours(hours, minutes, 0, 0);
        if (nextBackup <= now) {
          nextBackup.setDate(nextBackup.getDate() + 1);
        }
        break;
      case 'weekly':
        nextBackup.setHours(hours, minutes, 0, 0);
        const daysUntilSunday = (7 - nextBackup.getDay()) % 7;
        nextBackup.setDate(nextBackup.getDate() + daysUntilSunday);
        if (nextBackup <= now) {
          nextBackup.setDate(nextBackup.getDate() + 7);
        }
        break;
      case 'monthly':
        nextBackup.setDate(1);
        nextBackup.setHours(hours, minutes, 0, 0);
        nextBackup.setMonth(nextBackup.getMonth() + 1);
        break;
      default:
        return undefined;
    }
    
    return nextBackup.toLocaleString();
  };

  const handleGenerateSQL = async (historyId: number) => {
    try {
      setGeneratingSQL(historyId);
      
      const result = await window.electronAPI.generateCompleteBackupSQL(
        taskId,
        historyId,
        userId
      );
      
      if (result.success && result.filePath) {
        // 下载文件
        const link = document.createElement('a');
        link.href = `file://${result.filePath}`;
        link.download = `complete_backup_${task?.database_name}_${new Date().toISOString().split('T')[0]}.sql`;
        link.click();
        
        // 在文件管理器中显示
        if (window.electronAPI.showInFolder) {
          window.electronAPI.showInFolder(result.filePath);
        }
      } else {
        alert(result.message || '生成SQL文件失败');
      }
    } catch (error) {
      console.error('生成SQL文件失败:', error);
      alert('生成SQL文件失败');
    } finally {
      setGeneratingSQL(null);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number): string => {
    if (seconds < 60) return `${Math.round(seconds)}秒`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}分钟`;
    return `${Math.round(seconds / 3600)}小时`;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return '✅';
      case 'failed':
        return '❌';
      case 'running':
        return '🔄';
      default:
        return '⏸️';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'var(--success-color)';
      case 'failed':
        return 'var(--error-color)';
      case 'running':
        return 'var(--warning-color)';
      default:
        return 'var(--text-secondary)';
    }
  };

  const filteredHistory = history.filter(item => {
    const itemDate = new Date(item.start_time);
    const now = new Date();
    const diffDays = (now.getTime() - itemDate.getTime()) / (1000 * 60 * 60 * 24);
    
    switch (selectedTimeRange) {
      case '1d':
        return diffDays <= 1;
      case '7d':
        return diffDays <= 7;
      case '30d':
        return diffDays <= 30;
      case '90d':
        return diffDays <= 90;
      default:
        return true;
    }
  });

  if (loading) {
    return (
      <div className="backup-detail-enterprise">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>加载备份详情中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="backup-detail-enterprise">
        <div className="error-container">
          <div className="error-icon">⚠️</div>
          <h3>加载失败</h3>
          <p>{error}</p>
          <div className="error-actions">
            <button onClick={onBack} className="btn btn-secondary">
              返回
            </button>
            <button onClick={() => loadTaskHistory()} className="btn btn-primary">
              重试
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="backup-detail-enterprise">
      {/* 页面头部 */}
      <div className="detail-header">
        <div className="header-left">
          <button onClick={onBack} className="btn btn-ghost">
            <span className="icon">←</span>
            返回
          </button>
          <div className="breadcrumb">
            <span>备份管理</span>
            <span className="separator">/</span>
            <span>{task?.name || '备份详情'}</span>
          </div>
        </div>
        <div className="header-right">
          <div className="header-controls">
            <label className="auto-refresh-toggle">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
              />
              <span>自动刷新</span>
            </label>
            <button 
              onClick={() => loadTaskHistory(true)} 
              className={`btn btn-secondary ${refreshing ? 'loading' : ''}`}
              disabled={refreshing}
            >
              <span className="icon">🔄</span>
              {refreshing ? '刷新中...' : '刷新'}
            </button>
          </div>
        </div>
      </div>

      {/* 任务概览卡片 */}
      {task && (
        <div className="task-overview-card">
          <div className="card-header">
            <h2>{task.name}</h2>
            <div className={`status-badge ${task.status}`}>
              {task.status === 'active' ? '运行中' : '已停用'}
            </div>
          </div>
          <div className="card-content">
            <div className="overview-grid">
              <div className="overview-item">
                <span className="label">数据库</span>
                <span className="value">{task.database_name}</span>
              </div>
              <div className="overview-item">
                <span className="label">调度类型</span>
                <span className="value">
                  {task.schedule_type === 'hourly' && '每小时'}
                  {task.schedule_type === 'daily' && '每日'}
                  {task.schedule_type === 'weekly' && '每周'}
                  {task.schedule_type === 'monthly' && '每月'}
                  {task.schedule_type === 'manual' && '手动'}
                </span>
              </div>
              <div className="overview-item">
                <span className="label">调度时间</span>
                <span className="value">{task.schedule_time || '未设置'}</span>
              </div>
              <div className="overview-item">
                <span className="label">创建时间</span>
                <span className="value">{new Date(task.created_at).toLocaleString()}</span>
              </div>
              {stats?.nextScheduledBackup && (
                <div className="overview-item">
                  <span className="label">下次备份</span>
                  <span className="value next-backup">{stats.nextScheduledBackup}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 统计信息卡片 */}
      {stats && (
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-icon">📊</div>
            <div className="stat-content">
              <div className="stat-value">{stats.totalBackups}</div>
              <div className="stat-label">总备份数</div>
            </div>
          </div>
          <div className="stat-card success">
            <div className="stat-icon">✅</div>
            <div className="stat-content">
              <div className="stat-value">{stats.successfulBackups}</div>
              <div className="stat-label">成功备份</div>
            </div>
          </div>
          <div className="stat-card error">
            <div className="stat-icon">❌</div>
            <div className="stat-content">
              <div className="stat-value">{stats.failedBackups}</div>
              <div className="stat-label">失败备份</div>
            </div>
          </div>
          <div className="stat-card">
            <div className="stat-icon">💾</div>
            <div className="stat-content">
              <div className="stat-value">{formatFileSize(stats.totalSize)}</div>
              <div className="stat-label">总大小</div>
            </div>
          </div>
          <div className="stat-card">
            <div className="stat-icon">⏱️</div>
            <div className="stat-content">
              <div className="stat-value">{formatDuration(stats.avgDuration)}</div>
              <div className="stat-label">平均耗时</div>
            </div>
          </div>
          <div className="stat-card">
            <div className="stat-icon">📈</div>
            <div className="stat-content">
              <div className="stat-value">{stats.successRate.toFixed(1)}%</div>
              <div className="stat-label">成功率</div>
            </div>
          </div>
        </div>
      )}

      {/* 控制面板 */}
      <div className="controls-panel">
        <div className="controls-left">
          <div className="time-range-selector">
            <label>时间范围:</label>
            <select
              value={selectedTimeRange}
              onChange={(e) => setSelectedTimeRange(e.target.value)}
              className="select"
            >
              <option value="1d">最近1天</option>
              <option value="7d">最近7天</option>
              <option value="30d">最近30天</option>
              <option value="90d">最近90天</option>
              <option value="all">全部</option>
            </select>
          </div>
        </div>
        <div className="controls-right">
          <div className="view-mode-toggle">
            <button
              className={`btn ${viewMode === 'timeline' ? 'btn-primary' : 'btn-secondary'}`}
              onClick={() => setViewMode('timeline')}
            >
              <span className="icon">📅</span>
              时间线
            </button>
            <button
              className={`btn ${viewMode === 'table' ? 'btn-primary' : 'btn-secondary'}`}
              onClick={() => setViewMode('table')}
            >
              <span className="icon">📋</span>
              表格
            </button>
          </div>
        </div>
      </div>

      {/* 备份历史 */}
      <div className="backup-history-section">
        <div className="section-header">
          <h3>备份历史</h3>
          <span className="history-count">{filteredHistory.length} 条记录</span>
        </div>

        {filteredHistory.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">📁</div>
            <h4>暂无备份记录</h4>
            <p>在选定的时间范围内没有找到备份记录</p>
          </div>
        ) : viewMode === 'timeline' ? (
          <div className="timeline-container">
            <div className="timeline">
              {filteredHistory.map((item, index) => (
                <div key={item.id} className={`timeline-item ${item.status}`}>
                  <div className="timeline-marker">
                    <div className={`marker-dot ${item.status}`}>
                      {getStatusIcon(item.status)}
                    </div>
                    {index < filteredHistory.length - 1 && <div className="timeline-line"></div>}
                  </div>
                  <div className="timeline-content">
                    <div className="timeline-card">
                      <div className="card-header">
                        <div className="backup-info">
                          <span className={`backup-type ${item.backup_type}`}>
                            {item.backup_type === 'full' ? '全量备份' : '增量备份'}
                          </span>
                          <span className="backup-time">
                            {new Date(item.start_time).toLocaleString()}
                          </span>
                        </div>
                        <div className="backup-actions">
                          <button
                            onClick={() => handleGenerateSQL(item.id)}
                            disabled={generatingSQL === item.id || item.status !== 'completed'}
                            className="btn btn-sm btn-primary"
                          >
                            {generatingSQL === item.id ? '生成中...' : '下载完整SQL'}
                          </button>
                        </div>
                      </div>
                      <div className="card-content">
                        <div className="backup-details">
                          <div className="detail-item">
                            <span className="label">状态:</span>
                            <span className={`status ${item.status}`} style={{ color: getStatusColor(item.status) }}>
                              {item.status === 'completed' && '已完成'}
                              {item.status === 'failed' && '失败'}
                              {item.status === 'running' && '运行中'}
                            </span>
                          </div>
                          {item.file_size > 0 && (
                            <div className="detail-item">
                              <span className="label">文件大小:</span>
                              <span className="value">{formatFileSize(item.file_size)}</span>
                            </div>
                          )}
                          {item.end_time && (
                            <div className="detail-item">
                              <span className="label">耗时:</span>
                              <span className="value">
                                {formatDuration((new Date(item.end_time).getTime() - new Date(item.start_time).getTime()) / 1000)}
                              </span>
                            </div>
                          )}
                          {item.binlog_file && (
                            <div className="detail-item">
                              <span className="label">Binlog位置:</span>
                              <span className="value">{item.binlog_file}:{item.binlog_position}</span>
                            </div>
                          )}
                          {item.error_message && (
                            <div className="detail-item error">
                              <span className="label">错误信息:</span>
                              <span className="value error-message">{item.error_message}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="table-container">
            <table className="backup-table">
              <thead>
                <tr>
                  <th>类型</th>
                  <th>状态</th>
                  <th>开始时间</th>
                  <th>结束时间</th>
                  <th>耗时</th>
                  <th>文件大小</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                {filteredHistory.map((item) => (
                  <tr key={item.id} className={item.status}>
                    <td>
                      <span className={`backup-type-badge ${item.backup_type}`}>
                        {item.backup_type === 'full' ? '全量' : '增量'}
                      </span>
                    </td>
                    <td>
                      <span className={`status-badge ${item.status}`} style={{ color: getStatusColor(item.status) }}>
                        {getStatusIcon(item.status)}
                        {item.status === 'completed' && '已完成'}
                        {item.status === 'failed' && '失败'}
                        {item.status === 'running' && '运行中'}
                      </span>
                    </td>
                    <td>{new Date(item.start_time).toLocaleString()}</td>
                    <td>{item.end_time ? new Date(item.end_time).toLocaleString() : '-'}</td>
                    <td>
                      {item.end_time
                        ? formatDuration((new Date(item.end_time).getTime() - new Date(item.start_time).getTime()) / 1000)
                        : '-'
                      }
                    </td>
                    <td>{formatFileSize(item.file_size)}</td>
                    <td>
                      <button
                        onClick={() => handleGenerateSQL(item.id)}
                        disabled={generatingSQL === item.id || item.status !== 'completed'}
                        className="btn btn-sm btn-primary"
                      >
                        {generatingSQL === item.id ? '生成中...' : '下载SQL'}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default BackupPlanDetailEnterprise;
