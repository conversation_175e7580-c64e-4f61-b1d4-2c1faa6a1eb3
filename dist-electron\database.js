import mysql from 'mysql2/promise';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { NodeSSH } from 'node-ssh';
import fs from 'fs';
import path from 'path';
// 数据库配置
const DB_CONFIG = {
    host: '***************',
    user: 'user-hiram',
    password: 'user-hiram',
    database: 'user',
    port: 3306
};
// JWT密钥
const JWT_SECRET = 'mysql-backup-app-secret-key-2024';
// 数据库连接池
let pool;
// 初始化数据库连接
export function initDatabase() {
    pool = mysql.createPool({
        ...DB_CONFIG,
        waitForConnections: true,
        connectionLimit: 10,
        queueLimit: 0
    });
}
// 创建用户表（如果不存在）
export async function createUserTable() {
    try {
        const connection = await pool.getConnection();
        const createTableSQL = `
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `;
        await connection.execute(createTableSQL);
        connection.release();
        console.log('User table created successfully or already exists');
    }
    catch (error) {
        console.error('创建用户表失败:', error);
        throw error;
    }
}
// 用户注册
export async function registerUser(username, email, password) {
    try {
        const connection = await pool.getConnection();
        // 检查用户名是否已存在
        const [existingUsers] = await connection.execute('SELECT id FROM users WHERE username = ? OR email = ?', [username, email]);
        if (existingUsers.length > 0) {
            connection.release();
            const existingUser = existingUsers[0];
            if (existingUser.username === username) {
                return { success: false, message: '用户名已存在' };
            }
            else {
                return { success: false, message: '邮箱已被注册' };
            }
        }
        // 密码加密
        const saltRounds = 10;
        const passwordHash = await bcrypt.hash(password, saltRounds);
        // 插入新用户
        const [result] = await connection.execute('INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)', [username, email, passwordHash]);
        connection.release();
        return {
            success: true,
            message: '注册成功',
            userId: result.insertId
        };
    }
    catch (error) {
        console.error('用户注册失败:', error);
        return { success: false, message: '注册失败，请稍后重试' };
    }
}
// 用户登录
export async function loginUser(username, password) {
    try {
        const connection = await pool.getConnection();
        // 查找用户
        const [users] = await connection.execute('SELECT id, username, email, password_hash FROM users WHERE username = ?', [username]);
        connection.release();
        if (users.length === 0) {
            return { success: false, message: '用户名或密码错误' };
        }
        const user = users[0];
        // 验证密码
        const isPasswordValid = await bcrypt.compare(password, user.password_hash);
        if (!isPasswordValid) {
            return { success: false, message: '用户名或密码错误' };
        }
        // 生成JWT token
        const token = jwt.sign({
            userId: user.id,
            username: user.username,
            email: user.email
        }, JWT_SECRET, { expiresIn: '24h' });
        return {
            success: true,
            message: '登录成功',
            token,
            user: {
                id: user.id,
                username: user.username,
                email: user.email
            }
        };
    }
    catch (error) {
        console.error('用户登录失败:', error);
        return { success: false, message: '登录失败，请稍后重试' };
    }
}
// 验证JWT token
export function verifyToken(token) {
    try {
        const decoded = jwt.verify(token, JWT_SECRET);
        return { success: true, data: decoded };
    }
    catch (error) {
        return { success: false, message: 'Token无效或已过期' };
    }
}
// 邮箱格式验证
export function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
// 创建服务器表
export async function createServerTable() {
    try {
        const connection = await pool.getConnection();
        const createTableQuery = `
      CREATE TABLE IF NOT EXISTS servers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        host VARCHAR(255) NOT NULL,
        port INT DEFAULT 3306,
        username VARCHAR(100) NOT NULL,
        password VARCHAR(255) NOT NULL,
        ssh_host VARCHAR(255),
        ssh_port INT DEFAULT 22,
        ssh_username VARCHAR(100),
        ssh_password VARCHAR(255),
        ssh_private_key TEXT,
        status ENUM('active', 'inactive', 'error') DEFAULT 'inactive',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `;
        await connection.execute(createTableQuery);
        connection.release();
        console.log('Server table created successfully or already exists');
    }
    catch (error) {
        console.error('创建服务器表失败:', error);
        throw error;
    }
}
// 创建备份任务表
export async function createBackupTaskTable() {
    try {
        const connection = await pool.getConnection();
        const createTableQuery = `
      CREATE TABLE IF NOT EXISTS backup_tasks (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        server_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        database_name VARCHAR(100) NOT NULL,
        backup_type ENUM('full', 'incremental') DEFAULT 'incremental',
        schedule_type ENUM('manual', 'hourly', 'daily', 'weekly', 'monthly', 'custom') DEFAULT 'manual',
        schedule_time TIME,
        schedule_day INT,
        custom_interval_minutes INT DEFAULT NULL COMMENT '自定义备份间隔（分钟）',
        backup_path VARCHAR(500),
        retention_days INT DEFAULT 30,
        status ENUM('active', 'inactive', 'running', 'error') DEFAULT 'active',
        last_backup_time TIMESTAMP NULL,
        last_backup_size BIGINT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (server_id) REFERENCES servers(id) ON DELETE CASCADE
      )
    `;
        await connection.execute(createTableQuery);
        // 检查并添加新字段（如果表已存在但缺少新字段）
        try {
            // 首先检查字段是否存在
            const [columns] = await connection.execute(`
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'backup_tasks'
        AND COLUMN_NAME = 'custom_interval_minutes'
      `);
            if (columns.length === 0) {
                // 字段不存在，添加字段
                await connection.execute(`
          ALTER TABLE backup_tasks
          ADD COLUMN custom_interval_minutes INT DEFAULT NULL COMMENT '自定义备份间隔（分钟）'
        `);
                console.log('Added custom_interval_minutes column to backup_tasks table');
            }
            // 更新 schedule_type 枚举值
            await connection.execute(`
        ALTER TABLE backup_tasks
        MODIFY COLUMN schedule_type ENUM('manual', 'hourly', 'daily', 'weekly', 'monthly', 'custom') DEFAULT 'manual'
      `);
            console.log('Updated schedule_type enum values');
        }
        catch (alterError) {
            console.error('Error updating table structure:', alterError);
            // 如果是字段已存在的错误，可以忽略
            if (!alterError.message?.includes('Duplicate column name')) {
                throw alterError;
            }
        }
        connection.release();
        console.log('Backup task table created successfully or already exists');
    }
    catch (error) {
        console.error('创建备份任务表失败:', error);
        throw error;
    }
}
// 创建备份历史表
export async function createBackupHistoryTable() {
    try {
        const connection = await pool.getConnection();
        const createTableQuery = `
      CREATE TABLE IF NOT EXISTS backup_history (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task_id INT NOT NULL,
        backup_type ENUM('full', 'incremental') NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size BIGINT DEFAULT 0,
        start_time TIMESTAMP NOT NULL,
        end_time TIMESTAMP NULL,
        status ENUM('running', 'completed', 'failed') DEFAULT 'running',
        error_message TEXT,
        binlog_file VARCHAR(255),
        binlog_position BIGINT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (task_id) REFERENCES backup_tasks(id) ON DELETE CASCADE
      )
    `;
        await connection.execute(createTableQuery);
        connection.release();
        console.log('Backup history table created successfully or already exists');
    }
    catch (error) {
        console.error('创建备份历史表失败:', error);
        throw error;
    }
}
// 添加服务器
export async function addServer(serverData) {
    try {
        const connection = await pool.getConnection();
        // 检查服务器名称是否已存在
        const [existing] = await connection.execute('SELECT id FROM servers WHERE user_id = ? AND name = ?', [serverData.userId, serverData.name]);
        if (existing.length > 0) {
            connection.release();
            return { success: false, message: '服务器名称已存在' };
        }
        // 插入新服务器
        const [result] = await connection.execute(`INSERT INTO servers (user_id, name, host, port, username, password, ssh_host, ssh_port, ssh_username, ssh_password, ssh_private_key)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, [
            serverData.userId,
            serverData.name,
            serverData.host,
            serverData.port,
            serverData.username,
            serverData.password,
            serverData.sshHost || null,
            serverData.sshPort || null,
            serverData.sshUsername || null,
            serverData.sshPassword || null,
            serverData.sshPrivateKey || null
        ]);
        connection.release();
        return {
            success: true,
            message: '服务器添加成功',
            serverId: result.insertId
        };
    }
    catch (error) {
        console.error('添加服务器失败:', error);
        return { success: false, message: '添加服务器失败，请稍后重试' };
    }
}
// 获取用户的服务器列表
export async function getUserServers(userId) {
    try {
        const connection = await pool.getConnection();
        const [servers] = await connection.execute(`SELECT id, name, host, port, username, ssh_host, ssh_port, ssh_username, status, created_at, updated_at
       FROM servers WHERE user_id = ? ORDER BY created_at DESC`, [userId]);
        connection.release();
        return {
            success: true,
            servers
        };
    }
    catch (error) {
        console.error('获取服务器列表失败:', error);
        return { success: false, message: '获取服务器列表失败' };
    }
}
// 获取单个服务器信息
export async function getServerById(serverId, userId) {
    try {
        const connection = await pool.getConnection();
        const [servers] = await connection.execute('SELECT * FROM servers WHERE id = ? AND user_id = ?', [serverId, userId]);
        connection.release();
        if (servers.length === 0) {
            return { success: false, message: '服务器不存在' };
        }
        return { success: true, server: servers[0] };
    }
    catch (error) {
        console.error('获取服务器信息失败:', error);
        return { success: false, message: '获取服务器信息失败' };
    }
}
// 更新服务器信息
export async function updateServer(serverId, userId, serverData) {
    try {
        const connection = await pool.getConnection();
        const { name, host, port, username, password, sshHost, sshPort, sshUsername, sshPassword, sshPrivateKey } = serverData;
        await connection.execute(`UPDATE servers SET
        name = ?, host = ?, port = ?, username = ?, password = ?,
        ssh_host = ?, ssh_port = ?, ssh_username = ?, ssh_password = ?, ssh_private_key = ?,
        updated_at = CURRENT_TIMESTAMP
       WHERE id = ? AND user_id = ?`, [
            name, host, port, username, password,
            sshHost || null, sshPort || null, sshUsername || null,
            sshPassword || null, sshPrivateKey || null,
            serverId, userId
        ]);
        connection.release();
        return { success: true, message: '服务器更新成功' };
    }
    catch (error) {
        console.error('更新服务器失败:', error);
        return { success: false, message: '更新服务器失败' };
    }
}
// 删除服务器
export async function deleteServer(serverId, userId) {
    try {
        const connection = await pool.getConnection();
        // 检查服务器是否属于该用户
        const [servers] = await connection.execute('SELECT id FROM servers WHERE id = ? AND user_id = ?', [serverId, userId]);
        if (servers.length === 0) {
            connection.release();
            return { success: false, message: '服务器不存在或无权限删除' };
        }
        // 删除服务器
        await connection.execute('DELETE FROM servers WHERE id = ? AND user_id = ?', [serverId, userId]);
        connection.release();
        return {
            success: true,
            message: '服务器删除成功'
        };
    }
    catch (error) {
        console.error('删除服务器失败:', error);
        return { success: false, message: '删除服务器失败，请稍后重试' };
    }
}
// 更新服务器状态
export async function updateServerStatus(serverId, status) {
    try {
        const connection = await pool.getConnection();
        await connection.execute('UPDATE servers SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?', [status, serverId]);
        connection.release();
        return { success: true };
    }
    catch (error) {
        console.error('更新服务器状态失败:', error);
        return { success: false, message: '更新服务器状态失败' };
    }
}
// 更新备份任务状态
export async function updateBackupTaskStatus(taskId, userId, status) {
    try {
        const connection = await pool.getConnection();
        // 验证任务是否属于该用户
        const [taskCheck] = await connection.execute('SELECT id FROM backup_tasks WHERE id = ? AND user_id = ?', [taskId, userId]);
        if (taskCheck.length === 0) {
            connection.release();
            return { success: false, message: '备份任务不存在或无权限访问' };
        }
        // 更新任务状态
        await connection.execute('UPDATE backup_tasks SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND user_id = ?', [status, taskId, userId]);
        connection.release();
        console.log(`备份任务状态已更新: 任务ID=${taskId}, 新状态=${status}`);
        return {
            success: true,
            message: `任务已${status === 'active' ? '启用' : status === 'inactive' ? '禁用' : '更新状态'}`
        };
    }
    catch (error) {
        console.error('更新备份任务状态失败:', error);
        return { success: false, message: '更新备份任务状态失败' };
    }
}
// 执行数据库备份
export async function executeBackup(taskId) {
    try {
        const connection = await pool.getConnection();
        // 获取备份任务详情 - 明确指定字段名以避免冲突
        const [taskRows] = await connection.execute(`SELECT
        bt.id as task_id,
        bt.user_id,
        bt.server_id,
        bt.name as task_name,
        bt.database_name,
        bt.backup_type,
        bt.schedule_type,
        bt.schedule_time,
        bt.schedule_day,
        bt.custom_interval_minutes,
        bt.backup_path,
        bt.retention_days,
        bt.status as task_status,
        bt.last_backup_time,
        bt.last_backup_size,
        bt.created_at as task_created_at,
        bt.updated_at as task_updated_at,
        s.id as server_id,
        s.name as server_name,
        s.host,
        s.port,
        s.username,
        s.password,
        s.ssh_host,
        s.ssh_port,
        s.ssh_username,
        s.ssh_password,
        s.ssh_private_key,
        s.status as server_status
       FROM backup_tasks bt
       JOIN servers s ON bt.server_id = s.id
       WHERE bt.id = ?`, [taskId]);
        if (taskRows.length === 0) {
            connection.release();
            return { success: false, message: '备份任务不存在' };
        }
        const task = taskRows[0];
        connection.release();
        // 执行备份
        const backupResult = await performMySQLBackup(task);
        if (backupResult.success) {
            const now = new Date();
            // 记录备份历史，使用实际执行的备份类型
            const historyResult = await createBackupHistory({
                taskId: taskId,
                backupType: backupResult.actualBackupType || task.backup_type,
                filePath: backupResult.filePath,
                fileSize: backupResult.fileSize,
                startTime: now,
                binlogFile: backupResult.binlogFile,
                binlogPosition: backupResult.binlogPosition
            });
            // 立即更新为完成状态
            if (historyResult.success && historyResult.historyId) {
                await updateBackupHistory(historyResult.historyId, {
                    endTime: now,
                    status: 'completed'
                });
                // 更新备份任务的最后备份时间和大小
                try {
                    const connection = await pool.getConnection();
                    await connection.execute('UPDATE backup_tasks SET last_backup_time = ?, last_backup_size = ? WHERE id = ?', [now, backupResult.fileSize || 0, taskId]);
                    connection.release();
                    console.log(`Updated task ${taskId} last_backup_time to:`, now);
                }
                catch (error) {
                    console.error('Failed to update last_backup_time:', error);
                }
            }
        }
        return backupResult;
    }
    catch (error) {
        console.error('执行备份失败:', error);
        return { success: false, message: '执行备份失败' };
    }
}
// 检查用户权限
async function checkUserPrivileges(task) {
    try {
        const connection = await mysql.createConnection({
            host: task.host,
            port: task.port,
            user: task.username,
            password: task.password,
            database: task.database_name
        });
        // 检查用户权限
        const [grants] = await connection.execute('SHOW GRANTS FOR CURRENT_USER()');
        const grantText = grants.map((row) => Object.values(row)[0]).join(' ').toUpperCase();
        await connection.end();
        return {
            hasReload: grantText.includes('RELOAD') || grantText.includes('ALL PRIVILEGES'),
            hasLockTables: grantText.includes('LOCK TABLES') || grantText.includes('ALL PRIVILEGES'),
            hasShowView: grantText.includes('SHOW VIEW') || grantText.includes('ALL PRIVILEGES')
        };
    }
    catch (error) {
        console.log('Permission check failed, using conservative mode:', error);
        return {
            hasReload: false,
            hasLockTables: false,
            hasShowView: false
        };
    }
}
// 执行MySQL备份（在服务器端）
async function performMySQLBackup(task) {
    const { NodeSSH } = await import('node-ssh');
    let ssh = null;
    try {
        // 检查是否需要先进行全量备份
        const needsFullBackup = await checkIfNeedsFullBackup(task.task_id);
        let actualBackupType = task.backup_type;
        if (needsFullBackup && task.backup_type === 'incremental') {
            console.log('No full backup found, performing full backup first');
            actualBackupType = 'full';
        }
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const fileName = `${task.database_name}_${actualBackupType}_${timestamp}.sql`;
        const serverFilePath = path.posix.join(task.backup_path, fileName);
        // 建立SSH连接到服务器
        ssh = new NodeSSH();
        const sshConfig = {
            host: task.ssh_host || task.host,
            port: task.ssh_port || 22,
            username: task.ssh_username,
            password: task.ssh_password
        };
        if (task.ssh_private_key) {
            sshConfig.privateKey = task.ssh_private_key;
            delete sshConfig.password;
        }
        console.log('Connecting to server via SSH:', { host: sshConfig.host, port: sshConfig.port, username: sshConfig.username });
        await ssh.connect(sshConfig);
        console.log('SSH connection established successfully');
        // 在服务器上创建备份目录
        await ssh.execCommand(`mkdir -p "${task.backup_path}"`);
        console.log('Backup directory created on server:', task.backup_path);
        // 检查用户权限
        const privileges = await checkUserPrivileges(task);
        // 构建在服务器上执行的mysqldump命令
        const mysqldumpArgs = [
            `--host=${task.host}`,
            `--port=${task.port}`,
            `--user=${task.username}`,
            `--password=${task.password}`,
            '--single-transaction',
            '--lock-tables=false',
            '--no-tablespaces',
            '--skip-add-locks',
            '--skip-disable-keys',
            '--skip-set-charset',
            '--default-character-set=utf8mb4',
            '--skip-lock-tables', // 跳过锁表操作
            '--quick', // 快速导出，减少内存使用
            '--compress', // 启用压缩传输
            '--complete-insert', // 生成完整的INSERT语句
            '--extended-insert', // 使用多行INSERT语法
            '--add-drop-table', // 添加DROP TABLE语句
            '--hex-blob' // 处理二进制数据
        ];
        // 根据实际备份类型添加不同参数
        if (actualBackupType === 'full') {
            // 全量备份：导出完整的数据库结构和数据
            // 基础参数已经包含了 --complete-insert, --extended-insert, --add-drop-table, --hex-blob
            // 移除 --flush-logs 因为需要RELOAD权限
            // mysqldumpArgs.push('--flush-logs'); // 刷新binlog
            // 使用 --master-data=2 替代 --source-data=2 (兼容性更好)
            // 但这也需要RELOAD权限，所以我们跳过binlog位置记录
            // mysqldumpArgs.push('--master-data=2'); // 记录binlog位置
            if (privileges.hasShowView) {
                mysqldumpArgs.push('--routines');
                mysqldumpArgs.push('--triggers');
                mysqldumpArgs.push('--events');
            }
            mysqldumpArgs.push(task.database_name);
        }
        else if (actualBackupType === 'incremental') {
            // 增量备份：基于binlog的真正增量备份
            const lastBinlogInfo = await getLastBinlogInfo(task.task_id);
            if (lastBinlogInfo) {
                // 使用mysqlbinlog进行增量备份
                const binlogBackupResult = await performBinlogBackup(task, lastBinlogInfo, serverFilePath, ssh);
                if (binlogBackupResult.success) {
                    return {
                        success: true,
                        filePath: serverFilePath,
                        fileSize: binlogBackupResult.fileSize,
                        actualBackupType: 'incremental',
                        binlogFile: binlogBackupResult.endBinlogFile,
                        binlogPosition: binlogBackupResult.endBinlogPosition
                    };
                }
                else {
                    throw new Error(`Binlog备份失败: ${binlogBackupResult.message}`);
                }
            }
            else {
                // 如果没有找到上次binlog信息，执行全量备份
                console.log('No previous binlog info found, performing full backup');
                actualBackupType = 'full';
                // 基础参数已经包含了 --complete-insert, --extended-insert, --add-drop-table, --hex-blob
                // 移除需要RELOAD权限的参数
                // mysqldumpArgs.push('--flush-logs');
                // mysqldumpArgs.push('--source-data=2');
                if (privileges.hasShowView) {
                    mysqldumpArgs.push('--routines');
                    mysqldumpArgs.push('--triggers');
                    mysqldumpArgs.push('--events');
                }
                mysqldumpArgs.push(task.database_name);
            }
        }
        // 在服务器上执行mysqldump命令
        const mysqldumpCommand = `mysqldump ${mysqldumpArgs.join(' ')} > "${serverFilePath}"`;
        console.log('Executing mysqldump command on server:', mysqldumpCommand);
        const result = await ssh.execCommand(mysqldumpCommand);
        if (result.code !== 0) {
            throw new Error(`Mysqldump failed: ${result.stderr}`);
        }
        // 获取备份文件大小
        const sizeResult = await ssh.execCommand(`stat -c%s "${serverFilePath}"`);
        const fileSize = parseInt(sizeResult.stdout.trim()) || 0;
        // 获取当前binlog位置（用于后续增量备份）
        let binlogFile = null;
        let binlogPosition = null;
        if (actualBackupType === 'full') {
            const currentBinlogInfo = await getCurrentBinlogInfo(task, ssh);
            if (currentBinlogInfo.success) {
                binlogFile = currentBinlogInfo.binlogFile;
                binlogPosition = currentBinlogInfo.binlogPosition;
            }
        }
        console.log('Backup completed successfully on server:', {
            filePath: serverFilePath,
            fileSize: fileSize,
            binlogFile: binlogFile,
            binlogPosition: binlogPosition
        });
        return {
            success: true,
            message: '备份成功完成',
            filePath: serverFilePath,
            fileSize: fileSize,
            binlogFile: binlogFile || undefined,
            binlogPosition: binlogPosition || undefined,
            actualBackupType: actualBackupType
        };
    }
    catch (error) {
        console.error('Server backup failed:', error);
        return {
            success: false,
            message: `服务器备份失败: ${error.message}`
        };
    }
    finally {
        if (ssh) {
            try {
                ssh.dispose();
            }
            catch (e) {
                console.error('Error closing SSH connection:', e);
            }
        }
    }
}
// 获取服务器端备份文件列表
export async function getServerBackupFiles(serverId, userId) {
    const { NodeSSH } = await import('node-ssh');
    let ssh = null;
    try {
        const connection = await pool.getConnection();
        // 获取服务器信息
        const [servers] = await connection.execute('SELECT * FROM servers WHERE id = ? AND user_id = ?', [serverId, userId]);
        connection.release();
        if (servers.length === 0) {
            return { success: false, message: '服务器不存在或无权限' };
        }
        const server = servers[0];
        // 获取该服务器的备份任务路径
        const taskConnection = await pool.getConnection();
        const [tasks] = await taskConnection.execute('SELECT DISTINCT backup_path FROM backup_tasks WHERE server_id = ? AND user_id = ?', [serverId, userId]);
        taskConnection.release();
        if (tasks.length === 0) {
            return { success: true, files: [], message: '该服务器暂无备份任务' };
        }
        // 建立SSH连接
        ssh = new NodeSSH();
        const sshConfig = {
            host: server.ssh_host || server.host,
            port: server.ssh_port || 22,
            username: server.ssh_username,
            password: server.ssh_password
        };
        if (server.ssh_private_key) {
            sshConfig.privateKey = server.ssh_private_key;
            delete sshConfig.password;
        }
        await ssh.connect(sshConfig);
        const allFiles = [];
        // 遍历所有备份路径
        for (const task of tasks) {
            const backupPath = task.backup_path;
            try {
                // 列出备份目录中的文件
                const result = await ssh.execCommand(`find "${backupPath}" -name "*.sql" -type f -exec stat -c "%n|%s|%Y" {} \\;`);
                if (result.code === 0 && result.stdout.trim()) {
                    const lines = result.stdout.trim().split('\n');
                    for (const line of lines) {
                        const [filePath, size, mtime] = line.split('|');
                        if (filePath && size && mtime) {
                            allFiles.push({
                                path: filePath,
                                name: path.basename(filePath),
                                size: parseInt(size),
                                modifiedTime: new Date(parseInt(mtime) * 1000),
                                backupPath: backupPath
                            });
                        }
                    }
                }
            }
            catch (error) {
                console.error(`Error listing files in ${backupPath}:`, error);
            }
        }
        // 按修改时间排序（最新的在前）
        allFiles.sort((a, b) => b.modifiedTime.getTime() - a.modifiedTime.getTime());
        return {
            success: true,
            files: allFiles,
            message: `找到 ${allFiles.length} 个备份文件`
        };
    }
    catch (error) {
        console.error('获取服务器备份文件失败:', error);
        return {
            success: false,
            message: `获取服务器备份文件失败: ${error.message}`
        };
    }
    finally {
        if (ssh) {
            try {
                ssh.dispose();
            }
            catch (e) {
                console.error('Error closing SSH connection:', e);
            }
        }
    }
}
// 下载服务器端备份文件
export async function downloadServerBackupFile(serverId, filePath, userId) {
    const { NodeSSH } = await import('node-ssh');
    let ssh = null;
    try {
        const connection = await pool.getConnection();
        // 获取服务器信息
        const [servers] = await connection.execute('SELECT * FROM servers WHERE id = ? AND user_id = ?', [serverId, userId]);
        connection.release();
        if (servers.length === 0) {
            return { success: false, message: '服务器不存在或无权限' };
        }
        const server = servers[0];
        // 建立SSH连接
        ssh = new NodeSSH();
        const sshConfig = {
            host: server.ssh_host || server.host,
            port: server.ssh_port || 22,
            username: server.ssh_username,
            password: server.ssh_password
        };
        if (server.ssh_private_key) {
            sshConfig.privateKey = server.ssh_private_key;
            delete sshConfig.password;
        }
        await ssh.connect(sshConfig);
        // 检查文件是否存在
        const checkResult = await ssh.execCommand(`test -f "${filePath}" && echo "exists" || echo "not found"`);
        if (checkResult.stdout.trim() !== 'exists') {
            return { success: false, message: '备份文件不存在' };
        }
        // 创建本地临时目录
        const os = await import('os');
        const tempDir = path.join(os.tmpdir(), 'mysql-backup-downloads');
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }
        // 生成本地文件路径
        const fileName = path.basename(filePath);
        const localPath = path.join(tempDir, `${Date.now()}_${fileName}`);
        // 使用SSH下载文件到本地
        await ssh.getFile(localPath, filePath);
        // 验证文件是否下载成功
        if (!fs.existsSync(localPath)) {
            return { success: false, message: '文件下载失败' };
        }
        return {
            success: true,
            localPath: localPath,
            fileName: fileName,
            content: undefined // 不再返回内容，只返回本地路径
        };
    }
    catch (error) {
        console.error('下载服务器备份文件失败:', error);
        return {
            success: false,
            message: `下载备份文件失败: ${error.message}`
        };
    }
    finally {
        if (ssh) {
            try {
                ssh.dispose();
            }
            catch (e) {
                console.error('Error closing SSH connection:', e);
            }
        }
    }
}
// 下载服务器端备份文件到本地（专门用于文件下载）
export async function downloadServerBackupFileToLocal(serverId, filePath, userId) {
    const { NodeSSH } = await import('node-ssh');
    let ssh = null;
    try {
        const connection = await pool.getConnection();
        // 获取服务器信息
        const [servers] = await connection.execute('SELECT * FROM servers WHERE id = ? AND user_id = ?', [serverId, userId]);
        connection.release();
        if (servers.length === 0) {
            return { success: false, message: '服务器不存在或无权限' };
        }
        const server = servers[0];
        // 建立SSH连接
        ssh = new NodeSSH();
        const sshConfig = {
            host: server.ssh_host || server.host,
            port: server.ssh_port || 22,
            username: server.ssh_username,
            password: server.ssh_password
        };
        if (server.ssh_private_key) {
            sshConfig.privateKey = server.ssh_private_key;
            delete sshConfig.password;
        }
        await ssh.connect(sshConfig);
        // 检查文件是否存在
        const checkResult = await ssh.execCommand(`test -f "${filePath}" && echo "exists" || echo "not found"`);
        if (checkResult.stdout.trim() !== 'exists') {
            return { success: false, message: '备份文件不存在' };
        }
        // 创建本地临时目录
        const os = await import('os');
        const tempDir = path.join(os.tmpdir(), 'mysql-backup-downloads');
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }
        // 生成本地文件路径
        const fileName = path.basename(filePath);
        const localPath = path.join(tempDir, `${Date.now()}_${fileName}`);
        // 使用SSH下载文件到本地
        await ssh.getFile(localPath, filePath);
        // 验证文件是否下载成功
        if (!fs.existsSync(localPath)) {
            return { success: false, message: '文件下载失败' };
        }
        return {
            success: true,
            localPath: localPath,
            fileName: fileName
        };
    }
    catch (error) {
        console.error('下载服务器备份文件到本地失败:', error);
        return {
            success: false,
            message: `下载备份文件失败: ${error.message}`
        };
    }
    finally {
        if (ssh) {
            try {
                ssh.dispose();
            }
            catch (e) {
                console.error('Error closing SSH connection:', e);
            }
        }
    }
}
// 检查数据库是否真实存在
export async function checkDatabaseExists(server, databaseName) {
    let connection = null;
    try {
        // 创建到MySQL服务器的连接（不指定数据库）
        const mysqlConfig = {
            host: server.host,
            port: server.port,
            user: server.username,
            password: server.password,
            connectTimeout: 10000
        };
        connection = await mysql.createConnection(mysqlConfig);
        // 查询数据库是否存在
        const [rows] = await connection.execute('SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?', [databaseName]);
        if (rows.length === 0) {
            return {
                success: false,
                message: `数据库 '${databaseName}' 在服务器上不存在`
            };
        }
        // 尝试连接到具体数据库以验证权限
        await connection.end();
        const dbConfig = {
            ...mysqlConfig,
            database: databaseName
        };
        connection = await mysql.createConnection(dbConfig);
        // 测试基本查询权限
        await connection.execute('SELECT 1');
        return {
            success: true,
            message: '数据库验证成功'
        };
    }
    catch (error) {
        console.error('数据库验证失败:', error);
        if (error.code === 'ER_DBACCESS_DENIED_ERROR') {
            return {
                success: false,
                message: `没有访问数据库 '${databaseName}' 的权限`
            };
        }
        else if (error.code === 'ER_BAD_DB_ERROR') {
            return {
                success: false,
                message: `数据库 '${databaseName}' 不存在`
            };
        }
        else if (error.code === 'ECONNREFUSED') {
            return {
                success: false,
                message: '无法连接到MySQL服务器'
            };
        }
        else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
            return {
                success: false,
                message: 'MySQL用户名或密码错误'
            };
        }
        else {
            return {
                success: false,
                message: `数据库连接失败: ${error.message}`
            };
        }
    }
    finally {
        if (connection) {
            try {
                await connection.end();
            }
            catch (e) {
                // 忽略关闭连接时的错误
            }
        }
    }
}
// 通过服务器ID测试数据库连接
export async function testDatabaseConnectionById(serverId, databaseName, userId) {
    try {
        const connection = await pool.getConnection();
        // 获取服务器信息
        const [servers] = await connection.execute('SELECT * FROM servers WHERE id = ? AND user_id = ?', [serverId, userId]);
        connection.release();
        if (servers.length === 0) {
            return {
                success: false,
                message: '服务器不存在或无权限访问'
            };
        }
        const server = servers[0];
        // 使用checkDatabaseExists函数验证数据库
        return await checkDatabaseExists(server, databaseName);
    }
    catch (error) {
        console.error('测试数据库连接失败:', error);
        return {
            success: false,
            message: `测试数据库连接失败: ${error.message}`
        };
    }
}
// 创建备份任务
export async function createBackupTask(taskData) {
    try {
        const connection = await pool.getConnection();
        // 检查任务名称是否已存在
        const [existing] = await connection.execute('SELECT id FROM backup_tasks WHERE user_id = ? AND name = ?', [taskData.userId, taskData.name]);
        if (existing.length > 0) {
            connection.release();
            return { success: false, message: '备份任务名称已存在' };
        }
        // 验证服务器是否属于该用户并获取服务器信息
        const [servers] = await connection.execute('SELECT * FROM servers WHERE id = ? AND user_id = ?', [taskData.serverId, taskData.userId]);
        if (servers.length === 0) {
            connection.release();
            return { success: false, message: '服务器不存在或无权限' };
        }
        const server = servers[0];
        // 验证数据库是否真实存在
        const databaseExists = await checkDatabaseExists(server, taskData.databaseName);
        if (!databaseExists.success) {
            connection.release();
            return {
                success: false,
                message: `数据库验证失败: ${databaseExists.message}`
            };
        }
        // 插入新备份任务
        const [result] = await connection.execute(`INSERT INTO backup_tasks (user_id, server_id, name, database_name, backup_type, schedule_type, schedule_time, schedule_day, custom_interval_minutes, backup_path, retention_days)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, [
            taskData.userId,
            taskData.serverId,
            taskData.name,
            taskData.databaseName,
            taskData.backupType,
            taskData.scheduleType,
            taskData.scheduleTime || null,
            taskData.scheduleDay || null,
            taskData.customIntervalMinutes || null,
            taskData.backupPath,
            taskData.retentionDays
        ]);
        connection.release();
        return {
            success: true,
            message: '备份任务创建成功',
            taskId: result.insertId
        };
    }
    catch (error) {
        console.error('创建备份任务失败:', error);
        // 提供详细的错误信息
        let errorMessage = '创建备份任务失败';
        if (error.code === 'ER_DUP_ENTRY') {
            errorMessage = '备份任务名称已存在，请使用其他名称';
        }
        else if (error.code === 'ER_NO_SUCH_TABLE') {
            errorMessage = '数据库表不存在，请检查数据库配置';
        }
        else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
            errorMessage = '数据库访问权限不足，请检查用户权限';
        }
        else if (error.code === 'ECONNREFUSED') {
            errorMessage = '无法连接到数据库服务器，请检查服务器状态';
        }
        else if (error.code === 'ETIMEDOUT') {
            errorMessage = '数据库连接超时，请检查网络连接';
        }
        else if (error.message) {
            errorMessage = `创建备份任务失败: ${error.message}`;
        }
        else {
            errorMessage = `创建备份任务失败: ${error.toString()}`;
        }
        return {
            success: false,
            message: errorMessage,
            errorCode: error.code,
            errorDetails: error.message
        };
    }
}
// 获取用户的备份任务列表
export async function getUserBackupTasks(userId) {
    try {
        const connection = await pool.getConnection();
        const [tasks] = await connection.execute(`SELECT
        bt.id,
        bt.name,
        bt.database_name,
        bt.backup_type,
        bt.schedule_type,
        bt.schedule_time,
        bt.schedule_day,
        bt.custom_interval_minutes,
        bt.backup_path,
        bt.retention_days,
        bt.status,
        bt.last_backup_time,
        bt.last_backup_size,
        bt.created_at,
        bt.updated_at,
        bt.server_id,
        s.name as server_name,
        s.host as server_host
       FROM backup_tasks bt
       JOIN servers s ON bt.server_id = s.id
       WHERE bt.user_id = ?
       ORDER BY bt.created_at DESC`, [userId]);
        connection.release();
        return {
            success: true,
            tasks
        };
    }
    catch (error) {
        console.error('获取备份任务列表失败:', error);
        return { success: false, message: '获取备份任务列表失败' };
    }
}
// 删除备份任务
export async function deleteBackupTask(taskId, userId) {
    try {
        const connection = await pool.getConnection();
        // 检查任务是否属于该用户
        const [tasks] = await connection.execute('SELECT id FROM backup_tasks WHERE id = ? AND user_id = ?', [taskId, userId]);
        if (tasks.length === 0) {
            connection.release();
            return { success: false, message: '备份任务不存在或无权限删除' };
        }
        // 删除备份任务
        await connection.execute('DELETE FROM backup_tasks WHERE id = ? AND user_id = ?', [taskId, userId]);
        connection.release();
        return {
            success: true,
            message: '备份任务删除成功'
        };
    }
    catch (error) {
        console.error('删除备份任务失败:', error);
        return { success: false, message: '删除备份任务失败，请稍后重试' };
    }
}
// 创建备份历史记录
export async function createBackupHistory(historyData) {
    try {
        const connection = await pool.getConnection();
        const [result] = await connection.execute(`INSERT INTO backup_history (task_id, backup_type, file_path, file_size, start_time, binlog_file, binlog_position)
       VALUES (?, ?, ?, ?, ?, ?, ?)`, [
            historyData.taskId,
            historyData.backupType,
            historyData.filePath,
            historyData.fileSize,
            historyData.startTime,
            historyData.binlogFile || null,
            historyData.binlogPosition || null
        ]);
        connection.release();
        return {
            success: true,
            historyId: result.insertId
        };
    }
    catch (error) {
        console.error('创建备份历史记录失败:', error);
        return { success: false, message: '创建备份历史记录失败' };
    }
}
// 更新备份历史记录状态
export async function updateBackupHistory(historyId, updateData) {
    try {
        const connection = await pool.getConnection();
        await connection.execute(`UPDATE backup_history
       SET end_time = ?, status = ?, error_message = ?, file_size = ?
       WHERE id = ?`, [
            updateData.endTime || null,
            updateData.status,
            updateData.errorMessage || null,
            updateData.fileSize || null,
            historyId
        ]);
        connection.release();
        return { success: true };
    }
    catch (error) {
        console.error('更新备份历史记录失败:', error);
        return { success: false, message: '更新备份历史记录失败' };
    }
}
// 获取备份历史记录
export async function getBackupHistory(userId, limit = 50) {
    try {
        const connection = await pool.getConnection();
        // 确保limit是有效的数字
        const validLimit = Number.isInteger(limit) && limit > 0 ? limit : 50;
        const [history] = await connection.execute(`SELECT bh.*, bt.name as task_name, s.name as server_name, s.host as server_host
       FROM backup_history bh
       JOIN backup_tasks bt ON bh.task_id = bt.id
       JOIN servers s ON bt.server_id = s.id
       WHERE bt.user_id = ?
       ORDER BY bh.start_time DESC
       LIMIT ${validLimit}`, [userId]);
        connection.release();
        return {
            success: true,
            history
        };
    }
    catch (error) {
        console.error('获取备份历史记录失败:', error);
        return { success: false, message: '获取备份历史记录失败' };
    }
}
// 获取特定备份任务的详细历史记录
export async function getBackupTaskHistory(taskId, userId) {
    try {
        const connection = await pool.getConnection();
        // 首先验证任务是否属于该用户
        const [taskCheck] = await connection.execute('SELECT id, name, database_name FROM backup_tasks WHERE id = ? AND user_id = ?', [taskId, userId]);
        if (taskCheck.length === 0) {
            connection.release();
            return { success: false, message: '备份任务不存在或无权限访问' };
        }
        // 获取该任务的所有备份历史记录
        const [history] = await connection.execute(`SELECT bh.*, bt.name as task_name, bt.database_name, s.name as server_name, s.host as server_host
       FROM backup_history bh
       JOIN backup_tasks bt ON bh.task_id = bt.id
       JOIN servers s ON bt.server_id = s.id
       WHERE bh.task_id = ?
       ORDER BY bh.start_time DESC`, [taskId]);
        connection.release();
        return {
            success: true,
            task: taskCheck[0],
            history
        };
    }
    catch (error) {
        console.error('获取备份任务历史记录失败:', error);
        return { success: false, message: '获取备份任务历史记录失败' };
    }
}
// 获取备份文件路径
export async function getBackupFilePath(historyId, userId) {
    try {
        const connection = await pool.getConnection();
        // 获取备份历史和相关的服务器信息
        const [rows] = await connection.execute(`SELECT bh.file_path, bh.file_size, bh.status, bt.user_id, bt.server_id,
              s.host, s.ssh_host, s.ssh_port, s.ssh_username, s.ssh_password
       FROM backup_history bh
       JOIN backup_tasks bt ON bh.task_id = bt.id
       JOIN servers s ON bt.server_id = s.id
       WHERE bh.id = ? AND bt.user_id = ?`, [historyId, userId]);
        connection.release();
        if (rows.length === 0) {
            return { success: false, message: '备份文件不存在或无权限访问' };
        }
        const backupInfo = rows[0];
        const filePath = backupInfo.file_path;
        // 检查备份状态
        if (backupInfo.status !== 'completed') {
            return { success: false, message: '备份尚未完成，无法下载' };
        }
        // 对于远程服务器上的文件，我们需要先下载到本地
        if (backupInfo.ssh_host || backupInfo.host) {
            try {
                // 尝试下载文件到本地临时目录
                const downloadResult = await downloadServerBackupFileToLocal(backupInfo.server_id, filePath, userId);
                if (downloadResult.success && downloadResult.localPath) {
                    return {
                        success: true,
                        filePath: downloadResult.localPath,
                        isRemoteFile: true,
                        originalPath: filePath
                    };
                }
                else {
                    return {
                        success: false,
                        message: downloadResult.message || '下载远程备份文件失败'
                    };
                }
            }
            catch (error) {
                console.error('下载远程备份文件失败:', error);
                return {
                    success: false,
                    message: `下载远程备份文件失败: ${error.message}`
                };
            }
        }
        else {
            // 本地文件，直接检查是否存在
            if (!fs.existsSync(filePath)) {
                return { success: false, message: '备份文件已被删除或移动' };
            }
            return {
                success: true,
                filePath,
                isRemoteFile: false
            };
        }
    }
    catch (error) {
        console.error('获取备份文件路径失败:', error);
        return { success: false, message: '获取备份文件路径失败' };
    }
}
// 下载备份文件到用户选择的位置
export async function downloadBackupFile(historyId, userId, fileName) {
    const { dialog } = require('electron');
    const { NodeSSH } = require('node-ssh');
    try {
        console.log('Starting download for backup:', { historyId, userId, fileName });
        const connection = await pool.getConnection();
        // 验证备份历史记录是否属于该用户并获取服务器信息
        const [historyRows] = await connection.execute(`SELECT bh.file_path, bh.task_id, bh.status, bt.name as task_name, bt.database_name,
              s.ssh_host, s.ssh_port, s.ssh_username, s.ssh_password, s.host
       FROM backup_history bh
       JOIN backup_tasks bt ON bh.task_id = bt.id
       JOIN servers s ON bt.server_id = s.id
       WHERE bh.id = ? AND bt.user_id = ?`, [historyId, userId]);
        connection.release();
        if (historyRows.length === 0) {
            console.error('Backup not found or no permission');
            return { success: false, message: '备份文件不存在或无权限访问' };
        }
        const history = historyRows[0];
        console.log('Found backup history:', history);
        // 检查备份状态
        if (history.status !== 'completed') {
            return { success: false, message: '备份尚未完成，无法下载' };
        }
        // 显示保存对话框
        const result = await dialog.showSaveDialog({
            title: '保存备份文件',
            defaultPath: fileName,
            filters: [
                { name: 'SQL文件', extensions: ['sql'] },
                { name: '所有文件', extensions: ['*'] }
            ]
        });
        if (result.canceled || !result.filePath) {
            return { success: false, message: '用户取消了下载' };
        }
        console.log('User selected save path:', result.filePath);
        // 通过SSH下载文件
        const ssh = new NodeSSH();
        const sshConfig = {
            host: history.ssh_host || history.host,
            port: history.ssh_port || 22,
            username: history.ssh_username,
            password: history.ssh_password
        };
        console.log('Connecting to server for download:', sshConfig);
        await ssh.connect(sshConfig);
        // 检查远程文件是否存在
        const checkResult = await ssh.execCommand(`test -f "${history.file_path}" && echo "exists" || echo "not found"`);
        if (checkResult.stdout.trim() !== 'exists') {
            ssh.dispose();
            return { success: false, message: '服务器上的备份文件不存在' };
        }
        console.log('Downloading file from:', history.file_path, 'to:', result.filePath);
        // 下载文件
        await ssh.getFile(result.filePath, history.file_path);
        ssh.dispose();
        console.log('File downloaded successfully');
        return {
            success: true,
            message: '文件下载成功',
            localPath: result.filePath
        };
    }
    catch (error) {
        console.error('下载备份文件失败:', error);
        return { success: false, message: `下载失败: ${error.message || error.toString()}` };
    }
}
// 获取需要执行的定时任务
export async function getScheduledTasks() {
    try {
        const connection = await pool.getConnection();
        const now = new Date();
        const currentTime = now.toTimeString().slice(0, 5); // HH:MM格式
        const currentDay = now.getDay(); // 0=周日, 1=周一, ..., 6=周六
        const currentDate = now.getDate(); // 1-31
        // 获取所有活跃的定时任务
        const [allTasks] = await connection.execute(`SELECT
        bt.id as task_id,
        bt.name as task_name,
        bt.database_name,
        bt.backup_type,
        bt.schedule_type,
        bt.schedule_time,
        bt.schedule_day,
        bt.custom_interval_minutes,
        bt.backup_path,
        bt.retention_days,
        bt.status,
        bt.last_backup_time,
        bt.last_backup_size,
        bt.server_id,
        s.name as server_name,
        s.host,
        s.port,
        s.username,
        s.password,
        s.ssh_host,
        s.ssh_port,
        s.ssh_username,
        s.ssh_password,
        s.ssh_private_key
       FROM backup_tasks bt
       JOIN servers s ON bt.server_id = s.id
       WHERE bt.status = 'active'
       AND bt.schedule_type != 'manual'`, []);
        // 过滤出需要执行的任务
        const tasksToExecute = [];
        for (const task of allTasks) {
            let shouldExecute = false;
            switch (task.schedule_type) {
                case 'hourly':
                    shouldExecute = true; // 每小时检查一次
                    break;
                case 'daily':
                    shouldExecute = task.schedule_time === currentTime;
                    break;
                case 'weekly':
                    shouldExecute = task.schedule_time === currentTime && task.schedule_day === currentDay;
                    break;
                case 'monthly':
                    shouldExecute = task.schedule_time === currentTime && task.schedule_day === currentDate;
                    break;
                case 'custom':
                    // 自定义间隔逻辑
                    if (task.custom_interval_minutes) {
                        if (!task.last_backup_time) {
                            // 如果从未备份过，应该执行
                            shouldExecute = true;
                        }
                        else {
                            const lastBackupTime = new Date(task.last_backup_time);
                            const minutesSinceLastBackup = (now.getTime() - lastBackupTime.getTime()) / (1000 * 60);
                            shouldExecute = minutesSinceLastBackup >= task.custom_interval_minutes;
                        }
                    }
                    else if (task.custom_interval_minutes && !task.last_backup_time) {
                        // 如果从未备份过，则立即执行
                        shouldExecute = true;
                    }
                    break;
            }
            if (shouldExecute) {
                tasksToExecute.push(task);
            }
        }
        const tasks = tasksToExecute;
        connection.release();
        return {
            success: true,
            tasks
        };
    }
    catch (error) {
        console.error('获取定时任务失败:', error);
        return { success: false, message: '获取定时任务失败', tasks: [] };
    }
}
// 测试服务器连接（包括SSH隧道和MySQL连接）
export async function testServerConnection(serverConfig) {
    let ssh = null;
    let connection = null;
    try {
        console.log('Starting server connection test:', {
            host: serverConfig.host,
            port: serverConfig.port,
            hasSSH: !!serverConfig.sshHost
        });
        // 如果配置了SSH，先测试SSH连接
        if (serverConfig.sshHost && serverConfig.sshUsername) {
            console.log('Testing SSH connection...');
            ssh = new NodeSSH();
            const sshConfig = {
                host: serverConfig.sshHost,
                port: serverConfig.sshPort || 22,
                username: serverConfig.sshUsername,
            };
            // 优先使用私钥，否则使用密码
            if (serverConfig.sshPrivateKey) {
                sshConfig.privateKey = serverConfig.sshPrivateKey;
            }
            else if (serverConfig.sshPassword) {
                sshConfig.password = serverConfig.sshPassword;
            }
            else {
                return {
                    success: false,
                    message: 'SSH连接需要提供密码或私钥',
                    details: { step: 'ssh_auth_validation' }
                };
            }
            try {
                await ssh.connect(sshConfig);
                console.log('SSH connection successful');
            }
            catch (sshError) {
                console.error('SSH连接失败:', sshError);
                return {
                    success: false,
                    message: `SSH连接失败: ${sshError instanceof Error ? sshError.message : String(sshError)}`,
                    details: {
                        step: 'ssh_connection',
                        sshHost: serverConfig.sshHost,
                        sshPort: serverConfig.sshPort,
                        sshUsername: serverConfig.sshUsername,
                        errorMessage: sshError instanceof Error ? sshError.message : String(sshError)
                    }
                };
            }
        }
        // 测试MySQL连接
        console.log('Testing MySQL connection...');
        const mysqlConfig = {
            host: serverConfig.host,
            port: serverConfig.port,
            user: serverConfig.username,
            password: serverConfig.password,
            connectTimeout: 10000
        };
        try {
            connection = await mysql.createConnection(mysqlConfig);
            console.log('MySQL connection successful');
            // 获取MySQL版本信息
            const [rows] = await connection.execute('SELECT VERSION() as version');
            const version = rows[0]?.version;
            // 测试基本权限
            await connection.execute('SHOW DATABASES');
            return {
                success: true,
                message: 'MySQL连接测试成功',
                details: {
                    version,
                    host: serverConfig.host,
                    port: serverConfig.port,
                    username: serverConfig.username,
                    sshEnabled: !!serverConfig.sshHost
                }
            };
        }
        catch (mysqlError) {
            console.error('MySQL连接失败:', mysqlError);
            let errorMessage = 'MySQL连接失败';
            if (mysqlError instanceof Error) {
                if (mysqlError.message.includes('ECONNREFUSED')) {
                    errorMessage = `无法连接到MySQL服务器 ${serverConfig.host}:${serverConfig.port}，请检查服务器地址和端口是否正确`;
                }
                else if (mysqlError.message.includes('Access denied')) {
                    errorMessage = `MySQL认证失败，请检查用户名和密码是否正确`;
                }
                else if (mysqlError.message.includes('ETIMEDOUT')) {
                    errorMessage = `连接MySQL服务器超时，请检查网络连接和防火墙设置`;
                }
                else {
                    errorMessage = `MySQL连接失败: ${mysqlError.message}`;
                }
            }
            return {
                success: false,
                message: errorMessage,
                details: {
                    step: 'mysql_connection',
                    host: serverConfig.host,
                    port: serverConfig.port,
                    username: serverConfig.username,
                    errorMessage: mysqlError instanceof Error ? mysqlError.message : String(mysqlError)
                }
            };
        }
    }
    catch (error) {
        console.error('连接测试过程中发生未知错误:', error);
        return {
            success: false,
            message: `连接测试失败: ${error instanceof Error ? error.message : String(error)}`,
            details: {
                step: 'unknown_error',
                errorMessage: error instanceof Error ? error.message : String(error)
            }
        };
    }
    finally {
        // 清理连接
        if (connection) {
            try {
                await connection.end();
            }
            catch (e) {
                console.error('关闭MySQL连接时出错:', e);
            }
        }
        if (ssh) {
            try {
                ssh.dispose();
            }
            catch (e) {
                console.error('关闭SSH连接时出错:', e);
            }
        }
    }
}
// 测试数据库是否存在
export async function testDatabaseExists(serverConfig, databaseName) {
    let connection = null;
    try {
        console.log('Testing database existence:', { database: databaseName });
        const mysqlConfig = {
            host: serverConfig.host,
            port: serverConfig.port,
            user: serverConfig.username,
            password: serverConfig.password,
            connectTimeout: 10000
        };
        connection = await mysql.createConnection(mysqlConfig);
        // 查询数据库是否存在
        const [rows] = await connection.execute('SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?', [databaseName]);
        const exists = rows.length > 0;
        return {
            success: true,
            exists,
            message: exists ? `数据库 ${databaseName} 存在` : `数据库 ${databaseName} 不存在`
        };
    }
    catch (error) {
        console.error('测试数据库存在性失败:', error);
        return {
            success: false,
            message: `测试数据库失败: ${error instanceof Error ? error.message : String(error)}`,
            details: {
                errorMessage: error instanceof Error ? error.message : String(error)
            }
        };
    }
    finally {
        if (connection) {
            try {
                await connection.end();
            }
            catch (e) {
                console.error('关闭MySQL连接时出错:', e);
            }
        }
    }
}
// 关闭数据库连接
export async function closeDatabase() {
    if (pool) {
        await pool.end();
    }
}
// 生成完整的SQL文件（从全量备份到指定时间点）
export async function generateCompleteBackupSQL(taskId, targetHistoryId, userId) {
    try {
        const connection = await pool.getConnection();
        // 验证任务权限
        const [taskCheck] = await connection.execute('SELECT id, name, database_name, backup_path FROM backup_tasks WHERE id = ? AND user_id = ?', [taskId, userId]);
        if (taskCheck.length === 0) {
            connection.release();
            return { success: false, message: '备份任务不存在或无权限访问' };
        }
        const task = taskCheck[0];
        // 获取目标备份记录
        const [targetBackup] = await connection.execute('SELECT * FROM backup_history WHERE id = ? AND task_id = ?', [targetHistoryId, taskId]);
        if (targetBackup.length === 0) {
            connection.release();
            return { success: false, message: '目标备份记录不存在' };
        }
        const target = targetBackup[0];
        // 获取该任务的所有备份记录，按时间排序
        const [allBackups] = await connection.execute(`SELECT * FROM backup_history
       WHERE task_id = ? AND start_time <= ? AND status = 'completed'
       ORDER BY start_time ASC`, [taskId, target.start_time]);
        connection.release();
        if (allBackups.length === 0) {
            return { success: false, message: '没有找到可用的备份记录' };
        }
        // 找到最近的全量备份
        let fullBackup = null;
        let incrementalBackups = [];
        for (let i = allBackups.length - 1; i >= 0; i--) {
            if (allBackups[i].backup_type === 'full') {
                fullBackup = allBackups[i];
                incrementalBackups = allBackups.slice(i + 1);
                break;
            }
        }
        if (!fullBackup) {
            return { success: false, message: '没有找到全量备份，无法生成完整SQL文件' };
        }
        // 生成合并的SQL文件
        const outputDir = path.join(task.backup_path, 'merged');
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const outputFileName = `${task.database_name}_complete_${timestamp}.sql`;
        const outputPath = path.join(outputDir, outputFileName);
        // 创建输出文件
        const outputStream = fs.createWriteStream(outputPath);
        // 写入文件头注释
        outputStream.write(`-- MySQL 完整备份文件\n`);
        outputStream.write(`-- 数据库: ${task.database_name}\n`);
        outputStream.write(`-- 生成时间: ${new Date().toISOString()}\n`);
        outputStream.write(`-- 基于全量备份: ${fullBackup.file_path}\n`);
        outputStream.write(`-- 包含增量备份数量: ${incrementalBackups.length}\n`);
        outputStream.write(`-- 恢复到时间点: ${target.start_time}\n`);
        outputStream.write(`\n-- ========================================\n\n`);
        // 获取服务器连接信息
        const serverInfo = await getServerInfoByTaskId(taskId);
        if (!serverInfo.success) {
            outputStream.end();
            return { success: false, message: `获取服务器信息失败: ${serverInfo.message}` };
        }
        // 建立SSH连接下载备份文件
        const { NodeSSH } = await import('node-ssh');
        const ssh = new NodeSSH();
        try {
            await ssh.connect({
                host: serverInfo.server.ssh_host || serverInfo.server.host,
                port: serverInfo.server.ssh_port || 22,
                username: serverInfo.server.ssh_username,
                password: serverInfo.server.ssh_password
            });
            // 写入全量备份内容
            const fullBackupContent = await downloadBackupFileContent(ssh, fullBackup.file_path);
            if (!fullBackupContent.success) {
                outputStream.end();
                return { success: false, message: `下载全量备份文件失败: ${fullBackupContent.message}` };
            }
            outputStream.write(`-- 全量备份内容 (${fullBackup.start_time})\n`);
            outputStream.write(fullBackupContent.content);
            outputStream.write('\n\n');
            // 按顺序写入增量备份内容
            for (const incrementalBackup of incrementalBackups) {
                const incrementalContent = await downloadBackupFileContent(ssh, incrementalBackup.file_path);
                if (incrementalContent.success) {
                    outputStream.write(`-- 增量备份内容 (${incrementalBackup.start_time})\n`);
                    outputStream.write(incrementalContent.content);
                    outputStream.write('\n\n');
                }
                else {
                    console.warn(`下载增量备份文件失败: ${incrementalBackup.file_path}, 错误: ${incrementalContent.message}`);
                    outputStream.write(`-- 警告: 增量备份文件下载失败 (${incrementalBackup.start_time}): ${incrementalContent.message}\n\n`);
                }
            }
        }
        finally {
            if (ssh) {
                ssh.dispose();
            }
        }
        outputStream.write(`-- 备份合并完成\n`);
        outputStream.end();
        // 获取文件大小
        const stats = fs.statSync(outputPath);
        return {
            success: true,
            message: '完整SQL文件生成成功',
            filePath: outputPath,
            fileName: outputFileName,
            fileSize: stats.size,
            fullBackup: {
                id: fullBackup.id,
                start_time: fullBackup.start_time,
                file_size: fullBackup.file_size
            },
            incrementalBackups: incrementalBackups.map(backup => ({
                id: backup.id,
                start_time: backup.start_time,
                file_size: backup.file_size
            }))
        };
    }
    catch (error) {
        console.error('生成完整SQL文件失败:', error);
        return { success: false, message: '生成完整SQL文件失败' };
    }
}
// 检查是否需要全量备份
async function checkIfNeedsFullBackup(taskId) {
    try {
        const connection = await pool.getConnection();
        // 查找该任务的最近一次全量备份
        const [fullBackups] = await connection.execute(`SELECT id FROM backup_history
       WHERE task_id = ? AND backup_type = 'full' AND status = 'completed'
       ORDER BY start_time DESC
       LIMIT 1`, [taskId]);
        connection.release();
        // 如果没有找到全量备份，则需要进行全量备份
        return fullBackups.length === 0;
    }
    catch (error) {
        console.error('检查全量备份失败:', error);
        // 出错时默认需要全量备份
        return true;
    }
}
// 获取最后一次备份的binlog信息
async function getLastBinlogInfo(taskId) {
    try {
        const connection = await pool.getConnection();
        // 获取最近一次成功备份的binlog信息
        const [backups] = await connection.execute(`SELECT binlog_file, binlog_position FROM backup_history
       WHERE task_id = ? AND status = 'completed'
       AND binlog_file IS NOT NULL AND binlog_position IS NOT NULL
       ORDER BY start_time DESC
       LIMIT 1`, [taskId]);
        connection.release();
        if (backups.length > 0) {
            return {
                binlogFile: backups[0].binlog_file,
                binlogPosition: backups[0].binlog_position
            };
        }
        return null;
    }
    catch (error) {
        console.error('获取最后binlog信息失败:', error);
        return null;
    }
}
// 获取最后一次备份的时间（保留用于兼容性）
async function getLastBackupTime(taskId) {
    try {
        const connection = await pool.getConnection();
        // 获取最近一次成功备份的时间
        const [backups] = await connection.execute(`SELECT start_time FROM backup_history
       WHERE task_id = ? AND status = 'completed'
       ORDER BY start_time DESC
       LIMIT 1`, [taskId]);
        connection.release();
        if (backups.length > 0) {
            return new Date(backups[0].start_time);
        }
        return null;
    }
    catch (error) {
        console.error('获取最后备份时间失败:', error);
        return null;
    }
}
// 执行基于binlog的增量备份
async function performBinlogBackup(task, lastBinlogInfo, outputFilePath, ssh) {
    try {
        // 1. 获取当前binlog位置
        const currentBinlogInfo = await getCurrentBinlogInfo(task, ssh);
        if (!currentBinlogInfo.success) {
            return { success: false, message: `获取当前binlog位置失败: ${currentBinlogInfo.message}` };
        }
        // 2. 检查是否有新的binlog数据
        if (lastBinlogInfo.binlogFile === currentBinlogInfo.binlogFile &&
            lastBinlogInfo.binlogPosition >= currentBinlogInfo.binlogPosition) {
            // 没有新数据，创建空的增量备份文件
            const emptyBackupContent = `-- MySQL增量备份文件\n-- 数据库: ${task.database_name}\n-- 时间范围: ${lastBinlogInfo.binlogFile}:${lastBinlogInfo.binlogPosition} 到 ${currentBinlogInfo.binlogFile}:${currentBinlogInfo.binlogPosition}\n-- 状态: 无新数据变更\n`;
            await ssh.execCommand(`echo "${emptyBackupContent}" > "${outputFilePath}"`);
            return {
                success: true,
                fileSize: emptyBackupContent.length,
                endBinlogFile: currentBinlogInfo.binlogFile,
                endBinlogPosition: currentBinlogInfo.binlogPosition
            };
        }
        // 3. 使用mysqlbinlog提取增量数据
        const mysqlbinlogArgs = [
            `--host=${task.host}`,
            `--port=${task.port}`,
            `--user=${task.username}`,
            `--password=${task.password}`,
            `--database=${task.database_name}`,
            `--start-position=${lastBinlogInfo.binlogPosition}`,
            `--stop-position=${currentBinlogInfo.binlogPosition}`,
            '--read-from-remote-server',
            '--result-file=' + serverFilePath,
            '--force-if-open',
            '--disable-log-bin',
            lastBinlogInfo.binlogFile
        ];
        // 如果binlog文件不同，需要处理多个文件
        if (lastBinlogInfo.binlogFile !== currentBinlogInfo.binlogFile) {
            // 获取binlog文件列表
            const binlogFiles = await getBinlogFilesBetween(task, lastBinlogInfo.binlogFile, currentBinlogInfo.binlogFile, ssh);
            if (!binlogFiles.success) {
                return { success: false, message: `获取binlog文件列表失败: ${binlogFiles.message}` };
            }
            // 处理多个binlog文件
            let combinedContent = `-- MySQL增量备份文件\n-- 数据库: ${task.database_name}\n-- 开始位置: ${lastBinlogInfo.binlogFile}:${lastBinlogInfo.binlogPosition}\n-- 结束位置: ${currentBinlogInfo.binlogFile}:${currentBinlogInfo.binlogPosition}\n\n`;
            for (const binlogFile of binlogFiles.files) {
                const startPos = binlogFile === lastBinlogInfo.binlogFile ? lastBinlogInfo.binlogPosition : 4;
                const endPos = binlogFile === currentBinlogInfo.binlogFile ? currentBinlogInfo.binlogPosition : undefined;
                const binlogArgs = [
                    `--host=${task.host}`,
                    `--port=${task.port}`,
                    `--user=${task.username}`,
                    `--password=${task.password}`,
                    `--database=${task.database_name}`,
                    `--start-position=${startPos}`,
                    ...(endPos ? [`--stop-position=${endPos}`] : []),
                    '--read-from-remote-server',
                    '--force-if-open',
                    '--disable-log-bin',
                    '--skip-gtids', // 跳过GTID信息，避免冲突
                    binlogFile
                ];
                const binlogCommand = `mysqlbinlog ${binlogArgs.join(' ')}`;
                const result = await ssh.execCommand(binlogCommand);
                if (result.code !== 0) {
                    return { success: false, message: `mysqlbinlog执行失败: ${result.stderr}` };
                }
                combinedContent += `-- Binlog文件: ${binlogFile}\n${result.stdout}\n\n`;
            }
            // 写入合并后的内容
            await ssh.execCommand(`cat > "${outputFilePath}" << 'EOF'\n${combinedContent}\nEOF`);
        }
        else {
            // 单个binlog文件处理
            const mysqlbinlogCommand = `mysqlbinlog ${mysqlbinlogArgs.join(' ')} > "${outputFilePath}"`;
            const result = await ssh.execCommand(mysqlbinlogCommand);
            if (result.code !== 0) {
                return { success: false, message: `mysqlbinlog执行失败: ${result.stderr}` };
            }
        }
        // 4. 获取生成文件的大小
        const sizeResult = await ssh.execCommand(`stat -c%s "${outputFilePath}"`);
        const fileSize = parseInt(sizeResult.stdout.trim()) || 0;
        return {
            success: true,
            fileSize: fileSize,
            endBinlogFile: currentBinlogInfo.binlogFile,
            endBinlogPosition: currentBinlogInfo.binlogPosition
        };
    }
    catch (error) {
        console.error('Binlog备份执行失败:', error);
        return { success: false, message: `Binlog备份执行失败: ${error}` };
    }
}
// 获取当前binlog位置信息
async function getCurrentBinlogInfo(task, ssh) {
    try {
        // 连接到MySQL并获取当前binlog位置
        const showMasterStatusCommand = `mysql --host=${task.host} --port=${task.port} --user=${task.username} --password=${task.password} -e "SHOW BINARY LOG STATUS\\G"`;
        const result = await ssh.execCommand(showMasterStatusCommand);
        if (result.code !== 0) {
            return { success: false, message: `获取master状态失败: ${result.stderr}` };
        }
        // 解析输出
        const output = result.stdout;
        const fileMatch = output.match(/File:\s*(\S+)/);
        const positionMatch = output.match(/Position:\s*(\d+)/);
        if (!fileMatch || !positionMatch) {
            return { success: false, message: '无法解析binlog位置信息' };
        }
        return {
            success: true,
            binlogFile: fileMatch[1],
            binlogPosition: parseInt(positionMatch[1])
        };
    }
    catch (error) {
        console.error('获取当前binlog信息失败:', error);
        return { success: false, message: `获取当前binlog信息失败: ${error}` };
    }
}
// 获取两个binlog文件之间的所有文件列表
async function getBinlogFilesBetween(task, startFile, endFile, ssh) {
    try {
        // 获取所有binlog文件
        const showBinaryLogsCommand = `mysql --host=${task.host} --port=${task.port} --user=${task.username} --password=${task.password} -e "SHOW BINARY LOGS"`;
        const result = await ssh.execCommand(showBinaryLogsCommand);
        if (result.code !== 0) {
            return { success: false, message: `获取binlog文件列表失败: ${result.stderr}` };
        }
        // 解析binlog文件列表
        const lines = result.stdout.split('\n');
        const binlogFiles = [];
        let startFound = false;
        for (const line of lines) {
            const match = line.match(/(\S+\.?\d+)/);
            if (match) {
                const fileName = match[1];
                if (fileName === startFile) {
                    startFound = true;
                }
                if (startFound) {
                    binlogFiles.push(fileName);
                }
                if (fileName === endFile) {
                    break;
                }
            }
        }
        if (!startFound) {
            return { success: false, message: `未找到起始binlog文件: ${startFile}` };
        }
        return {
            success: true,
            files: binlogFiles
        };
    }
    catch (error) {
        console.error('获取binlog文件列表失败:', error);
        return { success: false, message: `获取binlog文件列表失败: ${error}` };
    }
}
// 获取任务对应的服务器信息
async function getServerInfoByTaskId(taskId) {
    try {
        const connection = await pool.getConnection();
        const [result] = await connection.execute(`SELECT s.* FROM servers s
       JOIN backup_tasks bt ON s.id = bt.server_id
       WHERE bt.id = ?`, [taskId]);
        connection.release();
        if (result.length === 0) {
            return { success: false, message: '未找到对应的服务器信息' };
        }
        return {
            success: true,
            server: result[0]
        };
    }
    catch (error) {
        console.error('获取服务器信息失败:', error);
        return { success: false, message: `获取服务器信息失败: ${error}` };
    }
}
// 下载备份文件内容
async function downloadBackupFileContent(ssh, filePath) {
    try {
        // 检查文件是否存在
        const checkResult = await ssh.execCommand(`test -f "${filePath}" && echo "exists" || echo "not found"`);
        if (checkResult.stdout.trim() !== 'exists') {
            return { success: false, message: '文件不存在' };
        }
        // 读取文件内容
        const result = await ssh.execCommand(`cat "${filePath}"`);
        if (result.code !== 0) {
            return { success: false, message: `读取文件失败: ${result.stderr}` };
        }
        return {
            success: true,
            content: result.stdout
        };
    }
    catch (error) {
        console.error('下载备份文件内容失败:', error);
        return { success: false, message: `下载备份文件内容失败: ${error}` };
    }
}
// 验证备份链的binlog连续性
export async function validateBackupChainContinuity(taskId, userId) {
    try {
        const connection = await pool.getConnection();
        // 验证权限
        const [taskCheck] = await connection.execute('SELECT id FROM backup_tasks WHERE id = ? AND user_id = ?', [taskId, userId]);
        if (taskCheck.length === 0) {
            connection.release();
            return { success: false, message: '备份任务不存在或无权限访问' };
        }
        // 获取所有成功的备份记录，按时间排序
        const [backups] = await connection.execute(`SELECT id, backup_type, start_time, binlog_file, binlog_position, file_path
       FROM backup_history
       WHERE task_id = ? AND status = 'completed'
       ORDER BY start_time ASC`, [taskId]);
        connection.release();
        if (backups.length === 0) {
            return { success: true, isValid: true, issues: [] };
        }
        const issues = [];
        let isValid = true;
        // 检查是否有全量备份作为起点
        const hasFullBackup = backups.some((backup) => backup.backup_type === 'full');
        if (!hasFullBackup) {
            issues.push('缺少全量备份作为基础');
            isValid = false;
        }
        // 检查binlog连续性
        for (let i = 1; i < backups.length; i++) {
            const prevBackup = backups[i - 1];
            const currentBackup = backups[i];
            // 如果当前备份是增量备份，检查binlog连续性
            if (currentBackup.backup_type === 'incremental') {
                if (!prevBackup.binlog_file || !prevBackup.binlog_position) {
                    issues.push(`备份 ${prevBackup.id} 缺少binlog位置信息`);
                    isValid = false;
                }
                if (!currentBackup.binlog_file || !currentBackup.binlog_position) {
                    issues.push(`备份 ${currentBackup.id} 缺少binlog位置信息`);
                    isValid = false;
                }
                // 检查binlog位置是否连续
                if (prevBackup.binlog_file && currentBackup.binlog_file) {
                    if (prevBackup.binlog_file === currentBackup.binlog_file) {
                        // 同一个binlog文件，位置应该递增
                        if (prevBackup.binlog_position >= currentBackup.binlog_position) {
                            issues.push(`备份 ${currentBackup.id} 的binlog位置不连续：${prevBackup.binlog_file}:${prevBackup.binlog_position} -> ${currentBackup.binlog_file}:${currentBackup.binlog_position}`);
                            isValid = false;
                        }
                    }
                    else {
                        // 不同binlog文件，检查文件序号连续性
                        const prevFileNum = extractBinlogFileNumber(prevBackup.binlog_file);
                        const currentFileNum = extractBinlogFileNumber(currentBackup.binlog_file);
                        if (currentFileNum <= prevFileNum) {
                            issues.push(`备份 ${currentBackup.id} 的binlog文件序号不连续：${prevBackup.binlog_file} -> ${currentBackup.binlog_file}`);
                            isValid = false;
                        }
                    }
                }
                // 检查时间连续性
                const prevTime = new Date(prevBackup.start_time).getTime();
                const currentTime = new Date(currentBackup.start_time).getTime();
                if (currentTime <= prevTime) {
                    issues.push(`备份 ${currentBackup.id} 的时间戳不连续`);
                    isValid = false;
                }
                // 检查文件大小合理性
                if (currentBackup.file_size && currentBackup.file_size < 100) {
                    issues.push(`备份 ${currentBackup.id} 的文件大小异常小 (${currentBackup.file_size} bytes)`);
                    isValid = false;
                }
            }
        }
        return {
            success: true,
            isValid: isValid,
            issues: issues
        };
    }
    catch (error) {
        console.error('验证备份链连续性失败:', error);
        return { success: false, message: `验证备份链连续性失败: ${error}` };
    }
}
// 验证备份文件完整性
export async function validateBackupFileIntegrity(historyId, userId) {
    try {
        const connection = await pool.getConnection();
        // 获取备份记录和任务信息
        const [backupInfo] = await connection.execute(`SELECT bh.*, bt.user_id, bt.server_id
       FROM backup_history bh
       JOIN backup_tasks bt ON bh.task_id = bt.id
       WHERE bh.id = ? AND bt.user_id = ?`, [historyId, userId]);
        if (backupInfo.length === 0) {
            connection.release();
            return { success: false, message: '备份记录不存在或无权限访问' };
        }
        const backup = backupInfo[0];
        // 获取服务器信息
        const [serverInfo] = await connection.execute('SELECT * FROM servers WHERE id = ?', [backup.server_id]);
        connection.release();
        if (serverInfo.length === 0) {
            return { success: false, message: '服务器信息不存在' };
        }
        const server = serverInfo[0];
        // 建立SSH连接验证文件
        const { NodeSSH } = await import('node-ssh');
        const ssh = new NodeSSH();
        try {
            await ssh.connect({
                host: server.ssh_host || server.host,
                port: server.ssh_port || 22,
                username: server.ssh_username,
                password: server.ssh_password
            });
            // 检查文件是否存在
            const checkResult = await ssh.execCommand(`test -f "${backup.file_path}" && echo "exists" || echo "not found"`);
            if (checkResult.stdout.trim() !== 'exists') {
                return {
                    success: true,
                    isValid: false,
                    message: '备份文件不存在'
                };
            }
            // 获取文件大小
            const sizeResult = await ssh.execCommand(`stat -c%s "${backup.file_path}"`);
            const actualFileSize = parseInt(sizeResult.stdout.trim()) || 0;
            // 比较文件大小
            const sizeMatches = actualFileSize === backup.file_size;
            // 简单的文件头验证（检查是否是有效的SQL文件）
            const headResult = await ssh.execCommand(`head -n 5 "${backup.file_path}"`);
            const isValidSQL = headResult.stdout.includes('MySQL') ||
                headResult.stdout.includes('mysqldump') ||
                headResult.stdout.includes('--') ||
                headResult.stdout.includes('CREATE') ||
                headResult.stdout.includes('INSERT');
            const isValid = sizeMatches && isValidSQL;
            return {
                success: true,
                isValid: isValid,
                fileSize: actualFileSize,
                message: isValid ? '文件完整性验证通过' :
                    !sizeMatches ? `文件大小不匹配，期望: ${backup.file_size}, 实际: ${actualFileSize}` :
                        '文件格式验证失败'
            };
        }
        finally {
            if (ssh) {
                ssh.dispose();
            }
        }
    }
    catch (error) {
        console.error('验证备份文件完整性失败:', error);
        return { success: false, message: `验证备份文件完整性失败: ${error}` };
    }
}
// 批量验证任务的所有备份文件
export async function validateAllBackupFiles(taskId, userId) {
    try {
        const connection = await pool.getConnection();
        // 验证权限
        const [taskCheck] = await connection.execute('SELECT id FROM backup_tasks WHERE id = ? AND user_id = ?', [taskId, userId]);
        if (taskCheck.length === 0) {
            connection.release();
            return { success: false, message: '备份任务不存在或无权限访问' };
        }
        // 获取所有成功的备份记录
        const [backups] = await connection.execute(`SELECT id, backup_type, start_time, file_path, file_size
       FROM backup_history
       WHERE task_id = ? AND status = 'completed'
       ORDER BY start_time DESC`, [taskId]);
        connection.release();
        const results = [];
        // 验证每个备份文件
        for (const backup of backups) {
            const validationResult = await validateBackupFileIntegrity(backup.id, userId);
            results.push({
                historyId: backup.id,
                backupType: backup.backup_type,
                startTime: backup.start_time,
                filePath: backup.file_path,
                expectedSize: backup.file_size,
                validation: validationResult
            });
        }
        return {
            success: true,
            results: results
        };
    }
    catch (error) {
        console.error('批量验证备份文件失败:', error);
        return { success: false, message: `批量验证备份文件失败: ${error}` };
    }
}
// 备份恢复功能
export async function restoreBackupToPointInTime(taskId, targetHistoryId, userId, options = {}) {
    try {
        const connection = await pool.getConnection();
        // 验证权限
        const [taskCheck] = await connection.execute('SELECT id, database_name, backup_path FROM backup_tasks WHERE id = ? AND user_id = ?', [taskId, userId]);
        if (taskCheck.length === 0) {
            connection.release();
            return { success: false, message: '备份任务不存在或无权限访问' };
        }
        const task = taskCheck[0];
        // 获取目标备份记录
        const [targetBackup] = await connection.execute('SELECT * FROM backup_history WHERE id = ? AND task_id = ? AND status = "completed"', [targetHistoryId, taskId]);
        if (targetBackup.length === 0) {
            connection.release();
            return { success: false, message: '目标备份记录不存在或未完成' };
        }
        const target = targetBackup[0];
        // 获取需要恢复的备份链（从最近的全量备份到目标备份）
        const [allBackups] = await connection.execute(`SELECT * FROM backup_history
       WHERE task_id = ? AND status = 'completed' AND start_time <= ?
       ORDER BY start_time ASC`, [taskId, target.start_time]);
        connection.release();
        if (allBackups.length === 0) {
            return { success: false, message: '没有找到可用的备份记录' };
        }
        // 找到最近的全量备份
        let fullBackup = null;
        let incrementalBackups = [];
        for (let i = allBackups.length - 1; i >= 0; i--) {
            if (allBackups[i].backup_type === 'full') {
                fullBackup = allBackups[i];
                incrementalBackups = allBackups.slice(i + 1);
                break;
            }
        }
        if (!fullBackup) {
            return { success: false, message: '没有找到全量备份，无法进行恢复' };
        }
        // 获取服务器连接信息
        const serverInfo = await getServerInfoByTaskId(taskId);
        if (!serverInfo.success) {
            return { success: false, message: `获取服务器信息失败: ${serverInfo.message}` };
        }
        const connectionConfig = {
            host: serverInfo.server.host,
            port: serverInfo.server.port,
            user: serverInfo.server.username,
            password: serverInfo.server.password,
            database: task.database_name
        };
        // 如果是干运行，只验证文件存在性
        if (options.dryRun) {
            console.log('干运行模式：验证恢复可行性');
            return {
                success: true,
                message: `恢复验证成功。将恢复全量备份和${incrementalBackups.length}个增量备份`,
                restoredToTime: target.start_time
            };
        }
        // 建立SSH连接
        const { NodeSSH } = await import('node-ssh');
        const ssh = new NodeSSH();
        try {
            await ssh.connect({
                host: serverInfo.server.ssh_host || serverInfo.server.host,
                port: serverInfo.server.ssh_port || 22,
                username: serverInfo.server.ssh_username,
                password: serverInfo.server.ssh_password
            });
            // 1. 恢复全量备份
            console.log('开始恢复全量备份:', fullBackup.file_path);
            const fullRestoreResult = await restoreFullBackupFile(ssh, fullBackup.file_path, connectionConfig, options.targetDatabase || task.database_name);
            if (!fullRestoreResult.success) {
                return { success: false, message: `全量备份恢复失败: ${fullRestoreResult.message}` };
            }
            // 2. 按顺序应用增量备份
            for (const incrementalBackup of incrementalBackups) {
                console.log('应用增量备份:', incrementalBackup.file_path);
                const incrementalRestoreResult = await applyIncrementalBackupFile(ssh, incrementalBackup.file_path, connectionConfig, options.targetDatabase || task.database_name);
                if (!incrementalRestoreResult.success) {
                    return {
                        success: false,
                        message: `增量备份应用失败: ${incrementalRestoreResult.message}`
                    };
                }
            }
            return {
                success: true,
                message: `恢复成功！已恢复全量备份和${incrementalBackups.length}个增量备份`,
                restoredToTime: target.start_time
            };
        }
        finally {
            if (ssh) {
                ssh.dispose();
            }
        }
    }
    catch (error) {
        console.error('恢复备份失败:', error);
        return { success: false, message: `恢复失败: ${error}` };
    }
}
// 恢复全量备份文件
async function restoreFullBackupFile(ssh, backupFilePath, connectionConfig, targetDatabase) {
    try {
        // 下载备份文件内容
        const backupContent = await downloadBackupFileContent(ssh, backupFilePath);
        if (!backupContent.success) {
            return { success: false, message: `下载备份文件失败: ${backupContent.message}` };
        }
        // 创建目标数据库（如果不存在）
        const createDbCommand = `mysql --host=${connectionConfig.host} --port=${connectionConfig.port} --user=${connectionConfig.user} --password=${connectionConfig.password} -e "CREATE DATABASE IF NOT EXISTS \\\`${targetDatabase}\\\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"`;
        const createDbResult = await ssh.execCommand(createDbCommand);
        if (createDbResult.code !== 0) {
            return { success: false, message: `创建数据库失败: ${createDbResult.stderr}` };
        }
        // 将备份内容写入临时文件
        const tempFilePath = `/tmp/restore_${Date.now()}.sql`;
        await ssh.putFile(Buffer.from(backupContent.content || ''), tempFilePath);
        // 执行恢复
        const restoreCommand = `mysql --host=${connectionConfig.host} --port=${connectionConfig.port} --user=${connectionConfig.user} --password=${connectionConfig.password} --default-character-set=utf8mb4 ${targetDatabase} < ${tempFilePath}`;
        const restoreResult = await ssh.execCommand(restoreCommand);
        // 清理临时文件
        await ssh.execCommand(`rm -f ${tempFilePath}`);
        if (restoreResult.code !== 0) {
            return { success: false, message: `恢复执行失败: ${restoreResult.stderr}` };
        }
        return { success: true, message: '全量备份恢复成功' };
    }
    catch (error) {
        console.error('恢复全量备份文件失败:', error);
        return { success: false, message: `恢复全量备份文件失败: ${error}` };
    }
}
// 应用增量备份文件
async function applyIncrementalBackupFile(ssh, incrementalFilePath, connectionConfig, targetDatabase) {
    try {
        // 下载增量备份文件内容
        const incrementalContent = await downloadBackupFileContent(ssh, incrementalFilePath);
        if (!incrementalContent.success) {
            return { success: false, message: `下载增量备份文件失败: ${incrementalContent.message}` };
        }
        // 将增量备份内容写入临时文件
        const tempFilePath = `/tmp/incremental_${Date.now()}.sql`;
        await ssh.putFile(Buffer.from(incrementalContent.content || ''), tempFilePath);
        // 应用增量备份
        const applyCommand = `mysql --host=${connectionConfig.host} --port=${connectionConfig.port} --user=${connectionConfig.user} --password=${connectionConfig.password} --default-character-set=utf8mb4 ${targetDatabase} < ${tempFilePath}`;
        const applyResult = await ssh.execCommand(applyCommand);
        // 清理临时文件
        await ssh.execCommand(`rm -f ${tempFilePath}`);
        if (applyResult.code !== 0) {
            return { success: false, message: `增量备份应用失败: ${applyResult.stderr}` };
        }
        return { success: true, message: '增量备份应用成功' };
    }
    catch (error) {
        console.error('应用增量备份文件失败:', error);
        return { success: false, message: `应用增量备份文件失败: ${error}` };
    }
}
// 辅助函数：从binlog文件名提取序号
function extractBinlogFileNumber(filename) {
    const match = filename.match(/\.(\d+)$/);
    return match ? parseInt(match[1], 10) : 0;
}
// 错误恢复机制：修复损坏的备份链
export async function repairBackupChain(taskId, userId, options = {}) {
    try {
        const connection = await pool.getConnection();
        // 验证权限
        const [taskCheck] = await connection.execute('SELECT id FROM backup_tasks WHERE id = ? AND user_id = ?', [taskId, userId]);
        if (taskCheck.length === 0) {
            connection.release();
            return { success: false, message: '备份任务不存在或无权限访问' };
        }
        // 获取所有备份记录
        const [allBackups] = await connection.execute(`SELECT * FROM backup_history
       WHERE task_id = ?
       ORDER BY start_time ASC`, [taskId]);
        connection.release();
        if (allBackups.length === 0) {
            return { success: false, message: '没有找到备份记录' };
        }
        const repairActions = [];
        let needsRepair = false;
        // 检查备份链完整性
        let lastFullBackup = null;
        for (let i = 0; i < allBackups.length; i++) {
            const backup = allBackups[i];
            if (backup.backup_type === 'full') {
                lastFullBackup = backup;
                continue;
            }
            if (backup.backup_type === 'incremental') {
                if (!lastFullBackup) {
                    repairActions.push(`增量备份 ${backup.id} 缺少基础的完整备份`);
                    needsRepair = true;
                    if (options.autoFix) {
                        // 标记为需要重新创建
                        await markBackupForRecreation(backup.id);
                        repairActions.push(`已标记备份 ${backup.id} 为需要重新创建`);
                    }
                }
                // 检查与前一个备份的连续性
                if (i > 0) {
                    const prevBackup = allBackups[i - 1];
                    if (!isBinlogContinuous(prevBackup, backup)) {
                        repairActions.push(`备份 ${backup.id} 与前一个备份的binlog位置不连续`);
                        needsRepair = true;
                    }
                }
            }
            // 检查备份状态
            if (backup.status === 'failed') {
                repairActions.push(`备份 ${backup.id} 状态为失败，需要重新执行`);
                needsRepair = true;
                if (options.autoFix) {
                    await retryFailedBackup(backup.id);
                    repairActions.push(`已重新调度失败的备份 ${backup.id}`);
                }
            }
        }
        if (!needsRepair) {
            return {
                success: true,
                message: '备份链完整，无需修复',
                repairActions: ['备份链验证通过']
            };
        }
        return {
            success: true,
            message: `发现 ${repairActions.length} 个问题${options.autoFix ? '，已自动修复' : '，需要手动处理'}`,
            repairActions
        };
    }
    catch (error) {
        console.error('修复备份链失败:', error);
        return { success: false, message: `修复失败: ${error}` };
    }
}
// 辅助函数：检查binlog连续性
function isBinlogContinuous(prevBackup, currentBackup) {
    if (!prevBackup.binlog_file || !currentBackup.binlog_file) {
        return false;
    }
    if (prevBackup.binlog_file === currentBackup.binlog_file) {
        return currentBackup.binlog_position > prevBackup.binlog_position;
    }
    const prevFileNum = extractBinlogFileNumber(prevBackup.binlog_file);
    const currentFileNum = extractBinlogFileNumber(currentBackup.binlog_file);
    return currentFileNum > prevFileNum;
}
// 辅助函数：标记备份需要重新创建
async function markBackupForRecreation(backupId) {
    const connection = await pool.getConnection();
    try {
        await connection.execute('UPDATE backup_history SET status = "needs_recreation", error_message = "Marked for recreation due to chain repair" WHERE id = ?', [backupId]);
    }
    finally {
        connection.release();
    }
}
// 辅助函数：重试失败的备份
async function retryFailedBackup(backupId) {
    const connection = await pool.getConnection();
    try {
        await connection.execute('UPDATE backup_history SET status = "pending", error_message = NULL, retry_count = COALESCE(retry_count, 0) + 1 WHERE id = ?', [backupId]);
    }
    finally {
        connection.release();
    }
}
