// 简单的数据库连接测试脚本
import mysql from 'mysql2/promise';

const DB_CONFIG = {
  host: '***************',
  user: 'user-hiram',
  password: 'user-hiram',
  database: 'user',
  port: 3306
};

async function testConnection() {
  try {
    console.log('正在连接数据库...');
    const connection = await mysql.createConnection(DB_CONFIG);
    
    console.log('数据库连接成功！');
    
    // 检查用户表是否存在
    const [tables] = await connection.execute("SHOW TABLES LIKE 'users'");
    
    if (tables.length > 0) {
      console.log('用户表已存在');
      
      // 查询用户数量
      const [rows] = await connection.execute('SELECT COUNT(*) as count FROM users');
      console.log(`当前用户数量: ${rows[0].count}`);
    } else {
      console.log('用户表不存在');
    }
    
    await connection.end();
    console.log('数据库连接已关闭');
    
  } catch (error) {
    console.error('数据库连接失败:', error.message);
  }
}

testConnection();
