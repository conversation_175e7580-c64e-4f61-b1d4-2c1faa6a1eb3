/* 企业级表单样式 */

.enterprise-form {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: var(--spacing-3xl);
  box-shadow: var(--shadow-card);
  margin-bottom: var(--spacing-2xl);
  position: relative;
  overflow: hidden;
}

.enterprise-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover), var(--info-color));
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-3xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.form-title {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.form-title::before {
  content: '';
  width: 8px;
  height: 8px;
  background: var(--primary-color);
  border-radius: var(--radius-full);
  box-shadow: 0 0 8px rgba(0, 120, 212, 0.4);
}

.form-subtitle {
  margin: var(--spacing-sm) 0 0 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-normal);
}

.form-body {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-xl);
  align-items: start;
}

.form-row.single-column {
  grid-template-columns: 1fr;
}

.form-row.two-columns {
  grid-template-columns: 1fr 1fr;
}

.form-row.three-columns {
  grid-template-columns: repeat(3, 1fr);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  position: relative;
}

.form-group.required .form-label::after {
  content: '*';
  color: var(--danger-color);
  margin-left: var(--spacing-xs);
}

.form-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-xs);
}

.form-input {
  padding: var(--spacing-md) var(--spacing-lg);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  background: var(--bg-secondary);
  color: var(--text-primary);
  transition: all var(--transition-normal);
  min-height: 44px;
  font-family: inherit;
}

.form-input:focus {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.1);
  outline: none;
}

.form-input:hover:not(:focus):not(:disabled) {
  border-color: var(--border-hover);
}

.form-input.error {
  border-color: var(--border-error);
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.form-input.success {
  border-color: var(--border-success);
  box-shadow: 0 0 0 3px rgba(16, 124, 16, 0.1);
}

.form-input:disabled {
  background: var(--bg-disabled);
  color: var(--text-disabled);
  cursor: not-allowed;
  opacity: 0.7;
}

.form-textarea {
  min-height: 120px;
  resize: vertical;
  font-family: inherit;
  line-height: var(--line-height-relaxed);
}

.form-select {
  appearance: none;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="6,9 12,15 18,9"></polyline></svg>');
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
  padding-right: var(--spacing-4xl);
}

.form-help {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  margin-top: var(--spacing-xs);
  line-height: var(--line-height-normal);
}

.form-error {
  font-size: var(--font-size-xs);
  color: var(--danger-color);
  margin-top: var(--spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.form-error::before {
  content: '⚠️';
  font-size: var(--font-size-sm);
}

.form-success {
  font-size: var(--font-size-xs);
  color: var(--success-color);
  margin-top: var(--spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.form-success::before {
  content: '✅';
  font-size: var(--font-size-sm);
}

.form-section {
  border-top: 1px solid var(--border-color);
  padding-top: var(--spacing-2xl);
  margin-top: var(--spacing-xl);
}

.form-section-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.form-section-title::before {
  content: '';
  width: 6px;
  height: 6px;
  background: var(--secondary-color);
  border-radius: var(--radius-full);
}

.form-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
  padding-top: var(--spacing-2xl);
  border-top: 1px solid var(--border-color);
  margin-top: var(--spacing-2xl);
}

.form-actions.center {
  justify-content: center;
}

.form-actions.start {
  justify-content: flex-start;
}

.form-actions.space-between {
  justify-content: space-between;
}

/* 输入组合样式 */
.input-group {
  display: flex;
  align-items: stretch;
  gap: 0;
}

.input-group .form-input {
  border-radius: 0;
  border-right: none;
}

.input-group .form-input:first-child {
  border-radius: var(--radius-lg) 0 0 var(--radius-lg);
}

.input-group .form-input:last-child {
  border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
  border-right: 2px solid var(--border-color);
}

.input-group .btn {
  border-radius: 0;
  border-left: none;
}

.input-group .btn:last-child {
  border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
}

/* 复选框和单选框样式 */
.form-checkbox,
.form-radio {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: background-color var(--transition-fast);
}

.form-checkbox:hover,
.form-radio:hover {
  background: var(--bg-hover);
}

.form-checkbox input,
.form-radio input {
  margin: 0;
  width: 18px;
  height: 18px;
  accent-color: var(--primary-color);
}

.form-checkbox label,
.form-radio label {
  margin: 0;
  cursor: pointer;
  font-size: var(--font-size-base);
  color: var(--text-primary);
  text-transform: none;
  letter-spacing: normal;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .enterprise-form {
    padding: var(--spacing-2xl);
  }
}
