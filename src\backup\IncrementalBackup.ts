/**
 * 真正的MySQL增量备份实现
 * 基于MySQL binlog的增量备份和恢复系统
 */

import { spawn, ChildProcess } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import { logger } from '../utils/Logger';
import { config } from '../config/AppConfig';

export interface BinlogPosition {
  file: string;
  position: number;
  gtidSet?: string;
  executed_gtid_set?: string;
}

export interface BackupChain {
  id: string;
  baseBackupId: string;
  incrementalBackups: IncrementalBackupInfo[];
  createdAt: Date;
  lastBackupAt: Date;
}

export interface IncrementalBackupInfo {
  id: string;
  chainId: string;
  startPosition: BinlogPosition;
  endPosition: BinlogPosition;
  filePath: string;
  fileSize: number;
  createdAt: Date;
  isValid: boolean;
}

export interface BackupMetadata {
  version: string;
  serverInfo: {
    version: string;
    serverId: number;
    hostname: string;
  };
  backupInfo: {
    type: 'full' | 'incremental';
    startTime: Date;
    endTime: Date;
    binlogPosition: BinlogPosition;
  };
  checksums: {
    [tableName: string]: string;
  };
}

/**
 * 增量备份管理器
 */
export class IncrementalBackupManager {
  private static instance: IncrementalBackupManager;
  private backupChains: Map<string, BackupChain> = new Map();
  private activeBackups: Set<string> = new Set();

  private constructor() {}

  public static getInstance(): IncrementalBackupManager {
    if (!IncrementalBackupManager.instance) {
      IncrementalBackupManager.instance = new IncrementalBackupManager();
    }
    return IncrementalBackupManager.instance;
  }

  /**
   * 获取当前binlog位置
   */
  public async getCurrentBinlogPosition(connectionConfig: any): Promise<BinlogPosition> {
    return new Promise((resolve, reject) => {
      const mysql = require('mysql2/promise');
      
      mysql.createConnection(connectionConfig)
        .then(async (connection: any) => {
          try {
            // 获取当前binlog位置
            const [rows] = await connection.execute('SHOW BINARY LOG STATUS');
            if (!rows || rows.length === 0) {
              throw new Error('无法获取binlog状态，请确保MySQL启用了binlog');
            }

            const masterStatus = rows[0];
            const position: BinlogPosition = {
              file: masterStatus.File,
              position: masterStatus.Position
            };

            // 如果支持GTID，也获取GTID信息
            try {
              const [gtidRows] = await connection.execute('SELECT @@GLOBAL.gtid_executed as gtid_set, @@GLOBAL.gtid_purged as gtid_purged');
              if (gtidRows && gtidRows.length > 0) {
                position.gtidSet = gtidRows[0].gtid_set;
                position.executed_gtid_set = gtidRows[0].gtid_set;

                // 检查GTID模式
                const [gtidModeRows] = await connection.execute('SELECT @@GLOBAL.gtid_mode as gtid_mode');
                if (gtidModeRows && gtidModeRows.length > 0) {
                  const gtidMode = gtidModeRows[0].gtid_mode;
                  logger.debug('GTID模式', { gtidMode, gtidSet: position.gtidSet });
                }
              }
            } catch (gtidError) {
              logger.debug('GTID not available or not enabled', { error: gtidError });
            }

            await connection.end();
            resolve(position);
          } catch (error) {
            await connection.end();
            reject(error);
          }
        })
        .catch(reject);
    });
  }

  /**
   * 创建完整备份（作为增量备份链的基础）
   */
  public async createFullBackup(
    connectionConfig: any,
    backupPath: string,
    options: {
      compression?: boolean;
      encryption?: boolean;
      verification?: boolean;
    } = {}
  ): Promise<{
    success: boolean;
    backupId: string;
    filePath?: string;
    metadata?: BackupMetadata;
    error?: string;
  }> {
    const backupId = this.generateBackupId();
    const startTime = new Date();

    try {
      logger.info('开始创建完整备份', { backupId, connectionConfig: { ...connectionConfig, password: '***' } });

      // 获取备份开始时的binlog位置
      const startPosition = await this.getCurrentBinlogPosition(connectionConfig);
      
      // 创建备份目录
      const backupDir = path.join(backupPath, backupId);
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
      }

      // 执行mysqldump
      const dumpResult = await this.performMysqlDump(connectionConfig, backupDir, {
        includeRoutines: true,
        includeTriggers: true,
        includeEvents: true,
        singleTransaction: true,
        ...options
      });

      if (!dumpResult.success) {
        throw new Error(dumpResult.error || '备份失败');
      }

      // 获取备份结束时的binlog位置
      const endPosition = await this.getCurrentBinlogPosition(connectionConfig);

      // 创建备份元数据
      const metadata: BackupMetadata = {
        version: '1.0',
        serverInfo: await this.getServerInfo(connectionConfig),
        backupInfo: {
          type: 'full',
          startTime,
          endTime: new Date(),
          binlogPosition: endPosition
        },
        checksums: await this.calculateTableChecksums(connectionConfig)
      };

      // 保存元数据
      const metadataPath = path.join(backupDir, 'metadata.json');
      fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));

      // 创建备份链
      const chainId = this.generateChainId();
      const backupChain: BackupChain = {
        id: chainId,
        baseBackupId: backupId,
        incrementalBackups: [],
        createdAt: startTime,
        lastBackupAt: new Date()
      };

      this.backupChains.set(chainId, backupChain);

      logger.info('完整备份创建成功', { 
        backupId, 
        chainId, 
        filePath: dumpResult.filePath,
        binlogPosition: endPosition 
      });

      return {
        success: true,
        backupId,
        filePath: dumpResult.filePath,
        metadata
      };

    } catch (error) {
      logger.error('完整备份失败', error as Error, { backupId });
      return {
        success: false,
        backupId,
        error: (error as Error).message
      };
    }
  }

  /**
   * 创建增量备份
   */
  public async createIncrementalBackup(
    chainId: string,
    connectionConfig: any,
    backupPath: string
  ): Promise<{
    success: boolean;
    backupId: string;
    filePath?: string;
    startPosition?: BinlogPosition;
    endPosition?: BinlogPosition;
    error?: string;
  }> {
    const backupId = this.generateBackupId();

    try {
      const chain = this.backupChains.get(chainId);
      if (!chain) {
        throw new Error('备份链不存在');
      }

      logger.info('开始创建增量备份', { backupId, chainId });

      // 获取上次备份的结束位置作为起始位置
      let startPosition: BinlogPosition;
      if (chain.incrementalBackups.length > 0) {
        const lastBackup = chain.incrementalBackups[chain.incrementalBackups.length - 1];
        startPosition = lastBackup.endPosition;
      } else {
        // 如果是第一个增量备份，从完整备份的位置开始
        const baseBackupMetadata = await this.loadBackupMetadata(
          path.join(backupPath, chain.baseBackupId, 'metadata.json')
        );
        startPosition = baseBackupMetadata.backupInfo.binlogPosition;
      }

      // 获取当前binlog位置作为结束位置
      const endPosition = await this.getCurrentBinlogPosition(connectionConfig);

      // 检查是否有新的binlog数据
      if (this.isSamePosition(startPosition, endPosition)) {
        logger.info('没有新的binlog数据，跳过增量备份', { chainId, position: startPosition });
        return {
          success: true,
          backupId,
          startPosition,
          endPosition
        };
      }

      // 创建增量备份目录
      const incrementalDir = path.join(backupPath, chainId, 'incremental', backupId);
      if (!fs.existsSync(incrementalDir)) {
        fs.mkdirSync(incrementalDir, { recursive: true });
      }

      // 使用mysqlbinlog提取binlog
      const binlogResult = await this.extractBinlog(
        connectionConfig,
        startPosition,
        endPosition,
        incrementalDir
      );

      if (!binlogResult.success) {
        throw new Error(binlogResult.error || '增量备份失败');
      }

      // 创建增量备份信息
      const incrementalInfo: IncrementalBackupInfo = {
        id: backupId,
        chainId,
        startPosition,
        endPosition,
        filePath: binlogResult.filePath!,
        fileSize: fs.statSync(binlogResult.filePath!).size,
        createdAt: new Date(),
        isValid: true
      };

      // 更新备份链
      chain.incrementalBackups.push(incrementalInfo);
      chain.lastBackupAt = new Date();

      logger.info('增量备份创建成功', { 
        backupId, 
        chainId,
        startPosition,
        endPosition,
        filePath: binlogResult.filePath 
      });

      return {
        success: true,
        backupId,
        filePath: binlogResult.filePath,
        startPosition,
        endPosition
      };

    } catch (error) {
      logger.error('增量备份失败', error as Error, { backupId, chainId });
      return {
        success: false,
        backupId,
        error: (error as Error).message
      };
    }
  }

  /**
   * 执行mysqldump
   */
  private async performMysqlDump(
    connectionConfig: any,
    outputDir: string,
    options: any
  ): Promise<{ success: boolean; filePath?: string; error?: string }> {
    return new Promise((resolve) => {
      const outputFile = path.join(outputDir, 'full_backup.sql');
      
      const args = [
        `--host=${connectionConfig.host}`,
        `--port=${connectionConfig.port}`,
        `--user=${connectionConfig.user}`,
        `--password=${connectionConfig.password}`,
        '--single-transaction',
        '--lock-tables=false',
        '--no-tablespaces',
        '--routines',
        '--triggers',
        '--events',
        '--complete-insert',
        '--extended-insert',
        '--add-drop-table',
        '--default-character-set=utf8mb4',
        connectionConfig.database
      ];

      const mysqldump = spawn('mysqldump', args);
      const writeStream = fs.createWriteStream(outputFile);

      mysqldump.stdout.pipe(writeStream);

      let errorOutput = '';
      mysqldump.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      mysqldump.on('close', (code) => {
        writeStream.end();
        
        if (code === 0 || (fs.existsSync(outputFile) && fs.statSync(outputFile).size > 100)) {
          resolve({ success: true, filePath: outputFile });
        } else {
          resolve({ success: false, error: errorOutput || `Process exited with code ${code}` });
        }
      });

      mysqldump.on('error', (error) => {
        resolve({ success: false, error: error.message });
      });
    });
  }

  /**
   * 提取binlog
   */
  private async extractBinlog(
    connectionConfig: any,
    startPosition: BinlogPosition,
    endPosition: BinlogPosition,
    outputDir: string
  ): Promise<{ success: boolean; filePath?: string; error?: string }> {
    return new Promise((resolve) => {
      const outputFile = path.join(outputDir, 'incremental.sql');
      
      const args = [
        `--host=${connectionConfig.host}`,
        `--port=${connectionConfig.port}`,
        `--user=${connectionConfig.user}`,
        `--password=${connectionConfig.password}`,
        '--read-from-remote-server',
        `--database=${connectionConfig.database}`,
        `--start-position=${startPosition.position}`,
        `--stop-position=${endPosition.position}`,
        startPosition.file
      ];

      const mysqlbinlog = spawn('mysqlbinlog', args);
      const writeStream = fs.createWriteStream(outputFile);

      mysqlbinlog.stdout.pipe(writeStream);

      let errorOutput = '';
      mysqlbinlog.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      mysqlbinlog.on('close', (code) => {
        writeStream.end();
        
        if (code === 0) {
          resolve({ success: true, filePath: outputFile });
        } else {
          resolve({ success: false, error: errorOutput || `Process exited with code ${code}` });
        }
      });

      mysqlbinlog.on('error', (error) => {
        resolve({ success: false, error: error.message });
      });
    });
  }

  private async getServerInfo(connectionConfig: any): Promise<any> {
    const mysql = require('mysql2/promise');
    const connection = await mysql.createConnection(connectionConfig);
    
    try {
      const [versionRows] = await connection.execute('SELECT VERSION() as version');
      const [serverIdRows] = await connection.execute('SELECT @@server_id as server_id');
      const [hostnameRows] = await connection.execute('SELECT @@hostname as hostname');
      
      return {
        version: versionRows[0].version,
        serverId: serverIdRows[0].server_id,
        hostname: hostnameRows[0].hostname
      };
    } finally {
      await connection.end();
    }
  }

  private async calculateTableChecksums(connectionConfig: any): Promise<{ [tableName: string]: string }> {
    const mysql = require('mysql2/promise');
    const connection = await mysql.createConnection(connectionConfig);
    
    try {
      const [tables] = await connection.execute(
        'SELECT TABLE_NAME FROM information_schema.TABLES WHERE TABLE_SCHEMA = ?',
        [connectionConfig.database]
      );
      
      const checksums: { [tableName: string]: string } = {};
      
      for (const table of tables as any[]) {
        try {
          const [checksumRows] = await connection.execute(
            `CHECKSUM TABLE \`${table.TABLE_NAME}\``
          );
          checksums[table.TABLE_NAME] = (checksumRows as any[])[0].Checksum?.toString() || '0';
        } catch (error) {
          logger.warn(`无法计算表 ${table.TABLE_NAME} 的校验和`, { error });
          checksums[table.TABLE_NAME] = '0';
        }
      }
      
      return checksums;
    } finally {
      await connection.end();
    }
  }

  private async loadBackupMetadata(metadataPath: string): Promise<BackupMetadata> {
    const content = fs.readFileSync(metadataPath, 'utf8');
    return JSON.parse(content);
  }

  private isSamePosition(pos1: BinlogPosition, pos2: BinlogPosition): boolean {
    // 如果支持GTID，优先使用GTID比较
    if (pos1.gtidSet && pos2.gtidSet) {
      return pos1.gtidSet === pos2.gtidSet;
    }

    // 否则使用传统的文件名和位置比较
    return pos1.file === pos2.file && pos1.position === pos2.position;
  }

  /**
   * 检查binlog位置是否连续
   */
  private isContinuousPosition(prevPos: BinlogPosition, currentPos: BinlogPosition): boolean {
    // 如果支持GTID，检查GTID连续性
    if (prevPos.gtidSet && currentPos.gtidSet) {
      // 简化的GTID连续性检查
      return this.isGtidContinuous(prevPos.gtidSet, currentPos.gtidSet);
    }

    // 传统的位置连续性检查
    if (prevPos.file === currentPos.file) {
      return currentPos.position > prevPos.position;
    }

    // 不同文件的连续性检查需要更复杂的逻辑
    return this.isBinlogFileContinuous(prevPos.file, currentPos.file);
  }

  /**
   * 检查GTID连续性（简化版本）
   */
  private isGtidContinuous(prevGtid: string, currentGtid: string): boolean {
    // 这里实现简化的GTID连续性检查
    // 实际应用中需要解析GTID格式并比较事务ID
    return currentGtid.length >= prevGtid.length;
  }

  /**
   * 检查binlog文件连续性
   */
  private isBinlogFileContinuous(prevFile: string, currentFile: string): boolean {
    // 提取文件序号进行比较
    const prevNum = this.extractBinlogNumber(prevFile);
    const currentNum = this.extractBinlogNumber(currentFile);

    return currentNum > prevNum;
  }

  /**
   * 从binlog文件名提取序号
   */
  private extractBinlogNumber(filename: string): number {
    const match = filename.match(/\.(\d+)$/);
    return match ? parseInt(match[1], 10) : 0;
  }

  private generateBackupId(): string {
    return 'backup_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private generateChainId(): string {
    return 'chain_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 获取备份链列表
   */
  public getBackupChains(): BackupChain[] {
    return Array.from(this.backupChains.values());
  }

  /**
   * 获取特定备份链
   */
  public getBackupChain(chainId: string): BackupChain | null {
    return this.backupChains.get(chainId) || null;
  }
}

export const incrementalBackupManager = IncrementalBackupManager.getInstance();

/**
 * 备份恢复管理器
 */
export class BackupRestoreManager {
  /**
   * 恢复到指定时间点
   */
  public async restoreToPointInTime(
    chainId: string,
    targetTime: Date,
    connectionConfig: any,
    restoreOptions: {
      targetDatabase?: string;
      skipTables?: string[];
      dryRun?: boolean;
    } = {}
  ): Promise<{ success: boolean; error?: string; restoredToTime?: Date }> {
    try {
      const chain = incrementalBackupManager.getBackupChain(chainId);
      if (!chain) {
        throw new Error('备份链不存在');
      }

      logger.info('开始时间点恢复', { chainId, targetTime, restoreOptions });

      // 1. 恢复完整备份
      const baseRestoreResult = await this.restoreFullBackup(
        chain.baseBackupId,
        connectionConfig,
        restoreOptions
      );

      if (!baseRestoreResult.success) {
        throw new Error(`完整备份恢复失败: ${baseRestoreResult.error}`);
      }

      // 2. 按顺序应用增量备份直到目标时间
      for (const incrementalBackup of chain.incrementalBackups) {
        if (incrementalBackup.createdAt > targetTime) {
          break; // 超过目标时间，停止应用
        }

        const incrementalRestoreResult = await this.applyIncrementalBackup(
          incrementalBackup,
          connectionConfig,
          restoreOptions
        );

        if (!incrementalRestoreResult.success) {
          throw new Error(`增量备份应用失败: ${incrementalRestoreResult.error}`);
        }
      }

      logger.info('时间点恢复完成', { chainId, targetTime });
      return { success: true, restoredToTime: targetTime };

    } catch (error) {
      logger.error('时间点恢复失败', error as Error, { chainId, targetTime });
      return { success: false, error: (error as Error).message };
    }
  }

  private async restoreFullBackup(
    backupId: string,
    connectionConfig: any,
    options: any
  ): Promise<{ success: boolean; error?: string }> {
    try {
      logger.info('开始恢复完整备份', { backupId, targetDatabase: options.targetDatabase });

      // 构建mysql命令来导入SQL文件
      const backupFilePath = path.join(config.backupPath, backupId, 'full_backup.sql');

      if (!fs.existsSync(backupFilePath)) {
        throw new Error(`备份文件不存在: ${backupFilePath}`);
      }

      const targetDatabase = options.targetDatabase || connectionConfig.database;

      // 如果是恢复到不同数据库，先创建数据库
      if (targetDatabase !== connectionConfig.database) {
        await this.createDatabaseIfNotExists(connectionConfig, targetDatabase);
      }

      const args = [
        `--host=${connectionConfig.host}`,
        `--port=${connectionConfig.port}`,
        `--user=${connectionConfig.user}`,
        `--password=${connectionConfig.password}`,
        '--default-character-set=utf8mb4',
        targetDatabase
      ];

      // 如果是干运行模式，只验证文件
      if (options.dryRun) {
        logger.info('干运行模式：验证备份文件', { backupFilePath });
        return { success: true };
      }

      return new Promise((resolve) => {
        const mysql = spawn('mysql', args);
        const readStream = fs.createReadStream(backupFilePath);

        readStream.pipe(mysql.stdin);

        let errorOutput = '';
        mysql.stderr.on('data', (data) => {
          errorOutput += data.toString();
        });

        mysql.on('close', (code) => {
          if (code === 0) {
            logger.info('完整备份恢复成功', { backupId, targetDatabase });
            resolve({ success: true });
          } else {
            logger.error('完整备份恢复失败', new Error(errorOutput), { backupId, code });
            resolve({ success: false, error: errorOutput || `Process exited with code ${code}` });
          }
        });

        mysql.on('error', (error) => {
          logger.error('完整备份恢复进程错误', error, { backupId });
          resolve({ success: false, error: error.message });
        });
      });

    } catch (error) {
      logger.error('完整备份恢复异常', error as Error, { backupId });
      return { success: false, error: (error as Error).message };
    }
  }

  private async applyIncrementalBackup(
    backup: IncrementalBackupInfo,
    connectionConfig: any,
    options: any
  ): Promise<{ success: boolean; error?: string }> {
    try {
      logger.info('开始应用增量备份', {
        backupId: backup.id,
        startPosition: backup.startPosition,
        endPosition: backup.endPosition
      });

      const incrementalFilePath = backup.filePath;

      if (!fs.existsSync(incrementalFilePath)) {
        throw new Error(`增量备份文件不存在: ${incrementalFilePath}`);
      }

      const targetDatabase = options.targetDatabase || connectionConfig.database;

      // 如果是干运行模式，只验证文件
      if (options.dryRun) {
        logger.info('干运行模式：验证增量备份文件', { incrementalFilePath });
        return { success: true };
      }

      const args = [
        `--host=${connectionConfig.host}`,
        `--port=${connectionConfig.port}`,
        `--user=${connectionConfig.user}`,
        `--password=${connectionConfig.password}`,
        `--database=${targetDatabase}`,
        '--default-character-set=utf8mb4'
      ];

      return new Promise((resolve) => {
        const mysql = spawn('mysql', args);
        const readStream = fs.createReadStream(incrementalFilePath);

        readStream.pipe(mysql.stdin);

        let errorOutput = '';
        mysql.stderr.on('data', (data) => {
          errorOutput += data.toString();
        });

        mysql.on('close', (code) => {
          if (code === 0) {
            logger.info('增量备份应用成功', {
              backupId: backup.id,
              targetDatabase,
              startPosition: backup.startPosition,
              endPosition: backup.endPosition
            });
            resolve({ success: true });
          } else {
            logger.error('增量备份应用失败', new Error(errorOutput), {
              backupId: backup.id,
              code
            });
            resolve({ success: false, error: errorOutput || `Process exited with code ${code}` });
          }
        });

        mysql.on('error', (error) => {
          logger.error('增量备份应用进程错误', error, { backupId: backup.id });
          resolve({ success: false, error: error.message });
        });
      });

    } catch (error) {
      logger.error('增量备份应用异常', error as Error, { backupId: backup.id });
      return { success: false, error: (error as Error).message };
    }
  }

  /**
   * 创建数据库（如果不存在）
   */
  private async createDatabaseIfNotExists(connectionConfig: any, databaseName: string): Promise<void> {
    const mysql = require('mysql2/promise');
    const connection = await mysql.createConnection({
      ...connectionConfig,
      database: undefined // 连接到MySQL服务器而不是特定数据库
    });

    try {
      await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${databaseName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
      logger.info('数据库创建成功', { databaseName });
    } finally {
      await connection.end();
    }
  }
}

export const backupRestoreManager = new BackupRestoreManager();
