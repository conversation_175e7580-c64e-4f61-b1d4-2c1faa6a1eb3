"use strict";const t=require("electron");t.contextBridge.exposeInMainWorld("electronAPI",{userRegister:e=>t.ipcRenderer.invoke("user-register",e),userLogin:e=>t.ipcRenderer.invoke("user-login",e),addServer:e=>t.ipcRenderer.invoke("add-server",e),getServers:e=>t.ipcRenderer.invoke("get-servers",e),deleteServer:(e,r)=>t.ipcRenderer.invoke("delete-server",{serverId:e,userId:r}),getServer:(e,r)=>t.ipcRenderer.invoke("get-server",{serverId:e,userId:r}),updateServer:(e,r,n)=>t.ipcRenderer.invoke("update-server",{serverId:e,userId:r,serverData:n}),testServerConnection:e=>t.ipcRenderer.invoke("test-server-connection",e),updateServerStatus:(e,r)=>t.ipcRenderer.invoke("update-server-status",e,r),createBackupTask:e=>t.ipcRenderer.invoke("create-backup-task",e),getBackupTasks:e=>t.ipcRenderer.invoke("get-backup-tasks",e),deleteBackupTask:(e,r)=>t.ipcRenderer.invoke("delete-backup-task",{taskId:e,userId:r}),updateBackupTaskStatus:(e,r,n)=>t.ipcRenderer.invoke("update-backup-task-status",e,r,n),getBackupHistory:(e,r)=>t.ipcRenderer.invoke("get-backup-history",e,r),getBackupTaskHistory:(e,r)=>t.ipcRenderer.invoke("get-backup-task-history",e,r),generateCompleteBackupSQL:(e,r,n)=>t.ipcRenderer.invoke("generate-complete-backup-sql",e,r,n),executeBackup:e=>t.ipcRenderer.invoke("execute-backup",e),downloadBackupFile:(e,r,n)=>t.ipcRenderer.invoke("download-backup-file",e,r,n),getBackupFilePath:(e,r)=>t.ipcRenderer.invoke("get-backup-file-path",e,r),showItemInFolder:e=>t.ipcRenderer.invoke("show-item-in-folder",e),testDatabaseExists:(e,r)=>t.ipcRenderer.invoke("test-database-exists",{serverConfig:e,databaseName:r}),testDatabaseConnection:(e,r,n)=>t.ipcRenderer.invoke("test-database-connection",e,r,n),getSystemInfo:e=>t.ipcRenderer.invoke("get-system-info",e),getSystemMetrics:()=>t.ipcRenderer.invoke("get-system-metrics"),getBackupStats:e=>t.ipcRenderer.invoke("get-backup-stats",e),getServerBackupFiles:(e,r)=>t.ipcRenderer.invoke("get-server-backup-files",e,r),downloadServerBackupFile:(e,r,n)=>t.ipcRenderer.invoke("download-server-backup-file",e,r,n),on(...e){const[r,n]=e;return t.ipcRenderer.on(r,(i,...c)=>n(i,...c))},off(...e){const[r,...n]=e;return t.ipcRenderer.off(r,...n)},send(...e){const[r,...n]=e;return t.ipcRenderer.send(r,...n)},invoke(...e){const[r,...n]=e;return t.ipcRenderer.invoke(r,...n)}});
