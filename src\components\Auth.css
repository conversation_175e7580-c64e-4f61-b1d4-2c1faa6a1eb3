.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-tertiary) 100%);
  padding: var(--spacing-2xl);
  position: relative;
  overflow: hidden;
}

.auth-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(0,120,212,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.5;
  pointer-events: none;
}

.auth-card {
  background: var(--bg-secondary);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  border: 1px solid var(--border-color);
  padding: var(--spacing-5xl);
  width: 100%;
  max-width: 420px;
  animation: slideInScale var(--transition-slow);
  position: relative;
  z-index: 1;
  backdrop-filter: blur(20px);
}

.auth-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover), var(--info-color));
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.auth-title {
  text-align: center;
  color: var(--text-primary);
  margin-bottom: var(--spacing-3xl);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
}

.auth-title::before {
  content: '🔐';
  font-size: var(--font-size-xl);
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  position: relative;
}

.form-group label {
  color: var(--text-secondary);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-group input {
  padding: var(--spacing-md) var(--spacing-lg);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  transition: all var(--transition-normal);
  outline: none;
  background: var(--bg-secondary);
  color: var(--text-primary);
  min-height: 48px;
  position: relative;
}

.form-group input:focus {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.1);
  transform: translateY(-1px);
}

.form-group input:hover:not(:focus):not(:disabled) {
  border-color: var(--border-hover);
}

.form-group input:disabled {
  background: var(--bg-disabled);
  color: var(--text-disabled);
  cursor: not-allowed;
  opacity: 0.7;
}

.form-group input::placeholder {
  color: var(--text-tertiary);
  font-style: italic;
}

.error-message {
  background: var(--danger-light);
  color: var(--danger-color);
  padding: var(--spacing-lg) var(--spacing-xl);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-error);
  font-size: var(--font-size-sm);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  animation: shake 0.5s ease-in-out;
}

.error-message::before {
  content: '⚠️';
  font-size: var(--font-size-base);
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.success-message {
  background: var(--success-light);
  color: var(--success-color);
  padding: var(--spacing-lg) var(--spacing-xl);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-success);
  font-size: var(--font-size-sm);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  animation: slideInDown var(--transition-normal);
}

.success-message::before {
  content: '✅';
  font-size: var(--font-size-base);
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.auth-button {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: var(--text-inverse);
  border: none;
  padding: var(--spacing-lg) var(--spacing-2xl);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--transition-normal);
  margin-top: var(--spacing-md);
  min-height: 52px;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-md);
}

.auth-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.auth-button:hover:not(:disabled)::before {
  left: 100%;
}

.auth-button:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-hover), var(--primary-active));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.auth-button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: var(--shadow-md);
}

.auth-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  background: var(--secondary-color);
}

.auth-switch {
  text-align: center;
  margin-top: 20px;
  color: var(--text-secondary);
  font-size: 13px;
}

.switch-button {
  background: none;
  border: none;
  color: var(--primary-color);
  cursor: pointer;
  font-weight: 500;
  margin-left: 4px;
  text-decoration: none;
  font-size: 13px;
  transition: color 0.2s ease;
  padding: 0;
}

.switch-button:hover:not(:disabled) {
  color: var(--primary-hover);
  text-decoration: underline;
}

.switch-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .auth-container {
    padding: 10px;
  }
  
  .auth-card {
    padding: 30px 20px;
  }
  
  .auth-title {
    font-size: 24px;
  }
}
